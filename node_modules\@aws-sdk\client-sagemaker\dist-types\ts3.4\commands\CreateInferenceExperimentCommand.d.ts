import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateInferenceExperimentRequest,
  CreateInferenceExperimentResponse,
} from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateInferenceExperimentCommandInput
  extends CreateInferenceExperimentRequest {}
export interface CreateInferenceExperimentCommandOutput
  extends CreateInferenceExperimentResponse,
    __MetadataBearer {}
declare const CreateInferenceExperimentCommand_base: {
  new (
    input: CreateInferenceExperimentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateInferenceExperimentCommandInput,
    CreateInferenceExperimentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateInferenceExperimentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateInferenceExperimentCommandInput,
    CreateInferenceExperimentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateInferenceExperimentCommand extends CreateInferenceExperimentCommand_base {
  protected static __types: {
    api: {
      input: CreateInferenceExperimentRequest;
      output: CreateInferenceExperimentResponse;
    };
    sdk: {
      input: CreateInferenceExperimentCommandInput;
      output: CreateInferenceExperimentCommandOutput;
    };
  };
}
