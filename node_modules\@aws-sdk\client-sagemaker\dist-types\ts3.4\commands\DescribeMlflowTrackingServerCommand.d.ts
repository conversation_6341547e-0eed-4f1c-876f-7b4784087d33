import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeMlflowTrackingServerRequest,
  DescribeMlflowTrackingServerResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeMlflowTrackingServerCommandInput
  extends DescribeMlflowTrackingServerRequest {}
export interface DescribeMlflowTrackingServerCommandOutput
  extends DescribeMlflowTrackingServerResponse,
    __MetadataBearer {}
declare const DescribeMlflowTrackingServerCommand_base: {
  new (
    input: DescribeMlflowTrackingServerCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeMlflowTrackingServerCommandInput,
    DescribeMlflowTrackingServerCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeMlflowTrackingServerCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeMlflowTrackingServerCommandInput,
    DescribeMlflowTrackingServerCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeMlflowTrackingServerCommand extends DescribeMlflowTrackingServerCommand_base {
  protected static __types: {
    api: {
      input: DescribeMlflowTrackingServerRequest;
      output: DescribeMlflowTrackingServerResponse;
    };
    sdk: {
      input: DescribeMlflowTrackingServerCommandInput;
      output: DescribeMlflowTrackingServerCommandOutput;
    };
  };
}
