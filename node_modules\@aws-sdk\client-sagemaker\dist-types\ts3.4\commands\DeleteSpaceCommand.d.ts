import { Command as $Command } from "@smithy/smithy-client";
import { <PERSON>ada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DeleteSpaceRequest } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteSpaceCommandInput extends DeleteSpaceRequest {}
export interface DeleteSpaceCommandOutput extends __MetadataBearer {}
declare const DeleteSpaceCommand_base: {
  new (
    input: DeleteSpaceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteSpaceCommandInput,
    DeleteSpaceCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteSpaceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteSpaceCommandInput,
    DeleteSpaceCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteSpaceCommand extends DeleteSpaceCommand_base {
  protected static __types: {
    api: {
      input: DeleteSpaceRequest;
      output: {};
    };
    sdk: {
      input: DeleteSpaceCommandInput;
      output: DeleteSpaceCommandOutput;
    };
  };
}
