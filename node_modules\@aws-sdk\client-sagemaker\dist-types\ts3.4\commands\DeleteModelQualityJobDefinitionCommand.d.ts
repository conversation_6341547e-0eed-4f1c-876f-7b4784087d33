import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DeleteModelQualityJobDefinitionRequest } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteModelQualityJobDefinitionCommandInput
  extends DeleteModelQualityJobDefinitionRequest {}
export interface DeleteModelQualityJobDefinitionCommandOutput
  extends __MetadataBearer {}
declare const DeleteModelQualityJobDefinitionCommand_base: {
  new (
    input: DeleteModelQualityJobDefinitionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteModelQualityJobDefinitionCommandInput,
    DeleteModelQualityJobDefinitionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteModelQualityJobDefinitionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteModelQualityJobDefinitionCommandInput,
    DeleteModelQualityJobDefinitionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteModelQualityJobDefinitionCommand extends DeleteModelQualityJobDefinitionCommand_base {
  protected static __types: {
    api: {
      input: DeleteModelQualityJobDefinitionRequest;
      output: {};
    };
    sdk: {
      input: DeleteModelQualityJobDefinitionCommandInput;
      output: DeleteModelQualityJobDefinitionCommandOutput;
    };
  };
}
