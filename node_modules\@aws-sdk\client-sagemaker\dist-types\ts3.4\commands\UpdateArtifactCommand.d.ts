import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  UpdateArtifactRequest,
  UpdateArtifactResponse,
} from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateArtifactCommandInput extends UpdateArtifactRequest {}
export interface UpdateArtifactCommandOutput
  extends UpdateArtifactResponse,
    __MetadataBearer {}
declare const UpdateArtifactCommand_base: {
  new (
    input: UpdateArtifactCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateArtifactCommandInput,
    UpdateArtifactCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateArtifactCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateArtifactCommandInput,
    UpdateArtifactCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateArtifactCommand extends UpdateArtifactCommand_base {
  protected static __types: {
    api: {
      input: UpdateArtifactRequest;
      output: UpdateArtifactResponse;
    };
    sdk: {
      input: UpdateArtifactCommandInput;
      output: UpdateArtifactCommandOutput;
    };
  };
}
