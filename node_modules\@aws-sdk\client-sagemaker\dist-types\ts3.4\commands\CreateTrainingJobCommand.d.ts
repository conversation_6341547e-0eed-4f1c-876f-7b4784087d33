import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateTrainingJobRequest,
  CreateTrainingJobResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateTrainingJobCommandInput
  extends CreateTrainingJobRequest {}
export interface CreateTrainingJobCommandOutput
  extends CreateTrainingJobResponse,
    __MetadataBearer {}
declare const CreateTrainingJobCommand_base: {
  new (
    input: CreateTrainingJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateTrainingJobCommandInput,
    CreateTrainingJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateTrainingJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateTrainingJobCommandInput,
    CreateTrainingJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateTrainingJobCommand extends CreateTrainingJobCommand_base {
  protected static __types: {
    api: {
      input: CreateTrainingJobRequest;
      output: CreateTrainingJobResponse;
    };
    sdk: {
      input: CreateTrainingJobCommandInput;
      output: CreateTrainingJobCommandOutput;
    };
  };
}
