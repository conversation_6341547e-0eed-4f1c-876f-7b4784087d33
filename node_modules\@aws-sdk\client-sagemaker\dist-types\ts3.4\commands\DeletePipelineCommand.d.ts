import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DeletePipelineRequest,
  DeletePipelineResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeletePipelineCommandInput extends DeletePipelineRequest {}
export interface DeletePipelineCommandOutput
  extends DeletePipelineResponse,
    __MetadataBearer {}
declare const DeletePipelineCommand_base: {
  new (
    input: DeletePipelineCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeletePipelineCommandInput,
    DeletePipelineCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeletePipelineCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeletePipelineCommandInput,
    DeletePipelineCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeletePipelineCommand extends DeletePipelineCommand_base {
  protected static __types: {
    api: {
      input: DeletePipelineRequest;
      output: DeletePipelineResponse;
    };
    sdk: {
      input: DeletePipelineCommandInput;
      output: DeletePipelineCommandOutput;
    };
  };
}
