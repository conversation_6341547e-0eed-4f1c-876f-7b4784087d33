import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeModelQualityJobDefinitionRequest,
  DescribeModelQualityJobDefinitionResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeModelQualityJobDefinitionCommandInput
  extends DescribeModelQualityJobDefinitionRequest {}
export interface DescribeModelQualityJobDefinitionCommandOutput
  extends DescribeModelQualityJobDefinitionResponse,
    __MetadataBearer {}
declare const DescribeModelQualityJobDefinitionCommand_base: {
  new (
    input: DescribeModelQualityJobDefinitionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeModelQualityJobDefinitionCommandInput,
    DescribeModelQualityJobDefinitionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeModelQualityJobDefinitionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeModelQualityJobDefinitionCommandInput,
    DescribeModelQualityJobDefinitionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeModelQualityJobDefinitionCommand extends DescribeModelQualityJobDefinitionCommand_base {
  protected static __types: {
    api: {
      input: DescribeModelQualityJobDefinitionRequest;
      output: DescribeModelQualityJobDefinitionResponse;
    };
    sdk: {
      input: DescribeModelQualityJobDefinitionCommandInput;
      output: DescribeModelQualityJobDefinitionCommandOutput;
    };
  };
}
