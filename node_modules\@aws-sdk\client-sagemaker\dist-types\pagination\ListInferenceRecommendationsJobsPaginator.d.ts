import { Paginator } from "@smithy/types";
import { ListInferenceRecommendationsJobsCommandInput, ListInferenceRecommendationsJobsCommandOutput } from "../commands/ListInferenceRecommendationsJobsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListInferenceRecommendationsJobs: (config: SageMakerPaginationConfiguration, input: ListInferenceRecommendationsJobsCommandInput, ...rest: any[]) => Paginator<ListInferenceRecommendationsJobsCommandOutput>;
