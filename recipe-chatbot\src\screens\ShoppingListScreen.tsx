import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
  TextInput,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useFocusEffect } from '@react-navigation/native';
import { storageService, ShoppingItem } from '../services/StorageService';

export default function ShoppingListScreen({ navigation }: any) {
  const [shoppingList, setShoppingList] = useState<ShoppingItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [newItemText, setNewItemText] = useState('');

  const loadShoppingList = async () => {
    try {
      const items = await storageService.getShoppingList();
      setShoppingList(items);
    } catch (error) {
      console.error('Alışveriş listesi yüklenirken hata:', error);
      Alert.alert('Hata', 'Alışveriş listesi yüklenirken bir hata oluştu.');
    } finally {
      setIsLoading(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      loadShoppingList();
    }, [])
  );

  const onRefresh = async () => {
    setRefreshing(true);
    await loadShoppingList();
    setRefreshing(false);
  };

  const handleToggleItem = async (itemId: string) => {
    try {
      await storageService.toggleShoppingItem(itemId);
      await loadShoppingList();
    } catch (error) {
      console.error('Item güncellenirken hata:', error);
      Alert.alert('Hata', 'Item güncellenirken bir hata oluştu.');
    }
  };

  const handleRemoveItem = async (itemId: string, itemName: string) => {
    Alert.alert(
      'Öğeyi Sil',
      `"${itemName}" öğesini listeden silmek istediğinizden emin misiniz?`,
      [
        {
          text: 'İptal',
          style: 'cancel',
        },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await storageService.removeFromShoppingList(itemId);
              await loadShoppingList();
            } catch (error) {
              console.error('Item silinirken hata:', error);
              Alert.alert('Hata', 'Item silinirken bir hata oluştu.');
            }
          },
        },
      ]
    );
  };

  const handleAddItem = async () => {
    if (!newItemText.trim()) {
      Alert.alert('Uyarı', 'Lütfen bir malzeme adı girin.');
      return;
    }

    try {
      await storageService.addToShoppingList(newItemText.trim());
      await loadShoppingList();
      setNewItemText('');
      setShowAddModal(false);
      Alert.alert('Başarılı', 'Malzeme listeye eklendi.');
    } catch (error) {
      console.error('Item eklenirken hata:', error);
      Alert.alert('Hata', 'Malzeme eklenirken bir hata oluştu.');
    }
  };

  const handleClearCompleted = async () => {
    const completedCount = shoppingList.filter(item => item.completed).length;
    
    if (completedCount === 0) {
      Alert.alert('Bilgi', 'Tamamlanmış öğe bulunmuyor.');
      return;
    }

    Alert.alert(
      'Tamamlananları Temizle',
      `${completedCount} tamamlanmış öğeyi silmek istediğinizden emin misiniz?`,
      [
        {
          text: 'İptal',
          style: 'cancel',
        },
        {
          text: 'Temizle',
          style: 'destructive',
          onPress: async () => {
            try {
              await storageService.clearCompletedItems();
              await loadShoppingList();
              Alert.alert('Başarılı', 'Tamamlanan öğeler temizlendi.');
            } catch (error) {
              console.error('Tamamlananlar silinirken hata:', error);
              Alert.alert('Hata', 'Öğeler silinirken bir hata oluştu.');
            }
          },
        },
      ]
    );
  };

  const renderShoppingItem = (item: ShoppingItem) => (
    <View key={item.id} style={styles.itemCard}>
      <TouchableOpacity
        style={styles.itemContent}
        onPress={() => handleToggleItem(item.id)}
      >
        <Ionicons
          name={item.completed ? 'checkmark-circle' : 'ellipse-outline'}
          size={24}
          color={item.completed ? '#4CAF50' : '#ccc'}
        />
        <Text
          style={[
            styles.itemText,
            item.completed && styles.completedItemText
          ]}
        >
          {item.name}
        </Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={styles.removeButton}
        onPress={() => handleRemoveItem(item.id, item.name)}
      >
        <Ionicons name="trash-outline" size={20} color="#FF6B35" />
      </TouchableOpacity>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="basket-outline" size={80} color="#ccc" />
      <Text style={styles.emptyTitle}>Alışveriş listeniz boş</Text>
      <Text style={styles.emptyDescription}>
        Chatbot'tan aldığınız tarif önerilerindeki malzemeleri buraya ekleyerek alışveriş listenizi oluşturabilirsiniz.
      </Text>
      <TouchableOpacity style={styles.addButton} onPress={() => setShowAddModal(true)}>
        <Ionicons name="add" size={20} color="white" />
        <Text style={styles.addButtonText}>Malzeme Ekle</Text>
      </TouchableOpacity>
    </View>
  );

  const completedCount = shoppingList.filter(item => item.completed).length;
  const totalCount = shoppingList.length;

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Alışveriş listesi yükleniyor... ⌨️</Text>
      </View>
    );
  }

  return (
    <>
      <ScrollView
        style={styles.container}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {shoppingList.length === 0 ? (
          renderEmptyState()
        ) : (
          <>
            {/* Header */}
            <View style={styles.header}>
              <View style={styles.headerInfo}>
                <Text style={styles.headerTitle}>Alışveriş Listesi</Text>
                <Text style={styles.headerSubtitle}>
                  {completedCount}/{totalCount} tamamlandı
                </Text>
              </View>
              <View style={styles.headerButtons}>
                <TouchableOpacity
                  style={styles.clearButton}
                  onPress={handleClearCompleted}
                  disabled={completedCount === 0}
                >
                  <Ionicons name="checkmark-done" size={20} color={completedCount > 0 ? "#4CAF50" : "#ccc"} />
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.addButton}
                  onPress={() => setShowAddModal(true)}
                >
                  <Ionicons name="add" size={20} color="white" />
                </TouchableOpacity>
              </View>
            </View>

            {/* Progress Bar */}
            <View style={styles.progressContainer}>
              <View style={styles.progressBar}>
                <View
                  style={[
                    styles.progressFill,
                    { width: `${totalCount > 0 ? (completedCount / totalCount) * 100 : 0}%` }
                  ]}
                />
              </View>
            </View>

            {/* Shopping List */}
            <View style={styles.listContainer}>
              {shoppingList.map(renderShoppingItem)}
            </View>

            {/* Tips */}
            <View style={styles.tipsContainer}>
              <View style={styles.tipCard}>
                <Ionicons name="bulb" size={20} color="#FF6B35" />
                <Text style={styles.tipText}>
                  💡 İpucu: Chatbot'a "Bu tarifte hangi malzemeler var?" diyerek tarif malzemelerini alışveriş listenize ekleyebilirsiniz!
                </Text>
              </View>
            </View>
          </>
        )}
      </ScrollView>

      {/* Add Item Modal */}
      <Modal
        visible={showAddModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowAddModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Yeni Malzeme Ekle</Text>
            
            <TextInput
              style={styles.modalInput}
              value={newItemText}
              onChangeText={setNewItemText}
              placeholder="Malzeme adını girin..."
              placeholderTextColor="#999"
              autoFocus={true}
              onSubmitEditing={handleAddItem}
            />
            
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={styles.modalCancelButton}
                onPress={() => {
                  setShowAddModal(false);
                  setNewItemText('');
                }}
              >
                <Text style={styles.modalCancelText}>İptal</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={styles.modalAddButton}
                onPress={handleAddItem}
              >
                <Text style={styles.modalAddText}>Ekle</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    fontStyle: 'italic',
  },
  header: {
    backgroundColor: 'white',
    padding: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerInfo: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  headerButtons: {
    flexDirection: 'row',
    gap: 10,
  },
  clearButton: {
    backgroundColor: '#f0f0f0',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addButton: {
    backgroundColor: '#FF6B35',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  addButtonText: {
    color: 'white',
    fontWeight: 'bold',
    marginLeft: 5,
  },
  progressContainer: {
    backgroundColor: 'white',
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  progressBar: {
    height: 8,
    backgroundColor: '#e0e0e0',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#4CAF50',
    borderRadius: 4,
  },
  listContainer: {
    paddingHorizontal: 20,
    paddingTop: 10,
  },
  itemCard: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 15,
    marginBottom: 10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  itemContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemText: {
    fontSize: 16,
    color: '#333',
    marginLeft: 15,
    flex: 1,
  },
  completedItemText: {
    textDecorationLine: 'line-through',
    color: '#999',
  },
  removeButton: {
    padding: 5,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    backgroundColor: 'white',
    margin: 20,
    borderRadius: 15,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 20,
    marginBottom: 10,
  },
  emptyDescription: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 30,
  },
  tipsContainer: {
    padding: 20,
  },
  tipCard: {
    backgroundColor: '#FFF3E0',
    padding: 15,
    borderRadius: 10,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  tipText: {
    fontSize: 14,
    color: '#E65100',
    marginLeft: 10,
    flex: 1,
    lineHeight: 20,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    width: '80%',
    maxWidth: 300,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
    textAlign: 'center',
  },
  modalInput: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 10,
    padding: 15,
    fontSize: 16,
    marginBottom: 20,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalCancelButton: {
    flex: 1,
    backgroundColor: '#f0f0f0',
    padding: 15,
    borderRadius: 10,
    marginRight: 10,
  },
  modalCancelText: {
    textAlign: 'center',
    color: '#666',
    fontWeight: 'bold',
  },
  modalAddButton: {
    flex: 1,
    backgroundColor: '#FF6B35',
    padding: 15,
    borderRadius: 10,
    marginLeft: 10,
  },
  modalAddText: {
    textAlign: 'center',
    color: 'white',
    fontWeight: 'bold',
  },
});
