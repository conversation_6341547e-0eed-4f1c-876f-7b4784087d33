import { Paginator } from "@smithy/types";
import {
  ListLabelingJobsCommandInput,
  ListLabelingJobsCommandOutput,
} from "../commands/ListLabelingJobsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
export declare const paginateListLabelingJobs: (
  config: SageMakerPaginationConfiguration,
  input: ListLabelingJobsCommandInput,
  ...rest: any[]
) => Paginator<ListLabelingJobsCommandOutput>;
