import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ListHyperParameterTuningJobsRequest,
  ListHyperParameterTuningJobsResponse,
} from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface ListHyperParameterTuningJobsCommandInput
  extends ListHyperParameterTuningJobsRequest {}
export interface ListHyperParameterTuningJobsCommandOutput
  extends ListHyperParameterTuningJobsResponse,
    __MetadataBearer {}
declare const ListHyperParameterTuningJobsCommand_base: {
  new (
    input: ListHyperParameterTuningJobsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListHyperParameterTuningJobsCommandInput,
    ListHyperParameterTuningJobsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    ...[input]: [] | [ListHyperParameterTuningJobsCommandInput]
  ): import("@smithy/smithy-client").CommandImpl<
    ListHyperParameterTuningJobsCommandInput,
    ListHyperParameterTuningJobsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListHyperParameterTuningJobsCommand extends ListHyperParameterTuningJobsCommand_base {
  protected static __types: {
    api: {
      input: ListHyperParameterTuningJobsRequest;
      output: ListHyperParameterTuningJobsResponse;
    };
    sdk: {
      input: ListHyperParameterTuningJobsCommandInput;
      output: ListHyperParameterTuningJobsCommandOutput;
    };
  };
}
