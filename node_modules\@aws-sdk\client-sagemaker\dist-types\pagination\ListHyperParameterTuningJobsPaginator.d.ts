import { Paginator } from "@smithy/types";
import { ListHyperParameterTuningJobsCommandInput, ListHyperParameterTuningJobsCommandOutput } from "../commands/ListHyperParameterTuningJobsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListHyperParameterTuningJobs: (config: SageMakerPaginationConfiguration, input: ListHyperParameterTuningJobsCommandInput, ...rest: any[]) => Paginator<ListHyperParameterTuningJobsCommandOutput>;
