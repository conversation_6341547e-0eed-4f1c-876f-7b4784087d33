import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeTransformJobRequest,
  DescribeTransformJobResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeTransformJobCommandInput
  extends DescribeTransformJobRequest {}
export interface DescribeTransformJobCommandOutput
  extends DescribeTransformJobResponse,
    __MetadataBearer {}
declare const DescribeTransformJobCommand_base: {
  new (
    input: DescribeTransformJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeTransformJobCommandInput,
    DescribeTransformJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeTransformJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeTransformJobCommandInput,
    DescribeTransformJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeTransformJobCommand extends DescribeTransformJobCommand_base {
  protected static __types: {
    api: {
      input: DescribeTransformJobRequest;
      output: DescribeTransformJobResponse;
    };
    sdk: {
      input: DescribeTransformJobCommandInput;
      output: DescribeTransformJobCommandOutput;
    };
  };
}
