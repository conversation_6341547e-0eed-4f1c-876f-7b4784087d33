import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DeleteAppRequest } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteAppCommandInput extends DeleteAppRequest {}
export interface DeleteAppCommandOutput extends __MetadataBearer {}
declare const DeleteAppCommand_base: {
  new (
    input: DeleteAppCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteAppCommandInput,
    DeleteAppCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteAppCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteAppCommandInput,
    DeleteAppCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteAppCommand extends DeleteAppCommand_base {
  protected static __types: {
    api: {
      input: DeleteAppRequest;
      output: {};
    };
    sdk: {
      input: DeleteAppCommandInput;
      output: DeleteAppCommandOutput;
    };
  };
}
