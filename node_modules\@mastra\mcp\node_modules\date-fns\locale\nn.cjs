"use strict";
exports.nn = void 0;
var _index = require("./nn/_lib/formatDistance.cjs");
var _index2 = require("./nn/_lib/formatLong.cjs");
var _index3 = require("./nn/_lib/formatRelative.cjs");
var _index4 = require("./nn/_lib/localize.cjs");
var _index5 = require("./nn/_lib/match.cjs");

/**
 * @category Locales
 * @summary Norwegian Nynorsk locale.
 * @language Norwegian Nynorsk
 * @iso-639-2 nno
 * <AUTHOR> By<PERSON> [@draperunner](https://github.com/draperunner)
 */
const nn = (exports.nn = {
  code: "nn",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
});
