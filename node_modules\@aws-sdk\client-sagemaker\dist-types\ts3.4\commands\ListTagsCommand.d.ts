import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { ListTagsInput, ListTagsOutput } from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface ListTagsCommandInput extends ListTagsInput {}
export interface ListTagsCommandOutput
  extends ListTagsOutput,
    __MetadataBearer {}
declare const ListTagsCommand_base: {
  new (
    input: ListTagsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListTagsCommandInput,
    ListTagsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: ListTagsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListTagsCommandInput,
    ListTagsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListTagsCommand extends ListTagsCommand_base {
  protected static __types: {
    api: {
      input: ListTagsInput;
      output: ListTagsOutput;
    };
    sdk: {
      input: ListTagsCommandInput;
      output: ListTagsCommandOutput;
    };
  };
}
