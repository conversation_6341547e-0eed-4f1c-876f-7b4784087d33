import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  StopPipelineExecutionRequest,
  StopPipelineExecutionResponse,
} from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface StopPipelineExecutionCommandInput
  extends StopPipelineExecutionRequest {}
export interface StopPipelineExecutionCommandOutput
  extends StopPipelineExecutionResponse,
    __MetadataBearer {}
declare const StopPipelineExecutionCommand_base: {
  new (
    input: StopPipelineExecutionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StopPipelineExecutionCommandInput,
    StopPipelineExecutionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: StopPipelineExecutionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StopPipelineExecutionCommandInput,
    StopPipelineExecutionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class StopPipelineExecutionCommand extends StopPipelineExecutionCommand_base {
  protected static __types: {
    api: {
      input: StopPipelineExecutionRequest;
      output: StopPipelineExecutionResponse;
    };
    sdk: {
      input: StopPipelineExecutionCommandInput;
      output: StopPipelineExecutionCommandOutput;
    };
  };
}
