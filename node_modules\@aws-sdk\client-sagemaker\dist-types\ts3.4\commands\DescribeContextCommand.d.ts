import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeContextRequest,
  DescribeContextResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeContextCommandInput extends DescribeContextRequest {}
export interface DescribeContextCommandOutput
  extends DescribeContextResponse,
    __MetadataBearer {}
declare const DescribeContextCommand_base: {
  new (
    input: DescribeContextCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeContextCommandInput,
    DescribeContextCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeContextCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeContextCommandInput,
    DescribeContextCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeContextCommand extends DescribeContextCommand_base {
  protected static __types: {
    api: {
      input: DescribeContextRequest;
      output: DescribeContextResponse;
    };
    sdk: {
      input: DescribeContextCommandInput;
      output: DescribeContextCommandOutput;
    };
  };
}
