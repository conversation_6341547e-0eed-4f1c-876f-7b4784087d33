import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { DeleteStudioLifecycleConfigRequest } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteStudioLifecycleConfigCommandInput
  extends DeleteStudioLifecycleConfigRequest {}
export interface DeleteStudioLifecycleConfigCommandOutput
  extends __MetadataBearer {}
declare const DeleteStudioLifecycleConfigCommand_base: {
  new (
    input: DeleteStudioLifecycleConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteStudioLifecycleConfigCommandInput,
    DeleteStudioLifecycleConfigCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteStudioLifecycleConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteStudioLifecycleConfigCommandInput,
    DeleteStudioLifecycleConfigCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteStudioLifecycleConfigCommand extends DeleteStudioLifecycleConfigCommand_base {
  protected static __types: {
    api: {
      input: DeleteStudioLifecycleConfigRequest;
      output: {};
    };
    sdk: {
      input: DeleteStudioLifecycleConfigCommandInput;
      output: DeleteStudioLifecycleConfigCommandOutput;
    };
  };
}
