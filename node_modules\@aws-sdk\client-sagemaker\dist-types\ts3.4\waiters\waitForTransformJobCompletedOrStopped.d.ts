import { WaiterConfiguration, WaiterResult } from "@smithy/util-waiter";
import { DescribeTransformJobCommandInput } from "../commands/DescribeTransformJobCommand";
import { SageMakerClient } from "../SageMakerClient";
export declare const waitForTransformJobCompletedOrStopped: (
  params: WaiterConfiguration<SageMakerClient>,
  input: DescribeTransformJobCommandInput
) => Promise<WaiterResult>;
export declare const waitUntilTransformJobCompletedOrStopped: (
  params: WaiterConfiguration<SageMakerClient>,
  input: DescribeTransformJobCommandInput
) => Promise<WaiterResult>;
