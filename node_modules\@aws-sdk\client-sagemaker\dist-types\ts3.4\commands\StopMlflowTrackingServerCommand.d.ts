import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  StopMlflowTrackingServerRequest,
  StopMlflowTrackingServerResponse,
} from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface StopMlflowTrackingServerCommandInput
  extends StopMlflowTrackingServerRequest {}
export interface StopMlflowTrackingServerCommandOutput
  extends StopMlflowTrackingServerResponse,
    __MetadataBearer {}
declare const StopMlflowTrackingServerCommand_base: {
  new (
    input: StopMlflowTrackingServerCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StopMlflowTrackingServerCommandInput,
    StopMlflowTrackingServerCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: StopMlflowTrackingServerCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StopMlflowTrackingServerCommandInput,
    StopMlflowTrackingServerCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class StopMlflowTrackingServerCommand extends StopMlflowTrackingServerCommand_base {
  protected static __types: {
    api: {
      input: StopMlflowTrackingServerRequest;
      output: StopMlflowTrackingServerResponse;
    };
    sdk: {
      input: StopMlflowTrackingServerCommandInput;
      output: StopMlflowTrackingServerCommandOutput;
    };
  };
}
