import AsyncStorage from '@react-native-async-storage/async-storage';

export interface FavoriteRecipe {
  id: string;
  title: string;
  description: string;
  timestamp: Date;
}

export interface ShoppingItem {
  id: string;
  name: string;
  completed: boolean;
  timestamp: Date;
}

class StorageService {
  private readonly FAVORITES_KEY = '@recipe_favorites';
  private readonly SHOPPING_LIST_KEY = '@shopping_list';

  // Favoriler
  async getFavorites(): Promise<FavoriteRecipe[]> {
    try {
      const favorites = await AsyncStorage.getItem(this.FAVORITES_KEY);
      if (favorites) {
        const parsed = JSON.parse(favorites);
        return parsed.map((item: any) => ({
          ...item,
          timestamp: new Date(item.timestamp)
        }));
      }
      return [];
    } catch (error) {
      console.error('Favoriler yüklenirken hata:', error);
      return [];
    }
  }

  async addToFavorites(recipe: Omit<FavoriteRecipe, 'timestamp'>): Promise<void> {
    try {
      const favorites = await this.getFavorites();
      const newFavorite: FavoriteRecipe = {
        ...recipe,
        timestamp: new Date()
      };
      
      // Aynı tarif zaten favorilerde mi kontrol et
      const exists = favorites.some(fav => fav.id === recipe.id);
      if (!exists) {
        favorites.unshift(newFavorite);
        await AsyncStorage.setItem(this.FAVORITES_KEY, JSON.stringify(favorites));
      }
    } catch (error) {
      console.error('Favorilere eklenirken hata:', error);
      throw error;
    }
  }

  async removeFromFavorites(recipeId: string): Promise<void> {
    try {
      const favorites = await this.getFavorites();
      const filtered = favorites.filter(fav => fav.id !== recipeId);
      await AsyncStorage.setItem(this.FAVORITES_KEY, JSON.stringify(filtered));
    } catch (error) {
      console.error('Favorilerden silinirken hata:', error);
      throw error;
    }
  }

  async isFavorite(recipeId: string): Promise<boolean> {
    try {
      const favorites = await this.getFavorites();
      return favorites.some(fav => fav.id === recipeId);
    } catch (error) {
      console.error('Favori kontrolü hatası:', error);
      return false;
    }
  }

  // Alışveriş Listesi
  async getShoppingList(): Promise<ShoppingItem[]> {
    try {
      const shoppingList = await AsyncStorage.getItem(this.SHOPPING_LIST_KEY);
      if (shoppingList) {
        const parsed = JSON.parse(shoppingList);
        return parsed.map((item: any) => ({
          ...item,
          timestamp: new Date(item.timestamp)
        }));
      }
      return [];
    } catch (error) {
      console.error('Alışveriş listesi yüklenirken hata:', error);
      return [];
    }
  }

  async addToShoppingList(itemName: string): Promise<void> {
    try {
      const shoppingList = await this.getShoppingList();
      const newItem: ShoppingItem = {
        id: Date.now().toString(),
        name: itemName.trim(),
        completed: false,
        timestamp: new Date()
      };
      
      // Aynı item zaten listede mi kontrol et
      const exists = shoppingList.some(item => 
        item.name.toLowerCase() === itemName.toLowerCase().trim()
      );
      
      if (!exists) {
        shoppingList.unshift(newItem);
        await AsyncStorage.setItem(this.SHOPPING_LIST_KEY, JSON.stringify(shoppingList));
      }
    } catch (error) {
      console.error('Alışveriş listesine eklenirken hata:', error);
      throw error;
    }
  }

  async toggleShoppingItem(itemId: string): Promise<void> {
    try {
      const shoppingList = await this.getShoppingList();
      const updatedList = shoppingList.map(item => 
        item.id === itemId ? { ...item, completed: !item.completed } : item
      );
      await AsyncStorage.setItem(this.SHOPPING_LIST_KEY, JSON.stringify(updatedList));
    } catch (error) {
      console.error('Alışveriş item güncellenirken hata:', error);
      throw error;
    }
  }

  async removeFromShoppingList(itemId: string): Promise<void> {
    try {
      const shoppingList = await this.getShoppingList();
      const filtered = shoppingList.filter(item => item.id !== itemId);
      await AsyncStorage.setItem(this.SHOPPING_LIST_KEY, JSON.stringify(filtered));
    } catch (error) {
      console.error('Alışveriş listesinden silinirken hata:', error);
      throw error;
    }
  }

  async clearCompletedItems(): Promise<void> {
    try {
      const shoppingList = await this.getShoppingList();
      const filtered = shoppingList.filter(item => !item.completed);
      await AsyncStorage.setItem(this.SHOPPING_LIST_KEY, JSON.stringify(filtered));
    } catch (error) {
      console.error('Tamamlanan itemlar silinirken hata:', error);
      throw error;
    }
  }
}

export const storageService = new StorageService();
