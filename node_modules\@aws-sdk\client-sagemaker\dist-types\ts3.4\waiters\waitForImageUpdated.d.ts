import { WaiterConfiguration, WaiterResult } from "@smithy/util-waiter";
import { DescribeImageCommandInput } from "../commands/DescribeImageCommand";
import { SageMakerClient } from "../SageMakerClient";
export declare const waitForImageUpdated: (
  params: WaiterConfiguration<SageMakerClient>,
  input: DescribeImageCommandInput
) => Promise<WaiterResult>;
export declare const waitUntilImageUpdated: (
  params: WaiterConfiguration<SageMakerClient>,
  input: DescribeImageCommandInput
) => Promise<WaiterResult>;
