import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  DeleteClusterRequest,
  DeleteClusterResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteClusterCommandInput extends DeleteClusterRequest {}
export interface DeleteClusterCommandOutput
  extends DeleteClusterResponse,
    __MetadataBearer {}
declare const DeleteClusterCommand_base: {
  new (
    input: DeleteClusterCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteClusterCommandInput,
    DeleteClusterCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteClusterCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteClusterCommandInput,
    DeleteClusterCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteClusterCommand extends DeleteClusterCommand_base {
  protected static __types: {
    api: {
      input: DeleteClusterRequest;
      output: DeleteClusterResponse;
    };
    sdk: {
      input: DeleteClusterCommandInput;
      output: DeleteClusterCommandOutput;
    };
  };
}
