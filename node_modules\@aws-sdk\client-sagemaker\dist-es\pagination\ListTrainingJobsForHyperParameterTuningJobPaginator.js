import { createPaginator } from "@smithy/core";
import { ListTrainingJobsForHyperParameterTuningJobCommand, } from "../commands/ListTrainingJobsForHyperParameterTuningJobCommand";
import { SageMakerClient } from "../SageMakerClient";
export const paginateListTrainingJobsForHyperParameterTuningJob = createPaginator(SageMakerClient, ListTrainingJobsForHyperParameterTuningJobCommand, "NextToken", "NextToken", "MaxResults");
