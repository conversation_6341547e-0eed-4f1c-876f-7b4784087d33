import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeEdgeDeploymentPlanRequest,
  DescribeEdgeDeploymentPlanResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeEdgeDeploymentPlanCommandInput
  extends DescribeEdgeDeploymentPlanRequest {}
export interface DescribeEdgeDeploymentPlanCommandOutput
  extends DescribeEdgeDeploymentPlanResponse,
    __MetadataBearer {}
declare const DescribeEdgeDeploymentPlanCommand_base: {
  new (
    input: DescribeEdgeDeploymentPlanCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeEdgeDeploymentPlanCommandInput,
    DescribeEdgeDeploymentPlanCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeEdgeDeploymentPlanCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeEdgeDeploymentPlanCommandInput,
    DescribeEdgeDeploymentPlanCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeEdgeDeploymentPlanCommand extends DescribeEdgeDeploymentPlanCommand_base {
  protected static __types: {
    api: {
      input: DescribeEdgeDeploymentPlanRequest;
      output: DescribeEdgeDeploymentPlanResponse;
    };
    sdk: {
      input: DescribeEdgeDeploymentPlanCommandInput;
      output: DescribeEdgeDeploymentPlanCommandOutput;
    };
  };
}
