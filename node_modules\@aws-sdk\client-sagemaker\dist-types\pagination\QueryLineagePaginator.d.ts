import { Paginator } from "@smithy/types";
import { QueryLineageCommandInput, QueryLineageCommandOutput } from "../commands/QueryLineageCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateQueryLineage: (config: SageMakerPaginationConfiguration, input: QueryLineageCommandInput, ...rest: any[]) => Paginator<QueryLineageCommandOutput>;
