import { Paginator } from "@smithy/types";
import {
  ListCompilationJobsCommandInput,
  ListCompilationJobsCommandOutput,
} from "../commands/ListCompilationJobsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
export declare const paginateListCompilationJobs: (
  config: SageMakerPaginationConfiguration,
  input: ListCompilationJobsCommandInput,
  ...rest: any[]
) => Paginator<ListCompilationJobsCommandOutput>;
