import { Paginator } from "@smithy/types";
import { ListCompilationJobsCommandInput, ListCompilationJobsCommandOutput } from "../commands/ListCompilationJobsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListCompilationJobs: (config: SageMakerPaginationConfiguration, input: ListCompilationJobsCommandInput, ...rest: any[]) => Paginator<ListCompilationJobsCommandOutput>;
