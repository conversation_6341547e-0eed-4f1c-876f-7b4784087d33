import { Paginator } from "@smithy/types";
import {
  ListClusterSchedulerConfigsCommandInput,
  ListClusterSchedulerConfigsCommandOutput,
} from "../commands/ListClusterSchedulerConfigsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
export declare const paginateListClusterSchedulerConfigs: (
  config: SageMakerPaginationConfiguration,
  input: ListClusterSchedulerConfigsCommandInput,
  ...rest: any[]
) => Paginator<ListClusterSchedulerConfigsCommandOutput>;
