import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ListStudioLifecycleConfigsRequest,
  ListStudioLifecycleConfigsResponse,
} from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface ListStudioLifecycleConfigsCommandInput
  extends ListStudioLifecycleConfigsRequest {}
export interface ListStudioLifecycleConfigsCommandOutput
  extends ListStudioLifecycleConfigsResponse,
    __MetadataBearer {}
declare const ListStudioLifecycleConfigsCommand_base: {
  new (
    input: ListStudioLifecycleConfigsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListStudioLifecycleConfigsCommandInput,
    ListStudioLifecycleConfigsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    ...[input]: [] | [ListStudioLifecycleConfigsCommandInput]
  ): import("@smithy/smithy-client").CommandImpl<
    ListStudioLifecycleConfigsCommandInput,
    ListStudioLifecycleConfigsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListStudioLifecycleConfigsCommand extends ListStudioLifecycleConfigsCommand_base {
  protected static __types: {
    api: {
      input: ListStudioLifecycleConfigsRequest;
      output: ListStudioLifecycleConfigsResponse;
    };
    sdk: {
      input: ListStudioLifecycleConfigsCommandInput;
      output: ListStudioLifecycleConfigsCommandOutput;
    };
  };
}
