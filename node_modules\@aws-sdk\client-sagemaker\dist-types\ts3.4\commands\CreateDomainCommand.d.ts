import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { CreateDomainRequest, CreateDomainResponse } from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateDomainCommandInput extends CreateDomainRequest {}
export interface CreateDomainCommandOutput
  extends CreateDomainResponse,
    __MetadataBearer {}
declare const CreateDomainCommand_base: {
  new (
    input: CreateDomainCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateDomainCommandInput,
    CreateDomainCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateDomainCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateDomainCommandInput,
    CreateDomainCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateDomainCommand extends CreateDomainCommand_base {
  protected static __types: {
    api: {
      input: CreateDomainRequest;
      output: CreateDomainResponse;
    };
    sdk: {
      input: CreateDomainCommandInput;
      output: CreateDomainCommandOutput;
    };
  };
}
