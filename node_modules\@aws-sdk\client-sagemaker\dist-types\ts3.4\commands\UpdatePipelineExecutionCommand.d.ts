import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  UpdatePipelineExecutionRequest,
  UpdatePipelineExecutionResponse,
} from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdatePipelineExecutionCommandInput
  extends UpdatePipelineExecutionRequest {}
export interface UpdatePipelineExecutionCommandOutput
  extends UpdatePipelineExecutionResponse,
    __MetadataBearer {}
declare const UpdatePipelineExecutionCommand_base: {
  new (
    input: UpdatePipelineExecutionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdatePipelineExecutionCommandInput,
    UpdatePipelineExecutionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdatePipelineExecutionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdatePipelineExecutionCommandInput,
    UpdatePipelineExecutionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdatePipelineExecutionCommand extends UpdatePipelineExecutionCommand_base {
  protected static __types: {
    api: {
      input: UpdatePipelineExecutionRequest;
      output: UpdatePipelineExecutionResponse;
    };
    sdk: {
      input: UpdatePipelineExecutionCommandInput;
      output: UpdatePipelineExecutionCommandOutput;
    };
  };
}
