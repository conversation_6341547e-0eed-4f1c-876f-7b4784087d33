import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  UpdateNotebookInstanceInput,
  UpdateNotebookInstanceOutput,
} from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateNotebookInstanceCommandInput
  extends UpdateNotebookInstanceInput {}
export interface UpdateNotebookInstanceCommandOutput
  extends UpdateNotebookInstanceOutput,
    __MetadataBearer {}
declare const UpdateNotebookInstanceCommand_base: {
  new (
    input: UpdateNotebookInstanceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateNotebookInstanceCommandInput,
    UpdateNotebookInstanceCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateNotebookInstanceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateNotebookInstanceCommandInput,
    UpdateNotebookInstanceCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateNotebookInstanceCommand extends UpdateNotebookInstanceCommand_base {
  protected static __types: {
    api: {
      input: UpdateNotebookInstanceInput;
      output: {};
    };
    sdk: {
      input: UpdateNotebookInstanceCommandInput;
      output: UpdateNotebookInstanceCommandOutput;
    };
  };
}
