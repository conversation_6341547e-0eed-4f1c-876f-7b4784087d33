import { Command as $Command } from "@smithy/smithy-client";
import { <PERSON>ada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DeleteInferenceComponentInput } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteInferenceComponentCommandInput
  extends DeleteInferenceComponentInput {}
export interface DeleteInferenceComponentCommandOutput
  extends __MetadataBearer {}
declare const DeleteInferenceComponentCommand_base: {
  new (
    input: DeleteInferenceComponentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteInferenceComponentCommandInput,
    DeleteInferenceComponentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteInferenceComponentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteInferenceComponentCommandInput,
    DeleteInferenceComponentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteInferenceComponentCommand extends DeleteInferenceComponentCommand_base {
  protected static __types: {
    api: {
      input: DeleteInferenceComponentInput;
      output: {};
    };
    sdk: {
      input: DeleteInferenceComponentCommandInput;
      output: DeleteInferenceComponentCommandOutput;
    };
  };
}
