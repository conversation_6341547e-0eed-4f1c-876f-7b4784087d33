import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DeleteInferenceExperimentRequest,
  DeleteInferenceExperimentResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteInferenceExperimentCommandInput
  extends DeleteInferenceExperimentRequest {}
export interface DeleteInferenceExperimentCommandOutput
  extends DeleteInferenceExperimentResponse,
    __MetadataBearer {}
declare const DeleteInferenceExperimentCommand_base: {
  new (
    input: DeleteInferenceExperimentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteInferenceExperimentCommandInput,
    DeleteInferenceExperimentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteInferenceExperimentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteInferenceExperimentCommandInput,
    DeleteInferenceExperimentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteInferenceExperimentCommand extends DeleteInferenceExperimentCommand_base {
  protected static __types: {
    api: {
      input: DeleteInferenceExperimentRequest;
      output: DeleteInferenceExperimentResponse;
    };
    sdk: {
      input: DeleteInferenceExperimentCommandInput;
      output: DeleteInferenceExperimentCommandOutput;
    };
  };
}
