import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeDataQualityJobDefinitionRequest,
  DescribeDataQualityJobDefinitionResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeDataQualityJobDefinitionCommandInput
  extends DescribeDataQualityJobDefinitionRequest {}
export interface DescribeDataQualityJobDefinitionCommandOutput
  extends DescribeDataQualityJobDefinitionResponse,
    __MetadataBearer {}
declare const DescribeDataQualityJobDefinitionCommand_base: {
  new (
    input: DescribeDataQualityJobDefinitionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeDataQualityJobDefinitionCommandInput,
    DescribeDataQualityJobDefinitionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeDataQualityJobDefinitionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeDataQualityJobDefinitionCommandInput,
    DescribeDataQualityJobDefinitionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeDataQualityJobDefinitionCommand extends DescribeDataQualityJobDefinitionCommand_base {
  protected static __types: {
    api: {
      input: DescribeDataQualityJobDefinitionRequest;
      output: DescribeDataQualityJobDefinitionResponse;
    };
    sdk: {
      input: DescribeDataQualityJobDefinitionCommandInput;
      output: DescribeDataQualityJobDefinitionCommandOutput;
    };
  };
}
