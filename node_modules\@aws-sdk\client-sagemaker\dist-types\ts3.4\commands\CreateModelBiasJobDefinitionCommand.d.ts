import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateModelBiasJobDefinitionRequest,
  CreateModelBiasJobDefinitionResponse,
} from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateModelBiasJobDefinitionCommandInput
  extends CreateModelBiasJobDefinitionRequest {}
export interface CreateModelBiasJobDefinitionCommandOutput
  extends CreateModelBiasJobDefinitionResponse,
    __MetadataBearer {}
declare const CreateModelBiasJobDefinitionCommand_base: {
  new (
    input: CreateModelBiasJobDefinitionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateModelBiasJobDefinitionCommandInput,
    CreateModelBiasJobDefinitionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateModelBiasJobDefinitionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateModelBiasJobDefinitionCommandInput,
    CreateModelBiasJobDefinitionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateModelBiasJobDefinitionCommand extends CreateModelBiasJobDefinitionCommand_base {
  protected static __types: {
    api: {
      input: CreateModelBiasJobDefinitionRequest;
      output: CreateModelBiasJobDefinitionResponse;
    };
    sdk: {
      input: CreateModelBiasJobDefinitionCommandInput;
      output: CreateModelBiasJobDefinitionCommandOutput;
    };
  };
}
