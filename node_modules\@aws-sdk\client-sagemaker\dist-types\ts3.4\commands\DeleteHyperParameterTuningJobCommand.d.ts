import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { DeleteHyperParameterTuningJobRequest } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteHyperParameterTuningJobCommandInput
  extends DeleteHyperParameterTuningJobRequest {}
export interface DeleteHyperParameterTuningJobCommandOutput
  extends __MetadataBearer {}
declare const DeleteHyperParameterTuningJobCommand_base: {
  new (
    input: DeleteHyperParameterTuningJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteHyperParameterTuningJobCommandInput,
    DeleteHyperParameterTuningJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteHyperParameterTuningJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteHyperParameterTuningJobCommandInput,
    DeleteHyperParameterTuningJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteHyperParameterTuningJobCommand extends DeleteHyperParameterTuningJobCommand_base {
  protected static __types: {
    api: {
      input: DeleteHyperParameterTuningJobRequest;
      output: {};
    };
    sdk: {
      input: DeleteHyperParameterTuningJobCommandInput;
      output: DeleteHyperParameterTuningJobCommandOutput;
    };
  };
}
