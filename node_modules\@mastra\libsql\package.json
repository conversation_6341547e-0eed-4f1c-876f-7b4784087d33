{"name": "@mastra/libsql", "version": "0.10.0", "description": "Libsql provider for Mastra - includes both vector and db storage capabilities", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "./package.json": "./package.json"}, "license": "MIT", "dependencies": {"@libsql/client": "^0.15.4"}, "devDependencies": {"@microsoft/api-extractor": "^7.52.5", "@types/node": "^20.17.32", "eslint": "^9.25.1", "tsup": "^8.4.0", "typescript": "^5.8.3", "vitest": "^3.1.2", "@internal/lint": "0.0.6", "@internal/storage-test-utils": "0.0.2", "@mastra/core": "0.10.0"}, "peerDependencies": {"@mastra/core": "^0.10.0"}, "scripts": {"build": "tsup src/index.ts --format esm,cjs --experimental-dts --clean --treeshake=smallest --splitting", "build:watch": "pnpm build --watch", "test": "vitest run", "lint": "eslint ."}}