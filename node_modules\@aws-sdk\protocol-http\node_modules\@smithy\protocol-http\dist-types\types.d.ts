import { FieldOptions as __FieldOptions, FieldPosition as __FieldPosition, HeaderBag as __HeaderBag, HttpHandlerOptions as __HttpHandlerOptions, HttpMessage as __HttpMessage } from "@smithy/types";
/**
 * @deprecated Use FieldOptions from `@smithy/types` instead
 */
export type FieldOptions = __FieldOptions;
/**
 * @deprecated Use FieldPosition from `@smithy/types` instead
 */
export type FieldPosition = __FieldPosition;
/**
 * @deprecated Use HeaderBag from `@smithy/types` instead
 */
export type HeaderBag = __HeaderBag;
/**
 * @deprecated Use HttpMessage from `@smithy/types` instead
 */
export type HttpMessage = __HttpMessage;
/**
 * @deprecated Use HttpHandlerOptions from `@smithy/types` instead
 */
export type HttpHandlerOptions = __HttpHandlerOptions;
