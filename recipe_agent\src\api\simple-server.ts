import express from 'express';
import cors from 'cors';
import { createServer } from 'http';
import { Server } from 'socket.io';

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Chat message interface
interface ChatMessage {
  id: string;
  text: string;
  user: {
    _id: string;
    name: string;
    avatar?: string;
  };
  createdAt: Date;
  typing?: boolean;
}

// Mock recipe responses
const mockRecipeResponses = [
  "🍳 Harika! Bu malzemelerle menemen yapabilirsin:\n\n**Malzemeler:**\n- 3 yumurta\n- 2 domates\n- 100g beyaz peynir\n- 1 yemek kaşığı tereyağı\n\n**Yapılışı:**\n1. Domatesleri küp küp doğra\n2. Tavada tereyağını eritip domatesleri kavur\n3. Yumurtaları çırp ve tavaya ekle\n4. Peyniri ufalayıp üzerine serp\n5. Karıştırarak pişir. Afiyet olsun! 😋",
  
  "🥪 Bu malzemelerle omlet de yapabilirsin:\n\n**Omlet Tarifi:**\n- Yumurtaları çırp\n- Tavada pişir\n- İçine domates ve peynir ekle\n- Katla ve servis et!\n\nBaşka tarif ister misin? 🤔",
  
  "🍕 Kahvaltı pizzası nasıl olur?\n\n**Malzemeler:**\n- Tortilla ya da lavash\n- Yumurta, domates, peynir\n\n**Yapılışı:**\n1. Tortillayı tavaya koy\n2. Üzerine malzemeleri yerleştir\n3. Kapakla kapatıp pişir\n\nDenemeye değer! 👨‍🍳"
];

let responseIndex = 0;

// REST API endpoint for testing
app.post('/api/chat', async (req, res) => {
  try {
    const { message } = req.body;
    
    if (!message) {
      return res.status(400).json({ error: 'Mesaj gerekli' });
    }

    console.log('📨 Gelen mesaj:', message);

    // Mock response
    const response = mockRecipeResponses[responseIndex % mockRecipeResponses.length];
    responseIndex++;
    
    console.log('🤖 Bot yanıtı:', response.substring(0, 50) + '...');

    res.json({
      success: true,
      response: response,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Chat API hatası:', error);
    res.status(500).json({ 
      error: 'Bir hata oluştu',
      details: error.message 
    });
  }
});

// Socket.IO for real-time chat
io.on('connection', (socket) => {
  console.log('👤 Kullanıcı bağlandı:', socket.id);

  // Hoş geldin mesajı gönder
  const welcomeMessage: ChatMessage = {
    id: Date.now().toString(),
    text: '👋 Merhaba! Ben tarif asistanınızım. Hangi malzemeleriniz var? Onlarla neler yapabileceğinizi söyleyeyim! 🍳\n\nÖrnek: "Yumurta, domates ve peynir var"',
    user: {
      _id: 'recipe-bot',
      name: 'Tarif Asistanı',
      avatar: '🤖'
    },
    createdAt: new Date()
  };

  socket.emit('message', welcomeMessage);

  // Kullanıcı mesajı geldiğinde
  socket.on('user_message', async (data: { message: string, userId: string }) => {
    try {
      console.log('📨 Socket mesajı:', data.message);

      // Typing indicator gönder
      socket.emit('bot_typing');

      // Simulate processing time
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Mock response
      const response = mockRecipeResponses[responseIndex % mockRecipeResponses.length];
      responseIndex++;

      // Typing indicator'ı kaldır
      socket.emit('bot_stop_typing');

      // Bot yanıtını gönder
      const botMessage: ChatMessage = {
        id: Date.now().toString(),
        text: response,
        user: {
          _id: 'recipe-bot',
          name: 'Tarif Asistanı',
          avatar: '🤖'
        },
        createdAt: new Date()
      };

      socket.emit('bot_message', botMessage);

      console.log('🤖 Bot yanıtı gönderildi:', response.substring(0, 50) + '...');

    } catch (error) {
      console.error('❌ Socket chat hatası:', error);
      
      // Hata mesajı gönder
      const errorMessage: ChatMessage = {
        id: Date.now().toString(),
        text: '😅 Üzgünüm, bir hata oluştu. Lütfen tekrar deneyin.',
        user: {
          _id: 'recipe-bot',
          name: 'Tarif Asistanı',
          avatar: '🤖'
        },
        createdAt: new Date()
      };

      socket.emit('bot_message', errorMessage);
    }
  });

  socket.on('disconnect', () => {
    console.log('👋 Kullanıcı ayrıldı:', socket.id);
  });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    message: 'Recipe Chat API Server çalışıyor (Mock Mode)',
    timestamp: new Date().toISOString()
  });
});

// Start server
server.listen(PORT, () => {
  console.log('🚀 Recipe Chat API Server başlatıldı! (Mock Mode)');
  console.log(`📡 HTTP API: http://localhost:${PORT}`);
  console.log(`🔌 WebSocket: ws://localhost:${PORT}`);
  console.log('🍳 Tarif chatbot hazır!');
});

export default app;
