import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { DeleteModelExplainabilityJobDefinitionRequest } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteModelExplainabilityJobDefinitionCommandInput
  extends DeleteModelExplainabilityJobDefinitionRequest {}
export interface DeleteModelExplainabilityJobDefinitionCommandOutput
  extends __MetadataBearer {}
declare const DeleteModelExplainabilityJobDefinitionCommand_base: {
  new (
    input: DeleteModelExplainabilityJobDefinitionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteModelExplainabilityJobDefinitionCommandInput,
    DeleteModelExplainabilityJobDefinitionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteModelExplainabilityJobDefinitionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteModelExplainabilityJobDefinitionCommandInput,
    DeleteModelExplainabilityJobDefinitionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteModelExplainabilityJobDefinitionCommand extends DeleteModelExplainabilityJobDefinitionCommand_base {
  protected static __types: {
    api: {
      input: DeleteModelExplainabilityJobDefinitionRequest;
      output: {};
    };
    sdk: {
      input: DeleteModelExplainabilityJobDefinitionCommandInput;
      output: DeleteModelExplainabilityJobDefinitionCommandOutput;
    };
  };
}
