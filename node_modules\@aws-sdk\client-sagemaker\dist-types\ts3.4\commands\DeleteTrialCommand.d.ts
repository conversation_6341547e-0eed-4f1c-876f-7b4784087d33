import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DeleteTrialRequest, DeleteTrialResponse } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteTrialCommandInput extends DeleteTrialRequest {}
export interface DeleteTrialCommandOutput
  extends DeleteTrialResponse,
    __MetadataBearer {}
declare const DeleteTrialCommand_base: {
  new (
    input: DeleteTrialCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteTrialCommandInput,
    DeleteTrialCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteTrialCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteTrialCommandInput,
    DeleteTrialCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteTrialCommand extends DeleteTrialCommand_base {
  protected static __types: {
    api: {
      input: DeleteTrialRequest;
      output: DeleteTrialResponse;
    };
    sdk: {
      input: DeleteTrialCommandInput;
      output: DeleteTrialCommandOutput;
    };
  };
}
