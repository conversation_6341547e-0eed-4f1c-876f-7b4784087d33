import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ListHubContentVersionsRequest,
  ListHubContentVersionsResponse,
} from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface ListHubContentVersionsCommandInput
  extends ListHubContentVersionsRequest {}
export interface ListHubContentVersionsCommandOutput
  extends ListHubContentVersionsResponse,
    __MetadataBearer {}
declare const ListHubContentVersionsCommand_base: {
  new (
    input: ListHubContentVersionsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListHubContentVersionsCommandInput,
    ListHubContentVersionsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: ListHubContentVersionsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListHubContentVersionsCommandInput,
    ListHubContentVersionsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListHubContentVersionsCommand extends ListHubContentVersionsCommand_base {
  protected static __types: {
    api: {
      input: ListHubContentVersionsRequest;
      output: ListHubContentVersionsResponse;
    };
    sdk: {
      input: ListHubContentVersionsCommandInput;
      output: ListHubContentVersionsCommandOutput;
    };
  };
}
