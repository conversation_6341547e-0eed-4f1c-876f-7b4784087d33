import { MastraBase } from './chunk-FI7R232B.js';

// src/vector/vector.ts
var MastraVector = class extends MastraBase {
  constructor() {
    super({ name: "<PERSON>straVector", component: "VECTOR" });
  }
  get indexSeparator() {
    return "_";
  }
  async validateExistingIndex(indexName, dimension, metric) {
    let info;
    try {
      info = await this.describeIndex({ indexName });
    } catch (infoError) {
      const message = `Index "${indexName}" already exists, but failed to fetch index info for dimension check: ${infoError}`;
      this.logger?.error(message);
      throw new Error(message);
    }
    const existingDim = info?.dimension;
    const existingMetric = info?.metric;
    if (existingDim === dimension) {
      this.logger?.info(
        `Index "${indexName}" already exists with ${existingDim} dimensions and metric ${existingMetric}, skipping creation.`
      );
      if (existingMetric !== metric) {
        this.logger?.warn(
          `Attempted to create index with metric "${metric}", but index already exists with metric "${existingMetric}". To use a different metric, delete and recreate the index.`
        );
      }
    } else if (info) {
      const message = `Index "${indexName}" already exists with ${existingDim} dimensions, but ${dimension} dimensions were requested`;
      this.logger?.error(message);
      throw new Error(message);
    } else {
      const message = `Index "${indexName}" already exists, but could not retrieve its dimensions for validation.`;
      this.logger?.error(message);
      throw new Error(message);
    }
  }
};

export { MastraVector };
