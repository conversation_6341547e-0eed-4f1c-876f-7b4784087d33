import { SENSITIVE_STRING } from "@smithy/smithy-client";
import { ModelPackageModelCardFilterSensitiveLog, } from "./models_1";
export const HubContentStatus = {
    AVAILABLE: "Available",
    DELETE_FAILED: "DeleteFailed",
    DELETING: "Deleting",
    IMPORTING: "Importing",
    IMPORT_FAILED: "ImportFailed",
};
export const HubContentSupportStatus = {
    DEPRECATED: "Deprecated",
    RESTRICTED: "Restricted",
    SUPPORTED: "Supported",
};
export const HumanTaskUiStatus = {
    ACTIVE: "Active",
    DELETING: "Deleting",
};
export const TrainingJobStatus = {
    COMPLETED: "Completed",
    FAILED: "Failed",
    IN_PROGRESS: "InProgress",
    STOPPED: "Stopped",
    STOPPING: "Stopping",
};
export const HyperParameterTuningJobStatus = {
    COMPLETED: "Completed",
    DELETE_FAILED: "DeleteFailed",
    DELETING: "Deleting",
    FAILED: "Failed",
    IN_PROGRESS: "InProgress",
    STOPPED: "Stopped",
    STOPPING: "Stopping",
};
export const ImageStatus = {
    CREATED: "CREATED",
    CREATE_FAILED: "CREATE_FAILED",
    CREATING: "CREATING",
    DELETE_FAILED: "DELETE_FAILED",
    DELETING: "DELETING",
    UPDATE_FAILED: "UPDATE_FAILED",
    UPDATING: "UPDATING",
};
export const ImageVersionStatus = {
    CREATED: "CREATED",
    CREATE_FAILED: "CREATE_FAILED",
    CREATING: "CREATING",
    DELETE_FAILED: "DELETE_FAILED",
    DELETING: "DELETING",
};
export const InferenceComponentStatus = {
    CREATING: "Creating",
    DELETING: "Deleting",
    FAILED: "Failed",
    IN_SERVICE: "InService",
    UPDATING: "Updating",
};
export const InferenceComponentCapacitySizeType = {
    CAPACITY_PERCENT: "CAPACITY_PERCENT",
    COPY_COUNT: "COPY_COUNT",
};
export const ModelVariantStatus = {
    CREATING: "Creating",
    DELETED: "Deleted",
    DELETING: "Deleting",
    IN_SERVICE: "InService",
    UPDATING: "Updating",
};
export const InferenceExperimentStatus = {
    CANCELLED: "Cancelled",
    COMPLETED: "Completed",
    CREATED: "Created",
    CREATING: "Creating",
    RUNNING: "Running",
    STARTING: "Starting",
    STOPPING: "Stopping",
    UPDATING: "Updating",
};
export const RecommendationJobStatus = {
    COMPLETED: "COMPLETED",
    DELETED: "DELETED",
    DELETING: "DELETING",
    FAILED: "FAILED",
    IN_PROGRESS: "IN_PROGRESS",
    PENDING: "PENDING",
    STOPPED: "STOPPED",
    STOPPING: "STOPPING",
};
export const LabelingJobStatus = {
    COMPLETED: "Completed",
    FAILED: "Failed",
    INITIALIZING: "Initializing",
    IN_PROGRESS: "InProgress",
    STOPPED: "Stopped",
    STOPPING: "Stopping",
};
export const IsTrackingServerActive = {
    ACTIVE: "Active",
    INACTIVE: "Inactive",
};
export const TrackingServerStatus = {
    CREATED: "Created",
    CREATE_FAILED: "CreateFailed",
    CREATING: "Creating",
    DELETE_FAILED: "DeleteFailed",
    DELETING: "Deleting",
    MAINTENANCE_COMPLETE: "MaintenanceComplete",
    MAINTENANCE_FAILED: "MaintenanceFailed",
    MAINTENANCE_IN_PROGRESS: "MaintenanceInProgress",
    STARTED: "Started",
    STARTING: "Starting",
    START_FAILED: "StartFailed",
    STOPPED: "Stopped",
    STOPPING: "Stopping",
    STOP_FAILED: "StopFailed",
    UPDATED: "Updated",
    UPDATE_FAILED: "UpdateFailed",
    UPDATING: "Updating",
};
export const ModelCardProcessingStatus = {
    CONTENT_DELETED: "ContentDeleted",
    DELETE_COMPLETED: "DeleteCompleted",
    DELETE_FAILED: "DeleteFailed",
    DELETE_INPROGRESS: "DeleteInProgress",
    DELETE_PENDING: "DeletePending",
    EXPORTJOBS_DELETED: "ExportJobsDeleted",
};
export const ModelCardExportJobStatus = {
    COMPLETED: "Completed",
    FAILED: "Failed",
    IN_PROGRESS: "InProgress",
};
export const DetailedModelPackageStatus = {
    COMPLETED: "Completed",
    FAILED: "Failed",
    IN_PROGRESS: "InProgress",
    NOT_STARTED: "NotStarted",
};
export const ModelPackageGroupStatus = {
    COMPLETED: "Completed",
    DELETE_FAILED: "DeleteFailed",
    DELETING: "Deleting",
    FAILED: "Failed",
    IN_PROGRESS: "InProgress",
    PENDING: "Pending",
};
export const ExecutionStatus = {
    COMPLETED: "Completed",
    COMPLETED_WITH_VIOLATIONS: "CompletedWithViolations",
    FAILED: "Failed",
    IN_PROGRESS: "InProgress",
    PENDING: "Pending",
    STOPPED: "Stopped",
    STOPPING: "Stopping",
};
export const ScheduleStatus = {
    FAILED: "Failed",
    PENDING: "Pending",
    SCHEDULED: "Scheduled",
    STOPPED: "Stopped",
};
export const NotebookInstanceStatus = {
    Deleting: "Deleting",
    Failed: "Failed",
    InService: "InService",
    Pending: "Pending",
    Stopped: "Stopped",
    Stopping: "Stopping",
    Updating: "Updating",
};
export const OptimizationJobStatus = {
    COMPLETED: "COMPLETED",
    FAILED: "FAILED",
    INPROGRESS: "INPROGRESS",
    STARTING: "STARTING",
    STOPPED: "STOPPED",
    STOPPING: "STOPPING",
};
export const PartnerAppStatus = {
    AVAILABLE: "Available",
    CREATING: "Creating",
    DELETED: "Deleted",
    DELETING: "Deleting",
    FAILED: "Failed",
    UPDATE_FAILED: "UpdateFailed",
    UPDATING: "Updating",
};
export const PipelineStatus = {
    ACTIVE: "Active",
    DELETING: "Deleting",
};
export const PipelineExecutionStatus = {
    EXECUTING: "Executing",
    FAILED: "Failed",
    STOPPED: "Stopped",
    STOPPING: "Stopping",
    SUCCEEDED: "Succeeded",
};
export const ProcessingJobStatus = {
    COMPLETED: "Completed",
    FAILED: "Failed",
    IN_PROGRESS: "InProgress",
    STOPPED: "Stopped",
    STOPPING: "Stopping",
};
export const ProjectStatus = {
    CREATE_COMPLETED: "CreateCompleted",
    CREATE_FAILED: "CreateFailed",
    CREATE_IN_PROGRESS: "CreateInProgress",
    DELETE_COMPLETED: "DeleteCompleted",
    DELETE_FAILED: "DeleteFailed",
    DELETE_IN_PROGRESS: "DeleteInProgress",
    PENDING: "Pending",
    UPDATE_COMPLETED: "UpdateCompleted",
    UPDATE_FAILED: "UpdateFailed",
    UPDATE_IN_PROGRESS: "UpdateInProgress",
};
export const SpaceStatus = {
    Delete_Failed: "Delete_Failed",
    Deleting: "Deleting",
    Failed: "Failed",
    InService: "InService",
    Pending: "Pending",
    Update_Failed: "Update_Failed",
    Updating: "Updating",
};
export const ProfilingStatus = {
    DISABLED: "Disabled",
    ENABLED: "Enabled",
};
export const SecondaryStatus = {
    COMPLETED: "Completed",
    DOWNLOADING: "Downloading",
    DOWNLOADING_TRAINING_IMAGE: "DownloadingTrainingImage",
    FAILED: "Failed",
    INTERRUPTED: "Interrupted",
    LAUNCHING_ML_INSTANCES: "LaunchingMLInstances",
    MAX_RUNTIME_EXCEEDED: "MaxRuntimeExceeded",
    MAX_WAIT_TIME_EXCEEDED: "MaxWaitTimeExceeded",
    PENDING: "Pending",
    PREPARING_TRAINING_STACK: "PreparingTrainingStack",
    RESTARTING: "Restarting",
    STARTING: "Starting",
    STOPPED: "Stopped",
    STOPPING: "Stopping",
    TRAINING: "Training",
    UPDATING: "Updating",
    UPLOADING: "Uploading",
};
export const WarmPoolResourceStatus = {
    AVAILABLE: "Available",
    INUSE: "InUse",
    REUSED: "Reused",
    TERMINATED: "Terminated",
};
export const ReservedCapacityInstanceType = {
    ML_P4D_24XLARGE: "ml.p4d.24xlarge",
    ML_P5EN_48XLARGE: "ml.p5en.48xlarge",
    ML_P5E_48XLARGE: "ml.p5e.48xlarge",
    ML_P5_48XLARGE: "ml.p5.48xlarge",
    ML_TRN1_32XLARGE: "ml.trn1.32xlarge",
    ML_TRN2_48XLARGE: "ml.trn2.48xlarge",
};
export const ReservedCapacityStatus = {
    ACTIVE: "Active",
    EXPIRED: "Expired",
    FAILED: "Failed",
    PENDING: "Pending",
    SCHEDULED: "Scheduled",
};
export const TrainingPlanStatus = {
    ACTIVE: "Active",
    EXPIRED: "Expired",
    FAILED: "Failed",
    PENDING: "Pending",
    SCHEDULED: "Scheduled",
};
export const SageMakerResourceName = {
    HYPERPOD_CLUSTER: "hyperpod-cluster",
    TRAINING_JOB: "training-job",
};
export const TransformJobStatus = {
    COMPLETED: "Completed",
    FAILED: "Failed",
    IN_PROGRESS: "InProgress",
    STOPPED: "Stopped",
    STOPPING: "Stopping",
};
export const UserProfileStatus = {
    Delete_Failed: "Delete_Failed",
    Deleting: "Deleting",
    Failed: "Failed",
    InService: "InService",
    Pending: "Pending",
    Update_Failed: "Update_Failed",
    Updating: "Updating",
};
export const WorkforceStatus = {
    ACTIVE: "Active",
    DELETING: "Deleting",
    FAILED: "Failed",
    INITIALIZING: "Initializing",
    UPDATING: "Updating",
};
export const DeviceDeploymentStatus = {
    Deployed: "DEPLOYED",
    Failed: "FAILED",
    InProgress: "INPROGRESS",
    ReadyToDeploy: "READYTODEPLOY",
    Stopped: "STOPPED",
    Stopping: "STOPPING",
};
export const Direction = {
    ASCENDANTS: "Ascendants",
    BOTH: "Both",
    DESCENDANTS: "Descendants",
};
export var MetricSpecification;
(function (MetricSpecification) {
    MetricSpecification.visit = (value, visitor) => {
        if (value.Predefined !== undefined)
            return visitor.Predefined(value.Predefined);
        if (value.Customized !== undefined)
            return visitor.Customized(value.Customized);
        return visitor._(value.$unknown[0], value.$unknown[1]);
    };
})(MetricSpecification || (MetricSpecification = {}));
export var ScalingPolicy;
(function (ScalingPolicy) {
    ScalingPolicy.visit = (value, visitor) => {
        if (value.TargetTracking !== undefined)
            return visitor.TargetTracking(value.TargetTracking);
        return visitor._(value.$unknown[0], value.$unknown[1]);
    };
})(ScalingPolicy || (ScalingPolicy = {}));
export const EndpointConfigSortKey = {
    CreationTime: "CreationTime",
    Name: "Name",
};
export const EndpointSortKey = {
    CreationTime: "CreationTime",
    Name: "Name",
    Status: "Status",
};
export const FeatureGroupSortBy = {
    CREATION_TIME: "CreationTime",
    FEATURE_GROUP_STATUS: "FeatureGroupStatus",
    NAME: "Name",
    OFFLINE_STORE_STATUS: "OfflineStoreStatus",
};
export const FeatureGroupSortOrder = {
    ASCENDING: "Ascending",
    DESCENDING: "Descending",
};
export const Operator = {
    CONTAINS: "Contains",
    EQUALS: "Equals",
    EXISTS: "Exists",
    GREATER_THAN: "GreaterThan",
    GREATER_THAN_OR_EQUAL_TO: "GreaterThanOrEqualTo",
    IN: "In",
    LESS_THAN: "LessThan",
    LESS_THAN_OR_EQUAL_TO: "LessThanOrEqualTo",
    NOT_EQUALS: "NotEquals",
    NOT_EXISTS: "NotExists",
};
export const SagemakerServicecatalogStatus = {
    DISABLED: "Disabled",
    ENABLED: "Enabled",
};
export const ResourceType = {
    ENDPOINT: "Endpoint",
    EXPERIMENT: "Experiment",
    EXPERIMENT_TRIAL: "ExperimentTrial",
    EXPERIMENT_TRIAL_COMPONENT: "ExperimentTrialComponent",
    FEATURE_GROUP: "FeatureGroup",
    FEATURE_METADATA: "FeatureMetadata",
    HYPER_PARAMETER_TUNING_JOB: "HyperParameterTuningJob",
    IMAGE: "Image",
    IMAGE_VERSION: "ImageVersion",
    MODEL: "Model",
    MODEL_CARD: "ModelCard",
    MODEL_PACKAGE: "ModelPackage",
    MODEL_PACKAGE_GROUP: "ModelPackageGroup",
    PIPELINE: "Pipeline",
    PIPELINE_EXECUTION: "PipelineExecution",
    PROJECT: "Project",
    TRAINING_JOB: "TrainingJob",
};
export const HubContentSortBy = {
    CREATION_TIME: "CreationTime",
    HUB_CONTENT_NAME: "HubContentName",
    HUB_CONTENT_STATUS: "HubContentStatus",
};
export const HubSortBy = {
    ACCOUNT_ID_OWNER: "AccountIdOwner",
    CREATION_TIME: "CreationTime",
    HUB_NAME: "HubName",
    HUB_STATUS: "HubStatus",
};
export const HyperParameterTuningJobSortByOptions = {
    CreationTime: "CreationTime",
    Name: "Name",
    Status: "Status",
};
export const ImageSortBy = {
    CREATION_TIME: "CREATION_TIME",
    IMAGE_NAME: "IMAGE_NAME",
    LAST_MODIFIED_TIME: "LAST_MODIFIED_TIME",
};
export const ImageSortOrder = {
    ASCENDING: "ASCENDING",
    DESCENDING: "DESCENDING",
};
export const ImageVersionSortBy = {
    CREATION_TIME: "CREATION_TIME",
    LAST_MODIFIED_TIME: "LAST_MODIFIED_TIME",
    VERSION: "VERSION",
};
export const ImageVersionSortOrder = {
    ASCENDING: "ASCENDING",
    DESCENDING: "DESCENDING",
};
export const InferenceComponentSortKey = {
    CreationTime: "CreationTime",
    Name: "Name",
    Status: "Status",
};
export const InferenceExperimentStopDesiredState = {
    CANCELLED: "Cancelled",
    COMPLETED: "Completed",
};
export const RecommendationStepType = {
    BENCHMARK: "BENCHMARK",
};
export const LineageType = {
    ACTION: "Action",
    ARTIFACT: "Artifact",
    CONTEXT: "Context",
    TRIAL_COMPONENT: "TrialComponent",
};
export const SortActionsBy = {
    CREATION_TIME: "CreationTime",
    NAME: "Name",
};
export const SortOrder = {
    ASCENDING: "Ascending",
    DESCENDING: "Descending",
};
export const SortArtifactsBy = {
    CREATION_TIME: "CreationTime",
};
export const SortAssociationsBy = {
    CREATION_TIME: "CreationTime",
    DESTINATION_ARN: "DestinationArn",
    DESTINATION_TYPE: "DestinationType",
    SOURCE_ARN: "SourceArn",
    SOURCE_TYPE: "SourceType",
};
export const SortClusterSchedulerConfigBy = {
    CREATION_TIME: "CreationTime",
    NAME: "Name",
    STATUS: "Status",
};
export const ListCompilationJobsSortBy = {
    CREATION_TIME: "CreationTime",
    NAME: "Name",
    STATUS: "Status",
};
export const DescribeModelCardResponseFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.Content && { Content: SENSITIVE_STRING }),
});
export const DescribeModelPackageOutputFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.ModelCard && { ModelCard: ModelPackageModelCardFilterSensitiveLog(obj.ModelCard) }),
});
