import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ListMonitoringAlertsRequest,
  ListMonitoringAlertsResponse,
} from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface ListMonitoringAlertsCommandInput
  extends ListMonitoringAlertsRequest {}
export interface ListMonitoringAlertsCommandOutput
  extends ListMonitoringAlertsResponse,
    __MetadataBearer {}
declare const ListMonitoringAlertsCommand_base: {
  new (
    input: ListMonitoringAlertsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListMonitoringAlertsCommandInput,
    ListMonitoringAlertsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: ListMonitoringAlertsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListMonitoringAlertsCommandInput,
    ListMonitoringAlertsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListMonitoringAlertsCommand extends ListMonitoringAlertsCommand_base {
  protected static __types: {
    api: {
      input: ListMonitoringAlertsRequest;
      output: ListMonitoringAlertsResponse;
    };
    sdk: {
      input: ListMonitoringAlertsCommandInput;
      output: ListMonitoringAlertsCommandOutput;
    };
  };
}
