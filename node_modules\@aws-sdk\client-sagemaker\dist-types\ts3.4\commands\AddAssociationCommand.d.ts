import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  AddAssociationRequest,
  AddAssociationResponse,
} from "../models/models_0";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface AddAssociationCommandInput extends AddAssociationRequest {}
export interface AddAssociationCommandOutput
  extends AddAssociationResponse,
    __MetadataBearer {}
declare const AddAssociationCommand_base: {
  new (
    input: AddAssociationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    AddAssociationCommandInput,
    AddAssociationCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: AddAssociationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    AddAssociationCommandInput,
    AddAssociationCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class AddAssociationCommand extends AddAssociationCommand_base {
  protected static __types: {
    api: {
      input: AddAssociationRequest;
      output: AddAssociationResponse;
    };
    sdk: {
      input: AddAssociationCommandInput;
      output: AddAssociationCommandOutput;
    };
  };
}
