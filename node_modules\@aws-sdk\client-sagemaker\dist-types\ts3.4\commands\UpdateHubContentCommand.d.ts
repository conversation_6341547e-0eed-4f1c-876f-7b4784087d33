import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  UpdateHubContentRequest,
  UpdateHubContentResponse,
} from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateHubContentCommandInput extends UpdateHubContentRequest {}
export interface UpdateHubContentCommandOutput
  extends UpdateHubContentResponse,
    __MetadataBearer {}
declare const UpdateHubContentCommand_base: {
  new (
    input: UpdateHubContentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateHubContentCommandInput,
    UpdateHubContentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateHubContentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateHubContentCommandInput,
    UpdateHubContentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateHubContentCommand extends UpdateHubContentCommand_base {
  protected static __types: {
    api: {
      input: UpdateHubContentRequest;
      output: UpdateHubContentResponse;
    };
    sdk: {
      input: UpdateHubContentCommandInput;
      output: UpdateHubContentCommandOutput;
    };
  };
}
