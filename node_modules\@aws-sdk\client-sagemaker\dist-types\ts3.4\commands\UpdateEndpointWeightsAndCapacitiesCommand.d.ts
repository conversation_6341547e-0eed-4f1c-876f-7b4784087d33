import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  UpdateEndpointWeightsAndCapacitiesInput,
  UpdateEndpointWeightsAndCapacitiesOutput,
} from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateEndpointWeightsAndCapacitiesCommandInput
  extends UpdateEndpointWeightsAndCapacitiesInput {}
export interface UpdateEndpointWeightsAndCapacitiesCommandOutput
  extends UpdateEndpointWeightsAndCapacitiesOutput,
    __MetadataBearer {}
declare const UpdateEndpointWeightsAndCapacitiesCommand_base: {
  new (
    input: UpdateEndpointWeightsAndCapacitiesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateEndpointWeightsAndCapacitiesCommandInput,
    UpdateEndpointWeightsAndCapacitiesCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateEndpointWeightsAndCapacitiesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateEndpointWeightsAndCapacitiesCommandInput,
    UpdateEndpointWeightsAndCapacitiesCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateEndpointWeightsAndCapacitiesCommand extends UpdateEndpointWeightsAndCapacitiesCommand_base {
  protected static __types: {
    api: {
      input: UpdateEndpointWeightsAndCapacitiesInput;
      output: UpdateEndpointWeightsAndCapacitiesOutput;
    };
    sdk: {
      input: UpdateEndpointWeightsAndCapacitiesCommandInput;
      output: UpdateEndpointWeightsAndCapacitiesCommandOutput;
    };
  };
}
