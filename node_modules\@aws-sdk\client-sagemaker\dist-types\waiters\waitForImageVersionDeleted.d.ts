import { WaiterConfiguration, WaiterResult } from "@smithy/util-waiter";
import { DescribeImageVersionCommandInput } from "../commands/DescribeImageVersionCommand";
import { SageMakerClient } from "../SageMakerClient";
/**
 *
 *  @deprecated Use waitUntilImageVersionDeleted instead. waitForImageVersionDeleted does not throw error in non-success cases.
 */
export declare const waitForImageVersionDeleted: (params: WaiterConfiguration<SageMakerClient>, input: DescribeImageVersionCommandInput) => Promise<WaiterResult>;
/**
 *
 *  @param params - Waiter configuration options.
 *  @param input - The input to DescribeImageVersionCommand for polling.
 */
export declare const waitUntilImageVersionDeleted: (params: WaiterConfiguration<SageMakerClient>, input: DescribeImageVersionCommandInput) => Promise<WaiterResult>;
