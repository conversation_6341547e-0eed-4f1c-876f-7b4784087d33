import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateModelCardExportJobRequest,
  CreateModelCardExportJobResponse,
} from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateModelCardExportJobCommandInput
  extends CreateModelCardExportJobRequest {}
export interface CreateModelCardExportJobCommandOutput
  extends CreateModelCardExportJobResponse,
    __MetadataBearer {}
declare const CreateModelCardExportJobCommand_base: {
  new (
    input: CreateModelCardExportJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateModelCardExportJobCommandInput,
    CreateModelCardExportJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateModelCardExportJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateModelCardExportJobCommandInput,
    CreateModelCardExportJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateModelCardExportJobCommand extends CreateModelCardExportJobCommand_base {
  protected static __types: {
    api: {
      input: CreateModelCardExportJobRequest;
      output: CreateModelCardExportJobResponse;
    };
    sdk: {
      input: CreateModelCardExportJobCommandInput;
      output: CreateModelCardExportJobCommandOutput;
    };
  };
}
