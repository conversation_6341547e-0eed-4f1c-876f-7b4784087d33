import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DeleteDeviceFleetRequest } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteDeviceFleetCommandInput
  extends DeleteDeviceFleetRequest {}
export interface DeleteDeviceFleetCommandOutput extends __MetadataBearer {}
declare const DeleteDeviceFleetCommand_base: {
  new (
    input: DeleteDeviceFleetCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteDeviceFleetCommandInput,
    DeleteDeviceFleetCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteDeviceFleetCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteDeviceFleetCommandInput,
    DeleteDeviceFleetCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteDeviceFleetCommand extends DeleteDeviceFleetCommand_base {
  protected static __types: {
    api: {
      input: DeleteDeviceFleetRequest;
      output: {};
    };
    sdk: {
      input: DeleteDeviceFleetCommandInput;
      output: DeleteDeviceFleetCommandOutput;
    };
  };
}
