import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateOptimizationJobRequest,
  CreateOptimizationJobResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateOptimizationJobCommandInput
  extends CreateOptimizationJobRequest {}
export interface CreateOptimizationJobCommandOutput
  extends CreateOptimizationJobResponse,
    __MetadataBearer {}
declare const CreateOptimizationJobCommand_base: {
  new (
    input: CreateOptimizationJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateOptimizationJobCommandInput,
    CreateOptimizationJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateOptimizationJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateOptimizationJobCommandInput,
    CreateOptimizationJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateOptimizationJobCommand extends CreateOptimizationJobCommand_base {
  protected static __types: {
    api: {
      input: CreateOptimizationJobRequest;
      output: CreateOptimizationJobResponse;
    };
    sdk: {
      input: CreateOptimizationJobCommandInput;
      output: CreateOptimizationJobCommandOutput;
    };
  };
}
