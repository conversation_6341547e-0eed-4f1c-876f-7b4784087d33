import { createPaginator } from "@smithy/core";
import { ListModelExplainabilityJobDefinitionsCommand, } from "../commands/ListModelExplainabilityJobDefinitionsCommand";
import { SageMakerClient } from "../SageMakerClient";
export const paginateListModelExplainabilityJobDefinitions = createPaginator(SageMakerClient, ListModelExplainabilityJobDefinitionsCommand, "NextToken", "NextToken", "MaxResults");
