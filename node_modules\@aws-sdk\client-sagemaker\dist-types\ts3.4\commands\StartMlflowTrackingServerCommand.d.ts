import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  StartMlflowTrackingServerRequest,
  StartMlflowTrackingServerResponse,
} from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface StartMlflowTrackingServerCommandInput
  extends StartMlflowTrackingServerRequest {}
export interface StartMlflowTrackingServerCommandOutput
  extends StartMlflowTrackingServerResponse,
    __MetadataBearer {}
declare const StartMlflowTrackingServerCommand_base: {
  new (
    input: StartMlflowTrackingServerCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StartMlflowTrackingServerCommandInput,
    StartMlflowTrackingServerCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: StartMlflowTrackingServerCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StartMlflowTrackingServerCommandInput,
    StartMlflowTrackingServerCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class StartMlflowTrackingServerCommand extends StartMlflowTrackingServerCommand_base {
  protected static __types: {
    api: {
      input: StartMlflowTrackingServerRequest;
      output: StartMlflowTrackingServerResponse;
    };
    sdk: {
      input: StartMlflowTrackingServerCommandInput;
      output: StartMlflowTrackingServerCommandOutput;
    };
  };
}
