import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ListModelExplainabilityJobDefinitionsRequest,
  ListModelExplainabilityJobDefinitionsResponse,
} from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface ListModelExplainabilityJobDefinitionsCommandInput
  extends ListModelExplainabilityJobDefinitionsRequest {}
export interface ListModelExplainabilityJobDefinitionsCommandOutput
  extends ListModelExplainabilityJobDefinitionsResponse,
    __MetadataBearer {}
declare const ListModelExplainabilityJobDefinitionsCommand_base: {
  new (
    input: ListModelExplainabilityJobDefinitionsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListModelExplainabilityJobDefinitionsCommandInput,
    ListModelExplainabilityJobDefinitionsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    ...[input]: [] | [ListModelExplainabilityJobDefinitionsCommandInput]
  ): import("@smithy/smithy-client").CommandImpl<
    ListModelExplainabilityJobDefinitionsCommandInput,
    ListModelExplainabilityJobDefinitionsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListModelExplainabilityJobDefinitionsCommand extends ListModelExplainabilityJobDefinitionsCommand_base {
  protected static __types: {
    api: {
      input: ListModelExplainabilityJobDefinitionsRequest;
      output: ListModelExplainabilityJobDefinitionsResponse;
    };
    sdk: {
      input: ListModelExplainabilityJobDefinitionsCommandInput;
      output: ListModelExplainabilityJobDefinitionsCommandOutput;
    };
  };
}
