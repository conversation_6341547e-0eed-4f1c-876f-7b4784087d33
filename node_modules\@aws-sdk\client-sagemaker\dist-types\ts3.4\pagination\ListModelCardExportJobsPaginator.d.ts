import { Paginator } from "@smithy/types";
import {
  ListModelCardExportJobsCommandInput,
  ListModelCardExportJobsCommandOutput,
} from "../commands/ListModelCardExportJobsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
export declare const paginateListModelCardExportJobs: (
  config: SageMakerPaginationConfiguration,
  input: ListModelCardExportJobsCommandInput,
  ...rest: any[]
) => Paginator<ListModelCardExportJobsCommandOutput>;
