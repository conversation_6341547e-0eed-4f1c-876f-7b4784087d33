import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeHubContentRequest,
  DescribeHubContentResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeHubContentCommandInput
  extends DescribeHubContentRequest {}
export interface DescribeHubContentCommandOutput
  extends DescribeHubContentResponse,
    __MetadataBearer {}
declare const DescribeHubContentCommand_base: {
  new (
    input: DescribeHubContentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeHubContentCommandInput,
    DescribeHubContentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeHubContentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeHubContentCommandInput,
    DescribeHubContentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeHubContentCommand extends DescribeHubContentCommand_base {
  protected static __types: {
    api: {
      input: DescribeHubContentRequest;
      output: DescribeHubContentResponse;
    };
    sdk: {
      input: DescribeHubContentCommandInput;
      output: DescribeHubContentCommandOutput;
    };
  };
}
