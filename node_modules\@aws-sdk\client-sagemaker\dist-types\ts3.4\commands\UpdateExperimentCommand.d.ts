import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  UpdateExperimentRequest,
  UpdateExperimentResponse,
} from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateExperimentCommandInput extends UpdateExperimentRequest {}
export interface UpdateExperimentCommandOutput
  extends UpdateExperimentResponse,
    __MetadataBearer {}
declare const UpdateExperimentCommand_base: {
  new (
    input: UpdateExperimentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateExperimentCommandInput,
    UpdateExperimentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateExperimentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateExperimentCommandInput,
    UpdateExperimentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateExperimentCommand extends UpdateExperimentCommand_base {
  protected static __types: {
    api: {
      input: UpdateExperimentRequest;
      output: UpdateExperimentResponse;
    };
    sdk: {
      input: UpdateExperimentCommandInput;
      output: UpdateExperimentCommandOutput;
    };
  };
}
