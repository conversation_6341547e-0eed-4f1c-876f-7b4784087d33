import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  AssociateTrialComponentRequest,
  AssociateTrialComponentResponse,
} from "../models/models_0";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface AssociateTrialComponentCommandInput
  extends AssociateTrialComponentRequest {}
export interface AssociateTrialComponentCommandOutput
  extends AssociateTrialComponentResponse,
    __MetadataBearer {}
declare const AssociateTrialComponentCommand_base: {
  new (
    input: AssociateTrialComponentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    AssociateTrialComponentCommandInput,
    AssociateTrialComponentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: AssociateTrialComponentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    AssociateTrialComponentCommandInput,
    AssociateTrialComponentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class AssociateTrialComponentCommand extends AssociateTrialComponentCommand_base {
  protected static __types: {
    api: {
      input: AssociateTrialComponentRequest;
      output: AssociateTrialComponentResponse;
    };
    sdk: {
      input: AssociateTrialComponentCommandInput;
      output: AssociateTrialComponentCommandOutput;
    };
  };
}
