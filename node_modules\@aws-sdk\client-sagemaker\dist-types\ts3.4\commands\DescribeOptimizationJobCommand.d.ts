import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeOptimizationJobRequest,
  DescribeOptimizationJobResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeOptimizationJobCommandInput
  extends DescribeOptimizationJobRequest {}
export interface DescribeOptimizationJobCommandOutput
  extends DescribeOptimizationJobResponse,
    __MetadataBearer {}
declare const DescribeOptimizationJobCommand_base: {
  new (
    input: DescribeOptimizationJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeOptimizationJobCommandInput,
    DescribeOptimizationJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeOptimizationJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeOptimizationJobCommandInput,
    DescribeOptimizationJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeOptimizationJobCommand extends DescribeOptimizationJobCommand_base {
  protected static __types: {
    api: {
      input: DescribeOptimizationJobRequest;
      output: DescribeOptimizationJobResponse;
    };
    sdk: {
      input: DescribeOptimizationJobCommandInput;
      output: DescribeOptimizationJobCommandOutput;
    };
  };
}
