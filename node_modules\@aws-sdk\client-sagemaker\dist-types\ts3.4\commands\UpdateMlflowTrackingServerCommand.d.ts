import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  UpdateMlflowTrackingServerRequest,
  UpdateMlflowTrackingServerResponse,
} from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateMlflowTrackingServerCommandInput
  extends UpdateMlflowTrackingServerRequest {}
export interface UpdateMlflowTrackingServerCommandOutput
  extends UpdateMlflowTrackingServerResponse,
    __MetadataBearer {}
declare const UpdateMlflowTrackingServerCommand_base: {
  new (
    input: UpdateMlflowTrackingServerCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateMlflowTrackingServerCommandInput,
    UpdateMlflowTrackingServerCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateMlflowTrackingServerCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateMlflowTrackingServerCommandInput,
    UpdateMlflowTrackingServerCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateMlflowTrackingServerCommand extends UpdateMlflowTrackingServerCommand_base {
  protected static __types: {
    api: {
      input: UpdateMlflowTrackingServerRequest;
      output: UpdateMlflowTrackingServerResponse;
    };
    sdk: {
      input: UpdateMlflowTrackingServerCommandInput;
      output: UpdateMlflowTrackingServerCommandOutput;
    };
  };
}
