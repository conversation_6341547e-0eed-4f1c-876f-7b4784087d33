import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeComputeQuotaRequest,
  DescribeComputeQuotaResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeComputeQuotaCommandInput
  extends DescribeComputeQuotaRequest {}
export interface DescribeComputeQuotaCommandOutput
  extends DescribeComputeQuotaResponse,
    __MetadataBearer {}
declare const DescribeComputeQuotaCommand_base: {
  new (
    input: DescribeComputeQuotaCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeComputeQuotaCommandInput,
    DescribeComputeQuotaCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeComputeQuotaCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeComputeQuotaCommandInput,
    DescribeComputeQuotaCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeComputeQuotaCommand extends DescribeComputeQuotaCommand_base {
  protected static __types: {
    api: {
      input: DescribeComputeQuotaRequest;
      output: DescribeComputeQuotaResponse;
    };
    sdk: {
      input: DescribeComputeQuotaCommandInput;
      output: DescribeComputeQuotaCommandOutput;
    };
  };
}
