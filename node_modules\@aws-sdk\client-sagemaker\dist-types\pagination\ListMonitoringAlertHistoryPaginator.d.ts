import { Paginator } from "@smithy/types";
import { ListMonitoringAlertHistoryCommandInput, ListMonitoringAlertHistoryCommandOutput } from "../commands/ListMonitoringAlertHistoryCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListMonitoringAlertHistory: (config: SageMakerPaginationConfiguration, input: ListMonitoringAlertHistoryCommandInput, ...rest: any[]) => Paginator<ListMonitoringAlertHistoryCommandOutput>;
