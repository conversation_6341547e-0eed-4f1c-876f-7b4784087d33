import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  GetSagemakerServicecatalogPortfolioStatusInput,
  GetSagemakerServicecatalogPortfolioStatusOutput,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface GetSagemakerServicecatalogPortfolioStatusCommandInput
  extends GetSagemakerServicecatalogPortfolioStatusInput {}
export interface GetSagemakerServicecatalogPortfolioStatusCommandOutput
  extends GetSagemakerServicecatalogPortfolioStatusOutput,
    __MetadataBearer {}
declare const GetSagemakerServicecatalogPortfolioStatusCommand_base: {
  new (
    input: GetSagemakerServicecatalogPortfolioStatusCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetSagemakerServicecatalogPortfolioStatusCommandInput,
    GetSagemakerServicecatalogPortfolioStatusCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    ...[input]: [] | [GetSagemakerServicecatalogPortfolioStatusCommandInput]
  ): import("@smithy/smithy-client").CommandImpl<
    GetSagemakerServicecatalogPortfolioStatusCommandInput,
    GetSagemakerServicecatalogPortfolioStatusCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetSagemakerServicecatalogPortfolioStatusCommand extends GetSagemakerServicecatalogPortfolioStatusCommand_base {
  protected static __types: {
    api: {
      input: {};
      output: GetSagemakerServicecatalogPortfolioStatusOutput;
    };
    sdk: {
      input: GetSagemakerServicecatalogPortfolioStatusCommandInput;
      output: GetSagemakerServicecatalogPortfolioStatusCommandOutput;
    };
  };
}
