import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  UpdateFeatureGroupRequest,
  UpdateFeatureGroupResponse,
} from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateFeatureGroupCommandInput
  extends UpdateFeatureGroupRequest {}
export interface UpdateFeatureGroupCommandOutput
  extends UpdateFeatureGroupResponse,
    __MetadataBearer {}
declare const UpdateFeatureGroupCommand_base: {
  new (
    input: UpdateFeatureGroupCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateFeatureGroupCommandInput,
    UpdateFeatureGroupCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateFeatureGroupCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateFeatureGroupCommandInput,
    UpdateFeatureGroupCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateFeatureGroupCommand extends UpdateFeatureGroupCommand_base {
  protected static __types: {
    api: {
      input: UpdateFeatureGroupRequest;
      output: UpdateFeatureGroupResponse;
    };
    sdk: {
      input: UpdateFeatureGroupCommandInput;
      output: UpdateFeatureGroupCommandOutput;
    };
  };
}
