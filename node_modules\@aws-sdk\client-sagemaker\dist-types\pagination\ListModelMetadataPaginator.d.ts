import { Paginator } from "@smithy/types";
import { ListModelMetadataCommandInput, ListModelMetadataCommandOutput } from "../commands/ListModelMetadataCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListModelMetadata: (config: SageMakerPaginationConfiguration, input: ListModelMetadataCommandInput, ...rest: any[]) => Paginator<ListModelMetadataCommandOutput>;
