import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeDeviceRequest,
  DescribeDeviceResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeDeviceCommandInput extends DescribeDeviceRequest {}
export interface DescribeDeviceCommandOutput
  extends DescribeDeviceResponse,
    __MetadataBearer {}
declare const DescribeDeviceCommand_base: {
  new (
    input: DescribeDeviceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeDeviceCommandInput,
    DescribeDeviceCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeDeviceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeDeviceCommandInput,
    DescribeDeviceCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeDeviceCommand extends DescribeDeviceCommand_base {
  protected static __types: {
    api: {
      input: DescribeDeviceRequest;
      output: DescribeDeviceResponse;
    };
    sdk: {
      input: DescribeDeviceCommandInput;
      output: DescribeDeviceCommandOutput;
    };
  };
}
