import { Paginator } from "@smithy/types";
import { ListLineageGroupsCommandInput, ListLineageGroupsCommandOutput } from "../commands/ListLineageGroupsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListLineageGroups: (config: SageMakerPaginationConfiguration, input: ListLineageGroupsCommandInput, ...rest: any[]) => Paginator<ListLineageGroupsCommandOutput>;
