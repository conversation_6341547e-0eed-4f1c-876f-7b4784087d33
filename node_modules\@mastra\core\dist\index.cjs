'use strict';

var chunkKSPHQSZX_cjs = require('./chunk-KSPHQSZX.cjs');
var chunkPIZM25KI_cjs = require('./chunk-PIZM25KI.cjs');
var chunkMAFHTHTJ_cjs = require('./chunk-MAFHTHTJ.cjs');
var chunkSFZZYGKB_cjs = require('./chunk-SFZZYGKB.cjs');
var chunkP7BGXOQV_cjs = require('./chunk-P7BGXOQV.cjs');
var chunk3C6V2FEP_cjs = require('./chunk-3C6V2FEP.cjs');
var chunkSKG2NIZW_cjs = require('./chunk-SKG2NIZW.cjs');
var chunkMUNFCOMB_cjs = require('./chunk-MUNFCOMB.cjs');
var chunkXRF2JEZJ_cjs = require('./chunk-XRF2JEZJ.cjs');
var chunkSTLR3SIQ_cjs = require('./chunk-STLR3SIQ.cjs');
var chunkSLKAWVQR_cjs = require('./chunk-SLKAWVQR.cjs');
var chunkST5RMVLG_cjs = require('./chunk-ST5RMVLG.cjs');
var chunkUVRGQEMD_cjs = require('./chunk-UVRGQEMD.cjs');
var chunkU3L3NEOM_cjs = require('./chunk-U3L3NEOM.cjs');
var chunkCWSFP2HS_cjs = require('./chunk-CWSFP2HS.cjs');
var chunkYEULQPUY_cjs = require('./chunk-YEULQPUY.cjs');

// src/agent/index.warning.ts
var Agent2 = class extends chunkSLKAWVQR_cjs.Agent {
  constructor(config) {
    super(config);
    this.logger.warn('Please import "Agent from "@mastra/core/agent" instead of "@mastra/core"');
  }
};

// src/base.warning.ts
var MastraBase2 = class extends chunkYEULQPUY_cjs.MastraBase {
  constructor(args) {
    super(args);
    this.logger.warn('Please import "MastraBase" from "@mastra/core/base" instead of "@mastra/core"');
  }
};

// src/deployer/index.warning.ts
var MastraDeployer2 = class extends chunkCWSFP2HS_cjs.MastraDeployer {
  constructor(args) {
    super(args);
    this.logger.warn('Please import "MastraDeployer" from "@mastra/core/deployer" instead of "@mastra/core"');
  }
};

// src/storage/base.warning.ts
var MastraStorage2 = class extends chunkP7BGXOQV_cjs.MastraStorage {
  constructor({ name }) {
    super({
      name
    });
    this.logger.warn('Please import "MastraStorage" from "@mastra/core/storage" instead of "@mastra/core"');
  }
};

// src/integration/integration.warning.ts
var Integration2 = class extends chunkSKG2NIZW_cjs.Integration {
  constructor() {
    super();
    console.warn('Please import "Integration" from "@mastra/core/integration" instead of "@mastra/core"');
  }
};

// src/integration/openapi-toolset.warning.ts
var OpenAPIToolset2 = class extends chunkSKG2NIZW_cjs.OpenAPIToolset {
  constructor() {
    super();
    console.warn('Please import "OpenAPIToolset" from "@mastra/core/integration" instead of "@mastra/core"');
  }
};

// src/memory/index.warning.ts
var MastraMemory2 = class extends chunkSTLR3SIQ_cjs.MastraMemory {
  constructor(_arg) {
    super({ name: `Deprecated memory` });
    this.logger.warn('Please import "MastraMemory" from "@mastra/core/memory" instead of "@mastra/core"');
  }
};

// src/tools/index.warning.ts
var Tool2 = class extends chunkU3L3NEOM_cjs.Tool {
  constructor(opts) {
    super(opts);
    console.warn('Please import "Tool" from "@mastra/core/tools" instead of "@mastra/core"');
  }
};

// src/tts/index.warning.ts
var MastraTTS2 = class extends chunkKSPHQSZX_cjs.MastraTTS {
  constructor(args) {
    super(args);
    this.logger.warn('Please import "MastraTTS" from "@mastra/core/tts" instead of "@mastra/core"');
  }
};

// src/vector/index.warning.ts
var MastraVector2 = class extends chunkPIZM25KI_cjs.MastraVector {
  constructor() {
    super();
    this.logger.warn('Please import "MastraVector" from "@mastra/core/vector" instead of "@mastra/core"');
  }
};

// src/workflows/workflow.warning.ts
var Workflow2 = class extends chunkMAFHTHTJ_cjs.Workflow {
  constructor(args) {
    super(args);
    this.logger.warn('Please import "Workflow" from "@mastra/core/workflows" instead of "@mastra/core"');
  }
};

Object.defineProperty(exports, "DefaultExecutionEngine", {
  enumerable: true,
  get: function () { return chunkMAFHTHTJ_cjs.DefaultExecutionEngine; }
});
Object.defineProperty(exports, "ExecutionEngine", {
  enumerable: true,
  get: function () { return chunkMAFHTHTJ_cjs.ExecutionEngine; }
});
Object.defineProperty(exports, "Run", {
  enumerable: true,
  get: function () { return chunkMAFHTHTJ_cjs.Run; }
});
Object.defineProperty(exports, "cloneStep", {
  enumerable: true,
  get: function () { return chunkMAFHTHTJ_cjs.cloneStep; }
});
Object.defineProperty(exports, "cloneWorkflow", {
  enumerable: true,
  get: function () { return chunkMAFHTHTJ_cjs.cloneWorkflow; }
});
Object.defineProperty(exports, "createStep", {
  enumerable: true,
  get: function () { return chunkMAFHTHTJ_cjs.createStep; }
});
Object.defineProperty(exports, "createWorkflow", {
  enumerable: true,
  get: function () { return chunkMAFHTHTJ_cjs.createWorkflow; }
});
Object.defineProperty(exports, "CohereRelevanceScorer", {
  enumerable: true,
  get: function () { return chunkSFZZYGKB_cjs.CohereRelevanceScorer; }
});
Object.defineProperty(exports, "MastraAgentRelevanceScorer", {
  enumerable: true,
  get: function () { return chunkSFZZYGKB_cjs.MastraAgentRelevanceScorer; }
});
Object.defineProperty(exports, "createSimilarityPrompt", {
  enumerable: true,
  get: function () { return chunkSFZZYGKB_cjs.createSimilarityPrompt; }
});
Object.defineProperty(exports, "Metric", {
  enumerable: true,
  get: function () { return chunk3C6V2FEP_cjs.Metric; }
});
Object.defineProperty(exports, "evaluate", {
  enumerable: true,
  get: function () { return chunk3C6V2FEP_cjs.evaluate; }
});
Object.defineProperty(exports, "createMockModel", {
  enumerable: true,
  get: function () { return chunkMUNFCOMB_cjs.createMockModel; }
});
Object.defineProperty(exports, "Mastra", {
  enumerable: true,
  get: function () { return chunkXRF2JEZJ_cjs.Mastra; }
});
Object.defineProperty(exports, "MemoryProcessor", {
  enumerable: true,
  get: function () { return chunkSTLR3SIQ_cjs.MemoryProcessor; }
});
Object.defineProperty(exports, "memoryDefaultOptions", {
  enumerable: true,
  get: function () { return chunkSTLR3SIQ_cjs.memoryDefaultOptions; }
});
Object.defineProperty(exports, "AvailableHooks", {
  enumerable: true,
  get: function () { return chunkST5RMVLG_cjs.AvailableHooks; }
});
Object.defineProperty(exports, "executeHook", {
  enumerable: true,
  get: function () { return chunkST5RMVLG_cjs.executeHook; }
});
Object.defineProperty(exports, "registerHook", {
  enumerable: true,
  get: function () { return chunkST5RMVLG_cjs.registerHook; }
});
Object.defineProperty(exports, "InstrumentClass", {
  enumerable: true,
  get: function () { return chunkUVRGQEMD_cjs.InstrumentClass; }
});
Object.defineProperty(exports, "OTLPStorageExporter", {
  enumerable: true,
  get: function () { return chunkUVRGQEMD_cjs.OTLPTraceExporter; }
});
Object.defineProperty(exports, "Telemetry", {
  enumerable: true,
  get: function () { return chunkUVRGQEMD_cjs.Telemetry; }
});
Object.defineProperty(exports, "getBaggageValues", {
  enumerable: true,
  get: function () { return chunkUVRGQEMD_cjs.getBaggageValues; }
});
Object.defineProperty(exports, "hasActiveTelemetry", {
  enumerable: true,
  get: function () { return chunkUVRGQEMD_cjs.hasActiveTelemetry; }
});
Object.defineProperty(exports, "withSpan", {
  enumerable: true,
  get: function () { return chunkUVRGQEMD_cjs.withSpan; }
});
Object.defineProperty(exports, "checkEvalStorageFields", {
  enumerable: true,
  get: function () { return chunkU3L3NEOM_cjs.checkEvalStorageFields; }
});
Object.defineProperty(exports, "createMastraProxy", {
  enumerable: true,
  get: function () { return chunkU3L3NEOM_cjs.createMastraProxy; }
});
Object.defineProperty(exports, "createTool", {
  enumerable: true,
  get: function () { return chunkU3L3NEOM_cjs.createTool; }
});
Object.defineProperty(exports, "deepMerge", {
  enumerable: true,
  get: function () { return chunkU3L3NEOM_cjs.deepMerge; }
});
Object.defineProperty(exports, "delay", {
  enumerable: true,
  get: function () { return chunkU3L3NEOM_cjs.delay; }
});
Object.defineProperty(exports, "ensureAllMessagesAreCoreMessages", {
  enumerable: true,
  get: function () { return chunkU3L3NEOM_cjs.ensureAllMessagesAreCoreMessages; }
});
Object.defineProperty(exports, "ensureToolProperties", {
  enumerable: true,
  get: function () { return chunkU3L3NEOM_cjs.ensureToolProperties; }
});
Object.defineProperty(exports, "isCoreMessage", {
  enumerable: true,
  get: function () { return chunkU3L3NEOM_cjs.isCoreMessage; }
});
Object.defineProperty(exports, "isUiMessage", {
  enumerable: true,
  get: function () { return chunkU3L3NEOM_cjs.isUiMessage; }
});
Object.defineProperty(exports, "isVercelTool", {
  enumerable: true,
  get: function () { return chunkU3L3NEOM_cjs.isVercelTool; }
});
Object.defineProperty(exports, "isZodType", {
  enumerable: true,
  get: function () { return chunkU3L3NEOM_cjs.isZodType; }
});
Object.defineProperty(exports, "makeCoreTool", {
  enumerable: true,
  get: function () { return chunkU3L3NEOM_cjs.makeCoreTool; }
});
Object.defineProperty(exports, "maskStreamTags", {
  enumerable: true,
  get: function () { return chunkU3L3NEOM_cjs.maskStreamTags; }
});
Object.defineProperty(exports, "parseFieldKey", {
  enumerable: true,
  get: function () { return chunkU3L3NEOM_cjs.parseFieldKey; }
});
Object.defineProperty(exports, "parseSqlIdentifier", {
  enumerable: true,
  get: function () { return chunkU3L3NEOM_cjs.parseSqlIdentifier; }
});
Object.defineProperty(exports, "resolveSerializedZodOutput", {
  enumerable: true,
  get: function () { return chunkU3L3NEOM_cjs.resolveSerializedZodOutput; }
});
exports.Agent = Agent2;
exports.Integration = Integration2;
exports.MastraBase = MastraBase2;
exports.MastraDeployer = MastraDeployer2;
exports.MastraMemory = MastraMemory2;
exports.MastraStorage = MastraStorage2;
exports.MastraTTS = MastraTTS2;
exports.MastraVector = MastraVector2;
exports.OpenAPIToolset = OpenAPIToolset2;
exports.Tool = Tool2;
exports.Workflow = Workflow2;
