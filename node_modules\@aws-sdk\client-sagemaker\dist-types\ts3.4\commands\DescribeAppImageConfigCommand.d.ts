import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeAppImageConfigRequest,
  DescribeAppImageConfigResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeAppImageConfigCommandInput
  extends DescribeAppImageConfigRequest {}
export interface DescribeAppImageConfigCommandOutput
  extends DescribeAppImageConfigResponse,
    __MetadataBearer {}
declare const DescribeAppImageConfigCommand_base: {
  new (
    input: DescribeAppImageConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeAppImageConfigCommandInput,
    DescribeAppImageConfigCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeAppImageConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeAppImageConfigCommandInput,
    DescribeAppImageConfigCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeAppImageConfigCommand extends DescribeAppImageConfigCommand_base {
  protected static __types: {
    api: {
      input: DescribeAppImageConfigRequest;
      output: DescribeAppImageConfigResponse;
    };
    sdk: {
      input: DescribeAppImageConfigCommandInput;
      output: DescribeAppImageConfigCommandOutput;
    };
  };
}
