import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateStudioLifecycleConfigRequest,
  CreateStudioLifecycleConfigResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateStudioLifecycleConfigCommandInput
  extends CreateStudioLifecycleConfigRequest {}
export interface CreateStudioLifecycleConfigCommandOutput
  extends CreateStudioLifecycleConfigResponse,
    __MetadataBearer {}
declare const CreateStudioLifecycleConfigCommand_base: {
  new (
    input: CreateStudioLifecycleConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateStudioLifecycleConfigCommandInput,
    CreateStudioLifecycleConfigCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateStudioLifecycleConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateStudioLifecycleConfigCommandInput,
    CreateStudioLifecycleConfigCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateStudioLifecycleConfigCommand extends CreateStudioLifecycleConfigCommand_base {
  protected static __types: {
    api: {
      input: CreateStudioLifecycleConfigRequest;
      output: CreateStudioLifecycleConfigResponse;
    };
    sdk: {
      input: CreateStudioLifecycleConfigCommandInput;
      output: CreateStudioLifecycleConfigCommandOutput;
    };
  };
}
