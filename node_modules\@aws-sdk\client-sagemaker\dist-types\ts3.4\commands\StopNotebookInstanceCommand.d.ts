import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { StopNotebookInstanceInput } from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface StopNotebookInstanceCommandInput
  extends StopNotebookInstanceInput {}
export interface StopNotebookInstanceCommandOutput extends __MetadataBearer {}
declare const StopNotebookInstanceCommand_base: {
  new (
    input: StopNotebookInstanceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StopNotebookInstanceCommandInput,
    StopNotebookInstanceCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: StopNotebookInstanceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StopNotebookInstanceCommandInput,
    StopNotebookInstanceCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class StopNotebookInstanceCommand extends StopNotebookInstanceCommand_base {
  protected static __types: {
    api: {
      input: StopNotebookInstanceInput;
      output: {};
    };
    sdk: {
      input: StopNotebookInstanceCommandInput;
      output: StopNotebookInstanceCommandOutput;
    };
  };
}
