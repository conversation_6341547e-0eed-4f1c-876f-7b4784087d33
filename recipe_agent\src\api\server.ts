import cors from 'cors';
import express from 'express';
import { createServer } from 'http';
import { Server } from 'socket.io';
import { recipeAgent } from '../mastra/agents/recipeAgent';
import { initializeMCP } from '../mastra/init';

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Initialize MCP
initializeMCP().catch(console.error);

// Chat message interface
interface ChatMessage {
  id: string;
  text: string;
  user: {
    _id: string;
    name: string;
    avatar?: string;
  };
  createdAt: Date;
  typing?: boolean;
}

// REST API endpoint for testing
app.post('/api/chat', async (req, res) => {
  try {
    const { message } = req.body;
    
    if (!message) {
      return res.status(400).json({ error: '<PERSON><PERSON> gere<PERSON>' });
    }

    console.log('📨 Gelen mesaj:', message);

    // Recipe agent'ı çağır
    const response = await recipeAgent.generate(message);
    
    console.log('🤖 Bot yanıtı:', response.text);

    res.json({
      success: true,
      response: response.text,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Chat API hatası:', error);
    res.status(500).json({ 
      error: 'Bir hata oluştu',
      details: error.message 
    });
  }
});

// Socket.IO for real-time chat
io.on('connection', (socket) => {
  console.log('👤 Kullanıcı bağlandı:', socket.id);

  // Hoş geldin mesajı gönder
  const welcomeMessage: ChatMessage = {
    id: Date.now().toString(),
    text: '👋 Merhaba! Ben tarif asistanınızım. Hangi malzemeleriniz var? Onlarla neler yapabileceğinizi söyleyeyim! 🍳',
    user: {
      _id: 'recipe-bot',
      name: 'Tarif Asistanı',
      avatar: '🤖'
    },
    createdAt: new Date()
  };

  socket.emit('message', welcomeMessage);

  // Kullanıcı mesajı geldiğinde
  socket.on('user_message', async (data: { message: string, userId: string }) => {
    try {
      console.log('📨 Socket mesajı:', data.message);

      // Typing indicator gönder
      const typingMessage: ChatMessage = {
        id: `typing-${Date.now()}`,
        text: 'Yazıyor...',
        user: {
          _id: 'recipe-bot',
          name: 'Tarif Asistanı',
          avatar: '🤖'
        },
        createdAt: new Date(),
        typing: true
      };

      socket.emit('bot_typing', typingMessage);

      // Recipe agent'ı çağır
      const response = await recipeAgent.generate(data.message);

      // Typing indicator'ı kaldır
      socket.emit('bot_stop_typing');

      // Bot yanıtını gönder
      const botMessage: ChatMessage = {
        id: Date.now().toString(),
        text: response.text,
        user: {
          _id: 'recipe-bot',
          name: 'Tarif Asistanı',
          avatar: '🤖'
        },
        createdAt: new Date()
      };

      socket.emit('bot_message', botMessage);

      console.log('🤖 Bot yanıtı gönderildi:', response.text.substring(0, 100) + '...');

    } catch (error) {
      console.error('❌ Socket chat hatası:', error);
      
      // Hata mesajı gönder
      const errorMessage: ChatMessage = {
        id: Date.now().toString(),
        text: '😅 Üzgünüm, bir hata oluştu. Lütfen tekrar deneyin.',
        user: {
          _id: 'recipe-bot',
          name: 'Tarif Asistanı',
          avatar: '🤖'
        },
        createdAt: new Date()
      };

      socket.emit('bot_message', errorMessage);
    }
  });

  socket.on('disconnect', () => {
    console.log('👋 Kullanıcı ayrıldı:', socket.id);
  });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    message: 'Recipe Chat API Server çalışıyor',
    timestamp: new Date().toISOString()
  });
});

// Start server
server.listen(PORT, () => {
  console.log('🚀 Recipe Chat API Server başlatıldı!');
  console.log(`📡 HTTP API: http://localhost:${PORT}`);
  console.log(`🔌 WebSocket: ws://localhost:${PORT}`);
  console.log('🍳 Tarif chatbot hazır!');
});

export default app;
