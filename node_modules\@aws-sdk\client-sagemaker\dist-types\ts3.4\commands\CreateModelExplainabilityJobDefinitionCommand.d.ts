import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateModelExplainabilityJobDefinitionRequest,
  CreateModelExplainabilityJobDefinitionResponse,
} from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateModelExplainabilityJobDefinitionCommandInput
  extends CreateModelExplainabilityJobDefinitionRequest {}
export interface CreateModelExplainabilityJobDefinitionCommandOutput
  extends CreateModelExplainabilityJobDefinitionResponse,
    __MetadataBearer {}
declare const CreateModelExplainabilityJobDefinitionCommand_base: {
  new (
    input: CreateModelExplainabilityJobDefinitionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateModelExplainabilityJobDefinitionCommandInput,
    CreateModelExplainabilityJobDefinitionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateModelExplainabilityJobDefinitionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateModelExplainabilityJobDefinitionCommandInput,
    CreateModelExplainabilityJobDefinitionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateModelExplainabilityJobDefinitionCommand extends CreateModelExplainabilityJobDefinitionCommand_base {
  protected static __types: {
    api: {
      input: CreateModelExplainabilityJobDefinitionRequest;
      output: CreateModelExplainabilityJobDefinitionResponse;
    };
    sdk: {
      input: CreateModelExplainabilityJobDefinitionCommandInput;
      output: CreateModelExplainabilityJobDefinitionCommandOutput;
    };
  };
}
