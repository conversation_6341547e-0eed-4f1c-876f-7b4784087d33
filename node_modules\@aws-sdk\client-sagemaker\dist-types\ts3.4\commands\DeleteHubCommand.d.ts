import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DeleteHubRequest } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteHubCommandInput extends DeleteHubRequest {}
export interface DeleteHubCommandOutput extends __MetadataBearer {}
declare const DeleteHubCommand_base: {
  new (
    input: DeleteHubCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteHubCommandInput,
    DeleteHubCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteHubCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteHubCommandInput,
    DeleteHubCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteHubCommand extends DeleteHubCommand_base {
  protected static __types: {
    api: {
      input: DeleteHubRequest;
      output: {};
    };
    sdk: {
      input: DeleteHubCommandInput;
      output: DeleteHubCommandOutput;
    };
  };
}
