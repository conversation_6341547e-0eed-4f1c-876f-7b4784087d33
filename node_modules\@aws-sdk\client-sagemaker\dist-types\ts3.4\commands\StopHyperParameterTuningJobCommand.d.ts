import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { StopHyperParameterTuningJobRequest } from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface StopHyperParameterTuningJobCommandInput
  extends StopHyperParameterTuningJobRequest {}
export interface StopHyperParameterTuningJobCommandOutput
  extends __MetadataBearer {}
declare const StopHyperParameterTuningJobCommand_base: {
  new (
    input: StopHyperParameterTuningJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StopHyperParameterTuningJobCommandInput,
    StopHyperParameterTuningJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: StopHyperParameterTuningJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StopHyperParameterTuningJobCommandInput,
    StopHyperParameterTuningJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class StopHyperParameterTuningJobCommand extends StopHyperParameterTuningJobCommand_base {
  protected static __types: {
    api: {
      input: StopHyperParameterTuningJobRequest;
      output: {};
    };
    sdk: {
      input: StopHyperParameterTuningJobCommandInput;
      output: StopHyperParameterTuningJobCommandOutput;
    };
  };
}
