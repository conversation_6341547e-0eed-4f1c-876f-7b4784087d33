import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateMonitoringScheduleRequest,
  CreateMonitoringScheduleResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateMonitoringScheduleCommandInput
  extends CreateMonitoringScheduleRequest {}
export interface CreateMonitoringScheduleCommandOutput
  extends CreateMonitoringScheduleResponse,
    __MetadataBearer {}
declare const CreateMonitoringScheduleCommand_base: {
  new (
    input: CreateMonitoringScheduleCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateMonitoringScheduleCommandInput,
    CreateMonitoringScheduleCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateMonitoringScheduleCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateMonitoringScheduleCommandInput,
    CreateMonitoringScheduleCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateMonitoringScheduleCommand extends CreateMonitoringScheduleCommand_base {
  protected static __types: {
    api: {
      input: CreateMonitoringScheduleRequest;
      output: CreateMonitoringScheduleResponse;
    };
    sdk: {
      input: CreateMonitoringScheduleCommandInput;
      output: CreateMonitoringScheduleCommandOutput;
    };
  };
}
