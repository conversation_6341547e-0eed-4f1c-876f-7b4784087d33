import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DeleteModelCardRequest } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteModelCardCommandInput extends DeleteModelCardRequest {}
export interface DeleteModelCardCommandOutput extends __MetadataBearer {}
declare const DeleteModelCardCommand_base: {
  new (
    input: DeleteModelCardCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteModelCardCommandInput,
    DeleteModelCardCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteModelCardCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteModelCardCommandInput,
    DeleteModelCardCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteModelCardCommand extends DeleteModelCardCommand_base {
  protected static __types: {
    api: {
      input: DeleteModelCardRequest;
      output: {};
    };
    sdk: {
      input: DeleteModelCardCommandInput;
      output: DeleteModelCardCommandOutput;
    };
  };
}
