import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  UpdateTrialComponentRequest,
  UpdateTrialComponentResponse,
} from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateTrialComponentCommandInput
  extends UpdateTrialComponentRequest {}
export interface UpdateTrialComponentCommandOutput
  extends UpdateTrialComponentResponse,
    __MetadataBearer {}
declare const UpdateTrialComponentCommand_base: {
  new (
    input: UpdateTrialComponentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateTrialComponentCommandInput,
    UpdateTrialComponentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateTrialComponentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateTrialComponentCommandInput,
    UpdateTrialComponentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateTrialComponentCommand extends UpdateTrialComponentCommand_base {
  protected static __types: {
    api: {
      input: UpdateTrialComponentRequest;
      output: UpdateTrialComponentResponse;
    };
    sdk: {
      input: UpdateTrialComponentCommandInput;
      output: UpdateTrialComponentCommandOutput;
    };
  };
}
