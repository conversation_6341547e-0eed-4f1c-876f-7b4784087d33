import { Paginator } from "@smithy/types";
import { ListProcessingJobsCommandInput, ListProcessingJobsCommandOutput } from "../commands/ListProcessingJobsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListProcessingJobs: (config: SageMakerPaginationConfiguration, input: ListProcessingJobsCommandInput, ...rest: any[]) => Paginator<ListProcessingJobsCommandOutput>;
