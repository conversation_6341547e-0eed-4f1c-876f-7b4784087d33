import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  BatchDeleteClusterNodesRequest,
  BatchDeleteClusterNodesResponse,
} from "../models/models_0";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface BatchDeleteClusterNodesCommandInput
  extends BatchDeleteClusterNodesRequest {}
export interface BatchDeleteClusterNodesCommandOutput
  extends BatchDeleteClusterNodesResponse,
    __MetadataBearer {}
declare const BatchDeleteClusterNodesCommand_base: {
  new (
    input: BatchDeleteClusterNodesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    BatchDeleteClusterNodesCommandInput,
    BatchDeleteClusterNodesCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: BatchDeleteClusterNodesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    BatchDeleteClusterNodesCommandInput,
    BatchDeleteClusterNodesCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class BatchDeleteClusterNodesCommand extends BatchDeleteClusterNodesCommand_base {
  protected static __types: {
    api: {
      input: BatchDeleteClusterNodesRequest;
      output: BatchDeleteClusterNodesResponse;
    };
    sdk: {
      input: BatchDeleteClusterNodesCommandInput;
      output: BatchDeleteClusterNodesCommandOutput;
    };
  };
}
