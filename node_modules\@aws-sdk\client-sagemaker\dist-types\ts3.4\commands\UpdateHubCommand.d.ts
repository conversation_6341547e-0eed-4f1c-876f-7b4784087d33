import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { UpdateHubRequest, UpdateHubResponse } from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateHubCommandInput extends UpdateHubRequest {}
export interface UpdateHubCommandOutput
  extends UpdateHubResponse,
    __MetadataBearer {}
declare const UpdateHubCommand_base: {
  new (
    input: UpdateHubCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateHubCommandInput,
    UpdateHubCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateHubCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateHubCommandInput,
    UpdateHubCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateHubCommand extends UpdateHubCommand_base {
  protected static __types: {
    api: {
      input: UpdateHubRequest;
      output: UpdateHubResponse;
    };
    sdk: {
      input: UpdateHubCommandInput;
      output: UpdateHubCommandOutput;
    };
  };
}
