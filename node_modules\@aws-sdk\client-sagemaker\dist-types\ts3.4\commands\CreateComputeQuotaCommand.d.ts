import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateComputeQuotaRequest,
  CreateComputeQuotaResponse,
} from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateComputeQuotaCommandInput
  extends CreateComputeQuotaRequest {}
export interface CreateComputeQuotaCommandOutput
  extends CreateComputeQuotaResponse,
    __MetadataBearer {}
declare const CreateComputeQuotaCommand_base: {
  new (
    input: CreateComputeQuotaCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateComputeQuotaCommandInput,
    CreateComputeQuotaCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateComputeQuotaCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateComputeQuotaCommandInput,
    CreateComputeQuotaCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateComputeQuotaCommand extends CreateComputeQuotaCommand_base {
  protected static __types: {
    api: {
      input: CreateComputeQuotaRequest;
      output: CreateComputeQuotaResponse;
    };
    sdk: {
      input: CreateComputeQuotaCommandInput;
      output: CreateComputeQuotaCommandOutput;
    };
  };
}
