import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ListHumanTaskUisRequest,
  ListHumanTaskUisResponse,
} from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface ListHumanTaskUisCommandInput extends ListHumanTaskUisRequest {}
export interface ListHumanTaskUisCommandOutput
  extends ListHumanTaskUisResponse,
    __MetadataBearer {}
declare const ListHumanTaskUisCommand_base: {
  new (
    input: ListHumanTaskUisCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListHumanTaskUisCommandInput,
    ListHumanTaskUisCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    ...[input]: [] | [ListHumanTaskUisCommandInput]
  ): import("@smithy/smithy-client").CommandImpl<
    ListHumanTaskUisCommandInput,
    ListHumanTaskUisCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListHumanTaskUisCommand extends ListHumanTaskUisCommand_base {
  protected static __types: {
    api: {
      input: ListHumanTaskUisRequest;
      output: ListHumanTaskUisResponse;
    };
    sdk: {
      input: ListHumanTaskUisCommandInput;
      output: ListHumanTaskUisCommandOutput;
    };
  };
}
