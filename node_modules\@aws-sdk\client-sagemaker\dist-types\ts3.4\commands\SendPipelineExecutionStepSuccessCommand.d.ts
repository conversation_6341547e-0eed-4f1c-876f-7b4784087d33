import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  SendPipelineExecutionStepSuccessRequest,
  SendPipelineExecutionStepSuccessResponse,
} from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface SendPipelineExecutionStepSuccessCommandInput
  extends SendPipelineExecutionStepSuccessRequest {}
export interface SendPipelineExecutionStepSuccessCommandOutput
  extends SendPipelineExecutionStepSuccessResponse,
    __MetadataBearer {}
declare const SendPipelineExecutionStepSuccessCommand_base: {
  new (
    input: SendPipelineExecutionStepSuccessCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    SendPipelineExecutionStepSuccessCommandInput,
    SendPipelineExecutionStepSuccessCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: SendPipelineExecutionStepSuccessCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    SendPipelineExecutionStepSuccessCommandInput,
    SendPipelineExecutionStepSuccessCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class SendPipelineExecutionStepSuccessCommand extends SendPipelineExecutionStepSuccessCommand_base {
  protected static __types: {
    api: {
      input: SendPipelineExecutionStepSuccessRequest;
      output: SendPipelineExecutionStepSuccessResponse;
    };
    sdk: {
      input: SendPipelineExecutionStepSuccessCommandInput;
      output: SendPipelineExecutionStepSuccessCommandOutput;
    };
  };
}
