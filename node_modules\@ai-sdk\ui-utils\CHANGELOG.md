# @ai-sdk/ui-utils

## 1.2.11

### Patch Changes

- Updated dependencies [d87b9d1]
  - @ai-sdk/provider-utils@2.2.8

## 1.2.10

### Patch Changes

- 6c59ae7: feat (ui/react): support resuming an ongoing stream

## 1.2.9

### Patch Changes

- 62181ef: fix(react-native): support experimental_attachments without FileList global

## 1.2.8

### Patch Changes

- Updated dependencies [beef951]
  - @ai-sdk/provider@1.1.3
  - @ai-sdk/provider-utils@2.2.7

## 1.2.7

### Patch Changes

- Updated dependencies [013faa8]
  - @ai-sdk/provider@1.1.2
  - @ai-sdk/provider-utils@2.2.6

## 1.2.6

### Patch Changes

- Updated dependencies [c21fa6d]
  - @ai-sdk/provider-utils@2.2.5
  - @ai-sdk/provider@1.1.1

## 1.2.5

### Patch Changes

- Updated dependencies [2c19b9a]
  - @ai-sdk/provider-utils@2.2.4

## 1.2.4

### Patch Changes

- Updated dependencies [28be004]
  - @ai-sdk/provider-utils@2.2.3

## 1.2.3

### Patch Changes

- Updated dependencies [b01120e]
  - @ai-sdk/provider-utils@2.2.2

## 1.2.2

### Patch Changes

- 65243ce: fix (ui): introduce step start parts

## 1.2.1

### Patch Changes

- Updated dependencies [f10f0fa]
  - @ai-sdk/provider-utils@2.2.1

## 1.2.0

### Minor Changes

- 5bc638d: AI SDK 4.2

### Patch Changes

- Updated dependencies [5bc638d]
  - @ai-sdk/provider@1.1.0
  - @ai-sdk/provider-utils@2.2.0

## 1.1.21

### Patch Changes

- Updated dependencies [d0c4659]
  - @ai-sdk/provider-utils@2.1.15

## 1.1.20

### Patch Changes

- Updated dependencies [0bd5bc6]
  - @ai-sdk/provider@1.0.12
  - @ai-sdk/provider-utils@2.1.14

## 1.1.19

### Patch Changes

- Updated dependencies [2e1101a]
  - @ai-sdk/provider@1.0.11
  - @ai-sdk/provider-utils@2.1.13

## 1.1.18

### Patch Changes

- Updated dependencies [1531959]
  - @ai-sdk/provider-utils@2.1.12

## 1.1.17

### Patch Changes

- Updated dependencies [e1d3d42]
  - @ai-sdk/provider@1.0.10
  - @ai-sdk/provider-utils@2.1.11

## 1.1.16

### Patch Changes

- ddf9740: feat (ai): add anthropic reasoning
- Updated dependencies [ddf9740]
  - @ai-sdk/provider@1.0.9
  - @ai-sdk/provider-utils@2.1.10

## 1.1.15

### Patch Changes

- Updated dependencies [2761f06]
  - @ai-sdk/provider@1.0.8
  - @ai-sdk/provider-utils@2.1.9

## 1.1.14

### Patch Changes

- Updated dependencies [2e898b4]
  - @ai-sdk/provider-utils@2.1.8

## 1.1.13

### Patch Changes

- Updated dependencies [3ff4ef8]
  - @ai-sdk/provider-utils@2.1.7

## 1.1.12

### Patch Changes

- 166e09e: feat (ai/ui): forward source parts to useChat

## 1.1.11

### Patch Changes

- 318b351: fix (ui): update ui before automatic client-side tool call is executed

## 1.1.10

### Patch Changes

- bcc61d4: feat (ui): introduce message parts for useChat

## 1.1.9

### Patch Changes

- 6b8cc14: feat (ai/core): support recursive zod schemas

## 1.1.8

### Patch Changes

- Updated dependencies [d89c3b9]
  - @ai-sdk/provider@1.0.7
  - @ai-sdk/provider-utils@2.1.6

## 1.1.7

### Patch Changes

- 0d2d9bf: fix (ui): single assistant message with multiple tool steps

## 1.1.6

### Patch Changes

- 3a602ca: chore (core): rename CoreTool to Tool
- Updated dependencies [3a602ca]
  - @ai-sdk/provider-utils@2.1.5

## 1.1.5

### Patch Changes

- Updated dependencies [066206e]
  - @ai-sdk/provider-utils@2.1.4

## 1.1.4

### Patch Changes

- Updated dependencies [39e5c1f]
  - @ai-sdk/provider-utils@2.1.3

## 1.1.3

### Patch Changes

- 9ce598c: feat (ai/ui): add reasoning support to useChat

## 1.1.2

### Patch Changes

- Updated dependencies [ed012d2]
- Updated dependencies [3a58a2e]
  - @ai-sdk/provider-utils@2.1.2
  - @ai-sdk/provider@1.0.6

## 1.1.1

### Patch Changes

- e7a9ec9: feat (provider-utils): include raw value in json parse results
- 0a699f1: feat: add reasoning token support
- Updated dependencies [e7a9ec9]
- Updated dependencies [0a699f1]
  - @ai-sdk/provider-utils@2.1.1
  - @ai-sdk/provider@1.0.5

## 1.1.0

### Minor Changes

- 62ba5ad: release: AI SDK 4.1

### Patch Changes

- Updated dependencies [62ba5ad]
  - @ai-sdk/provider-utils@2.1.0

## 1.0.12

### Patch Changes

- 33592d2: fix (ai/core): switch to json schema 7 target for zod to json schema conversion

## 1.0.11

### Patch Changes

- 00114c5: feat (ui): generate and forward message ids for response messages
- Updated dependencies [00114c5]
  - @ai-sdk/provider-utils@2.0.8

## 1.0.10

### Patch Changes

- 37f4510: feat (ui): expose useChat id and send it to the server

## 1.0.9

### Patch Changes

- 2495973: feat (ai/core): use openai compatible mode for json schema conversion
- 2495973: fix (ai/core): duplicate instead of using reference in json schema

## 1.0.8

### Patch Changes

- Updated dependencies [90fb95a]
- Updated dependencies [e6dfef4]
- Updated dependencies [6636db6]
  - @ai-sdk/provider-utils@2.0.7

## 1.0.7

### Patch Changes

- Updated dependencies [19a2ce7]
- Updated dependencies [19a2ce7]
- Updated dependencies [6337688]
  - @ai-sdk/provider@1.0.4
  - @ai-sdk/provider-utils@2.0.6

## 1.0.6

### Patch Changes

- Updated dependencies [5ed5e45]
  - @ai-sdk/provider-utils@2.0.5
  - @ai-sdk/provider@1.0.3

## 1.0.5

### Patch Changes

- Updated dependencies [09a9cab]
  - @ai-sdk/provider@1.0.2
  - @ai-sdk/provider-utils@2.0.4

## 1.0.4

### Patch Changes

- Updated dependencies [0984f0b]
  - @ai-sdk/provider-utils@2.0.3

## 1.0.3

### Patch Changes

- 953469c: chore (ui): extract prepareAttachmentsForRequest
- a3dd2ed: fix (ui): preserve createdAt as Date object

## 1.0.2

### Patch Changes

- 88b364b: fix (ui-utils): deep clone messages
- Updated dependencies [b446ae5]
  - @ai-sdk/provider@1.0.1
  - @ai-sdk/provider-utils@2.0.2

## 1.0.1

### Patch Changes

- Updated dependencies [c3ab5de]
  - @ai-sdk/provider-utils@2.0.1

## 1.0.0

### Major Changes

- 8bf5756: chore: remove legacy function/tool calling
- 7814c4b: chore (ui): remove streamMode setting from useChat & useCompletion
- fe4f109: chore (ui): set default value of useChat keepLastMessageOnError to true
- 7e89ccb: chore: remove nanoid export

### Patch Changes

- 9f81e66: chore (ui-utils): remove unnecessary dependencies
- 70f28f6: chore (ui-utils): mark vitest as external to reduce bundle size of /test
- 04d3747: chore (ui-utils): restructure processAssistantMessage
- b053413: chore (ui): refactorings & README update
- Updated dependencies [b469a7e]
- Updated dependencies [dce4158]
- Updated dependencies [c0ddc24]
- Updated dependencies [b1da952]
- Updated dependencies [dce4158]
- Updated dependencies [8426f55]
- Updated dependencies [db46ce5]
  - @ai-sdk/provider-utils@2.0.0
  - @ai-sdk/provider@1.0.0

## 1.0.0-canary.9

### Patch Changes

- 04d3747: chore (ui-utils): restructure processAssistantMessage

## 1.0.0-canary.8

### Patch Changes

- b053413: chore (ui): refactorings & README update

## 1.0.0-canary.7

### Major Changes

- fe4f109: chore (ui): set default value of useChat keepLastMessageOnError to true

## 1.0.0-canary.6

### Patch Changes

- 70f28f6: chore (ui-utils): mark vitest as external to reduce bundle size of /test

## 1.0.0-canary.5

### Patch Changes

- 9f81e66: chore (ui-utils): remove unnecessary dependencies
- Updated dependencies [8426f55]
  - @ai-sdk/provider-utils@2.0.0-canary.3

## 1.0.0-canary.4

### Patch Changes

- Updated dependencies [dce4158]
- Updated dependencies [dce4158]
  - @ai-sdk/provider-utils@2.0.0-canary.2

## 1.0.0-canary.3

### Patch Changes

- Updated dependencies [b1da952]
  - @ai-sdk/provider-utils@2.0.0-canary.1

## 1.0.0-canary.2

### Major Changes

- 7814c4b: chore (ui): remove streamMode setting from useChat & useCompletion

### Patch Changes

- Updated dependencies [b469a7e]
- Updated dependencies [c0ddc24]
- Updated dependencies [db46ce5]
  - @ai-sdk/provider-utils@2.0.0-canary.0
  - @ai-sdk/provider@1.0.0-canary.0

## 1.0.0-canary.1

### Major Changes

- 8bf5756: chore: remove legacy function/tool calling

## 1.0.0-canary.0

### Major Changes

- 7e89ccb: chore: remove nanoid export

## 0.0.50

### Patch Changes

- a85c965: fix (ai/ui): send message annotations from onChunk

## 0.0.49

### Patch Changes

- 3bf8da0: fix (ai/ui): update latest message with stream data message annotations until new message starts

## 0.0.48

### Patch Changes

- aa98cdb: chore: more flexible dependency versioning
- 811a317: feat (ai/core): multi-part tool results (incl. images)
- Updated dependencies [aa98cdb]
- Updated dependencies [1486128]
- Updated dependencies [7b937c5]
- Updated dependencies [3b1b69a]
- Updated dependencies [811a317]
  - @ai-sdk/provider-utils@1.0.22
  - @ai-sdk/provider@0.0.26

## 0.0.47

### Patch Changes

- Updated dependencies [b9b0d7b]
  - @ai-sdk/provider@0.0.25
  - @ai-sdk/provider-utils@1.0.21

## 0.0.46

### Patch Changes

- Updated dependencies [d595d0d]
  - @ai-sdk/provider@0.0.24
  - @ai-sdk/provider-utils@1.0.20

## 0.0.45

### Patch Changes

- cd77c5d: feat (ai/core): add isContinued to steps

## 0.0.44

### Patch Changes

- Updated dependencies [273f696]
  - @ai-sdk/provider-utils@1.0.19

## 0.0.43

### Patch Changes

- 1f590ef: chore (ai): rename roundtrips to steps

## 0.0.42

### Patch Changes

- 14210d5: feat (ai/core): add sendUsage information to streamText data stream methods

## 0.0.41

### Patch Changes

- Updated dependencies [03313cd]
- Updated dependencies [3be7c1c]
  - @ai-sdk/provider-utils@1.0.18
  - @ai-sdk/provider@0.0.23

## 0.0.40

### Patch Changes

- aa2dc58: feat (ai/core): add maxToolRoundtrips to streamText

## 0.0.39

### Patch Changes

- Updated dependencies [26515cb]
  - @ai-sdk/provider@0.0.22
  - @ai-sdk/provider-utils@1.0.17

## 0.0.38

### Patch Changes

- d151349: feat (ai/core): array output for generateObject / streamObject

## 0.0.37

### Patch Changes

- Updated dependencies [09f895f]
  - @ai-sdk/provider-utils@1.0.16

## 0.0.36

### Patch Changes

- b5a82b7: chore (ai): update zod-to-json-schema to 3.23.2

## 0.0.35

### Patch Changes

- Updated dependencies [d67fa9c]
  - @ai-sdk/provider-utils@1.0.15

## 0.0.34

### Patch Changes

- Updated dependencies [f2c025e]
  - @ai-sdk/provider@0.0.21
  - @ai-sdk/provider-utils@1.0.14

## 0.0.33

### Patch Changes

- Updated dependencies [6ac355e]
  - @ai-sdk/provider@0.0.20
  - @ai-sdk/provider-utils@1.0.13

## 0.0.32

### Patch Changes

- dd712ac: fix: use FetchFunction type to prevent self-reference
- Updated dependencies [dd712ac]
  - @ai-sdk/provider-utils@1.0.12

## 0.0.31

### Patch Changes

- Updated dependencies [dd4a0f5]
  - @ai-sdk/provider@0.0.19
  - @ai-sdk/provider-utils@1.0.11

## 0.0.30

### Patch Changes

- e9c891d: feat (ai/react): useObject supports non-Zod schemas
- Updated dependencies [4bd27a9]
- Updated dependencies [845754b]
  - @ai-sdk/provider-utils@1.0.10
  - @ai-sdk/provider@0.0.18

## 0.0.29

### Patch Changes

- e5b58f3: fix (ai/ui): forward streaming errors in useChat

## 0.0.28

### Patch Changes

- Updated dependencies [029af4c]
  - @ai-sdk/provider@0.0.17
  - @ai-sdk/provider-utils@1.0.9

## 0.0.27

### Patch Changes

- Updated dependencies [d58517b]
  - @ai-sdk/provider@0.0.16
  - @ai-sdk/provider-utils@1.0.8

## 0.0.26

### Patch Changes

- Updated dependencies [96aed25]
  - @ai-sdk/provider@0.0.15
  - @ai-sdk/provider-utils@1.0.7

## 0.0.25

### Patch Changes

- Updated dependencies [9614584]
- Updated dependencies [0762a22]
  - @ai-sdk/provider-utils@1.0.6

## 0.0.24

### Patch Changes

- 5be25124: fix (ai/ui): useChat messages have stable ids with streamProtocol: "text"

## 0.0.23

### Patch Changes

- fea7b604: chore (ai/ui): remove "args" and "toolName" from tool result stream part.

## 0.0.22

### Patch Changes

- 1d93d716: fix (ai/ui): parse null as NaN in finish message stream parts

## 0.0.21

### Patch Changes

- c450fcf7: feat (ui): invoke useChat onFinish with finishReason and tokens
- e4a1719f: chore (ai/ui): rename streamMode to streamProtocol

## 0.0.20

### Patch Changes

- Updated dependencies [a8d1c9e9]
  - @ai-sdk/provider-utils@1.0.5

## 0.0.19

### Patch Changes

- Updated dependencies [4f88248f]
  - @ai-sdk/provider-utils@1.0.4

## 0.0.18

### Patch Changes

- @ai-sdk/provider-utils@1.0.3

## 0.0.17

### Patch Changes

- f63829fe: feat (ai/ui): add allowEmptySubmit flag to handleSubmit

## 0.0.16

### Patch Changes

- 5b7b3bbe: fix (ai/ui): tool call streaming

## 0.0.15

### Patch Changes

- 1f67fe49: feat (ai/ui): stream tool calls with streamText and useChat

## 0.0.14

### Patch Changes

- 99ddbb74: feat (ai/react): add experimental support for managing attachments to useChat

## 0.0.13

### Patch Changes

- a6cb2c8b: feat (ai/ui): add keepLastMessageOnError option to useChat

## 0.0.12

### Patch Changes

- 56bbc2a7: feat (ai/ui): set body and headers directly on options for handleSubmit and append

## 0.0.11

### Patch Changes

- @ai-sdk/provider-utils@1.0.2

## 0.0.10

### Patch Changes

- Updated dependencies [d481729f]
  - @ai-sdk/provider-utils@1.0.1

## 0.0.9

### Patch Changes

- 1894f811: feat (ai/ui): allow JSONValue as data in useChat handleSubmit

## 0.0.8

### Patch Changes

- d3100b9c: feat (ai/ui): support custom fetch function in useChat, useCompletion, useAssistant, useObject

## 0.0.7

### Patch Changes

- Updated dependencies [5edc6110]
- Updated dependencies [5edc6110]
  - @ai-sdk/provider-utils@1.0.0

## 0.0.6

### Patch Changes

- 54bf4083: feat (ai/react): control request body in useChat

## 0.0.5

### Patch Changes

- Updated dependencies [02f6a088]
  - @ai-sdk/provider-utils@0.0.16

## 0.0.4

### Patch Changes

- 008725ec: chore (@ai-sdk/ui-utils): move functions

## 0.0.3

### Patch Changes

- Updated dependencies [85712895]
- Updated dependencies [85712895]
  - @ai-sdk/provider-utils@0.0.15

## 0.0.2

### Patch Changes

- Updated dependencies [7910ae84]
  - @ai-sdk/provider-utils@0.0.14

## 0.0.1

### Patch Changes

- 85f209a4: chore: extracted ui library support into separate modules
