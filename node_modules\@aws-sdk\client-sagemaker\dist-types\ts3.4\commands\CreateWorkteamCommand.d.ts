import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateWorkteamRequest,
  CreateWorkteamResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateWorkteamCommandInput extends CreateWorkteamRequest {}
export interface CreateWorkteamCommandOutput
  extends CreateWorkteamResponse,
    __MetadataBearer {}
declare const CreateWorkteamCommand_base: {
  new (
    input: CreateWorkteamCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateWorkteamCommandInput,
    CreateWorkteamCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateWorkteamCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateWorkteamCommandInput,
    CreateWorkteamCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateWorkteamCommand extends CreateWorkteamCommand_base {
  protected static __types: {
    api: {
      input: CreateWorkteamRequest;
      output: CreateWorkteamResponse;
    };
    sdk: {
      input: CreateWorkteamCommandInput;
      output: CreateWorkteamCommandOutput;
    };
  };
}
