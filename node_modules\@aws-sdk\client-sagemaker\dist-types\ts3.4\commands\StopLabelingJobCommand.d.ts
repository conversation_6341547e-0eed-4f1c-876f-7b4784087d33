import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { StopLabelingJobRequest } from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface StopLabelingJobCommandInput extends StopLabelingJobRequest {}
export interface StopLabelingJobCommandOutput extends __MetadataBearer {}
declare const StopLabelingJobCommand_base: {
  new (
    input: StopLabelingJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StopLabelingJobCommandInput,
    StopLabelingJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: StopLabelingJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StopLabelingJobCommandInput,
    StopLabelingJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class StopLabelingJobCommand extends StopLabelingJobCommand_base {
  protected static __types: {
    api: {
      input: StopLabelingJobRequest;
      output: {};
    };
    sdk: {
      input: StopLabelingJobCommandInput;
      output: StopLabelingJobCommandOutput;
    };
  };
}
