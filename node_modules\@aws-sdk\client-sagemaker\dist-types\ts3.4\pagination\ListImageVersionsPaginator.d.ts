import { Paginator } from "@smithy/types";
import {
  ListImageVersionsCommandInput,
  ListImageVersionsCommandOutput,
} from "../commands/ListImageVersionsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
export declare const paginateListImageVersions: (
  config: SageMakerPaginationConfiguration,
  input: ListImageVersionsCommandInput,
  ...rest: any[]
) => Paginator<ListImageVersionsCommandOutput>;
