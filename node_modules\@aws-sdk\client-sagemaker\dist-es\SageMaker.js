import { createAggregatedClient } from "@smithy/smithy-client";
import { AddAssociationCommand, } from "./commands/AddAssociationCommand";
import { AddTagsCommand } from "./commands/AddTagsCommand";
import { AssociateTrialComponentCommand, } from "./commands/AssociateTrialComponentCommand";
import { BatchDeleteClusterNodesCommand, } from "./commands/BatchDeleteClusterNodesCommand";
import { BatchDescribeModelPackageCommand, } from "./commands/BatchDescribeModelPackageCommand";
import { CreateActionCommand, } from "./commands/CreateActionCommand";
import { CreateAlgorithmCommand, } from "./commands/CreateAlgorithmCommand";
import { CreateAppCommand } from "./commands/CreateAppCommand";
import { CreateAppImageConfigCommand, } from "./commands/CreateAppImageConfigCommand";
import { CreateArtifactCommand, } from "./commands/CreateArtifactCommand";
import { CreateAutoMLJobCommand, } from "./commands/CreateAutoMLJobCommand";
import { CreateAutoMLJobV2Command, } from "./commands/CreateAutoMLJobV2Command";
import { CreateClusterCommand, } from "./commands/CreateClusterCommand";
import { CreateClusterSchedulerConfigCommand, } from "./commands/CreateClusterSchedulerConfigCommand";
import { CreateCodeRepositoryCommand, } from "./commands/CreateCodeRepositoryCommand";
import { CreateCompilationJobCommand, } from "./commands/CreateCompilationJobCommand";
import { CreateComputeQuotaCommand, } from "./commands/CreateComputeQuotaCommand";
import { CreateContextCommand, } from "./commands/CreateContextCommand";
import { CreateDataQualityJobDefinitionCommand, } from "./commands/CreateDataQualityJobDefinitionCommand";
import { CreateDeviceFleetCommand, } from "./commands/CreateDeviceFleetCommand";
import { CreateDomainCommand, } from "./commands/CreateDomainCommand";
import { CreateEdgeDeploymentPlanCommand, } from "./commands/CreateEdgeDeploymentPlanCommand";
import { CreateEdgeDeploymentStageCommand, } from "./commands/CreateEdgeDeploymentStageCommand";
import { CreateEdgePackagingJobCommand, } from "./commands/CreateEdgePackagingJobCommand";
import { CreateEndpointCommand, } from "./commands/CreateEndpointCommand";
import { CreateEndpointConfigCommand, } from "./commands/CreateEndpointConfigCommand";
import { CreateExperimentCommand, } from "./commands/CreateExperimentCommand";
import { CreateFeatureGroupCommand, } from "./commands/CreateFeatureGroupCommand";
import { CreateFlowDefinitionCommand, } from "./commands/CreateFlowDefinitionCommand";
import { CreateHubCommand } from "./commands/CreateHubCommand";
import { CreateHubContentReferenceCommand, } from "./commands/CreateHubContentReferenceCommand";
import { CreateHumanTaskUiCommand, } from "./commands/CreateHumanTaskUiCommand";
import { CreateHyperParameterTuningJobCommand, } from "./commands/CreateHyperParameterTuningJobCommand";
import { CreateImageCommand } from "./commands/CreateImageCommand";
import { CreateImageVersionCommand, } from "./commands/CreateImageVersionCommand";
import { CreateInferenceComponentCommand, } from "./commands/CreateInferenceComponentCommand";
import { CreateInferenceExperimentCommand, } from "./commands/CreateInferenceExperimentCommand";
import { CreateInferenceRecommendationsJobCommand, } from "./commands/CreateInferenceRecommendationsJobCommand";
import { CreateLabelingJobCommand, } from "./commands/CreateLabelingJobCommand";
import { CreateMlflowTrackingServerCommand, } from "./commands/CreateMlflowTrackingServerCommand";
import { CreateModelBiasJobDefinitionCommand, } from "./commands/CreateModelBiasJobDefinitionCommand";
import { CreateModelCardCommand, } from "./commands/CreateModelCardCommand";
import { CreateModelCardExportJobCommand, } from "./commands/CreateModelCardExportJobCommand";
import { CreateModelCommand } from "./commands/CreateModelCommand";
import { CreateModelExplainabilityJobDefinitionCommand, } from "./commands/CreateModelExplainabilityJobDefinitionCommand";
import { CreateModelPackageCommand, } from "./commands/CreateModelPackageCommand";
import { CreateModelPackageGroupCommand, } from "./commands/CreateModelPackageGroupCommand";
import { CreateModelQualityJobDefinitionCommand, } from "./commands/CreateModelQualityJobDefinitionCommand";
import { CreateMonitoringScheduleCommand, } from "./commands/CreateMonitoringScheduleCommand";
import { CreateNotebookInstanceCommand, } from "./commands/CreateNotebookInstanceCommand";
import { CreateNotebookInstanceLifecycleConfigCommand, } from "./commands/CreateNotebookInstanceLifecycleConfigCommand";
import { CreateOptimizationJobCommand, } from "./commands/CreateOptimizationJobCommand";
import { CreatePartnerAppCommand, } from "./commands/CreatePartnerAppCommand";
import { CreatePartnerAppPresignedUrlCommand, } from "./commands/CreatePartnerAppPresignedUrlCommand";
import { CreatePipelineCommand, } from "./commands/CreatePipelineCommand";
import { CreatePresignedDomainUrlCommand, } from "./commands/CreatePresignedDomainUrlCommand";
import { CreatePresignedMlflowTrackingServerUrlCommand, } from "./commands/CreatePresignedMlflowTrackingServerUrlCommand";
import { CreatePresignedNotebookInstanceUrlCommand, } from "./commands/CreatePresignedNotebookInstanceUrlCommand";
import { CreateProcessingJobCommand, } from "./commands/CreateProcessingJobCommand";
import { CreateProjectCommand, } from "./commands/CreateProjectCommand";
import { CreateSpaceCommand } from "./commands/CreateSpaceCommand";
import { CreateStudioLifecycleConfigCommand, } from "./commands/CreateStudioLifecycleConfigCommand";
import { CreateTrainingJobCommand, } from "./commands/CreateTrainingJobCommand";
import { CreateTrainingPlanCommand, } from "./commands/CreateTrainingPlanCommand";
import { CreateTransformJobCommand, } from "./commands/CreateTransformJobCommand";
import { CreateTrialCommand } from "./commands/CreateTrialCommand";
import { CreateTrialComponentCommand, } from "./commands/CreateTrialComponentCommand";
import { CreateUserProfileCommand, } from "./commands/CreateUserProfileCommand";
import { CreateWorkforceCommand, } from "./commands/CreateWorkforceCommand";
import { CreateWorkteamCommand, } from "./commands/CreateWorkteamCommand";
import { DeleteActionCommand, } from "./commands/DeleteActionCommand";
import { DeleteAlgorithmCommand, } from "./commands/DeleteAlgorithmCommand";
import { DeleteAppCommand } from "./commands/DeleteAppCommand";
import { DeleteAppImageConfigCommand, } from "./commands/DeleteAppImageConfigCommand";
import { DeleteArtifactCommand, } from "./commands/DeleteArtifactCommand";
import { DeleteAssociationCommand, } from "./commands/DeleteAssociationCommand";
import { DeleteClusterCommand, } from "./commands/DeleteClusterCommand";
import { DeleteClusterSchedulerConfigCommand, } from "./commands/DeleteClusterSchedulerConfigCommand";
import { DeleteCodeRepositoryCommand, } from "./commands/DeleteCodeRepositoryCommand";
import { DeleteCompilationJobCommand, } from "./commands/DeleteCompilationJobCommand";
import { DeleteComputeQuotaCommand, } from "./commands/DeleteComputeQuotaCommand";
import { DeleteContextCommand, } from "./commands/DeleteContextCommand";
import { DeleteDataQualityJobDefinitionCommand, } from "./commands/DeleteDataQualityJobDefinitionCommand";
import { DeleteDeviceFleetCommand, } from "./commands/DeleteDeviceFleetCommand";
import { DeleteDomainCommand, } from "./commands/DeleteDomainCommand";
import { DeleteEdgeDeploymentPlanCommand, } from "./commands/DeleteEdgeDeploymentPlanCommand";
import { DeleteEdgeDeploymentStageCommand, } from "./commands/DeleteEdgeDeploymentStageCommand";
import { DeleteEndpointCommand, } from "./commands/DeleteEndpointCommand";
import { DeleteEndpointConfigCommand, } from "./commands/DeleteEndpointConfigCommand";
import { DeleteExperimentCommand, } from "./commands/DeleteExperimentCommand";
import { DeleteFeatureGroupCommand, } from "./commands/DeleteFeatureGroupCommand";
import { DeleteFlowDefinitionCommand, } from "./commands/DeleteFlowDefinitionCommand";
import { DeleteHubCommand } from "./commands/DeleteHubCommand";
import { DeleteHubContentCommand, } from "./commands/DeleteHubContentCommand";
import { DeleteHubContentReferenceCommand, } from "./commands/DeleteHubContentReferenceCommand";
import { DeleteHumanTaskUiCommand, } from "./commands/DeleteHumanTaskUiCommand";
import { DeleteHyperParameterTuningJobCommand, } from "./commands/DeleteHyperParameterTuningJobCommand";
import { DeleteImageCommand } from "./commands/DeleteImageCommand";
import { DeleteImageVersionCommand, } from "./commands/DeleteImageVersionCommand";
import { DeleteInferenceComponentCommand, } from "./commands/DeleteInferenceComponentCommand";
import { DeleteInferenceExperimentCommand, } from "./commands/DeleteInferenceExperimentCommand";
import { DeleteMlflowTrackingServerCommand, } from "./commands/DeleteMlflowTrackingServerCommand";
import { DeleteModelBiasJobDefinitionCommand, } from "./commands/DeleteModelBiasJobDefinitionCommand";
import { DeleteModelCardCommand, } from "./commands/DeleteModelCardCommand";
import { DeleteModelCommand } from "./commands/DeleteModelCommand";
import { DeleteModelExplainabilityJobDefinitionCommand, } from "./commands/DeleteModelExplainabilityJobDefinitionCommand";
import { DeleteModelPackageCommand, } from "./commands/DeleteModelPackageCommand";
import { DeleteModelPackageGroupCommand, } from "./commands/DeleteModelPackageGroupCommand";
import { DeleteModelPackageGroupPolicyCommand, } from "./commands/DeleteModelPackageGroupPolicyCommand";
import { DeleteModelQualityJobDefinitionCommand, } from "./commands/DeleteModelQualityJobDefinitionCommand";
import { DeleteMonitoringScheduleCommand, } from "./commands/DeleteMonitoringScheduleCommand";
import { DeleteNotebookInstanceCommand, } from "./commands/DeleteNotebookInstanceCommand";
import { DeleteNotebookInstanceLifecycleConfigCommand, } from "./commands/DeleteNotebookInstanceLifecycleConfigCommand";
import { DeleteOptimizationJobCommand, } from "./commands/DeleteOptimizationJobCommand";
import { DeletePartnerAppCommand, } from "./commands/DeletePartnerAppCommand";
import { DeletePipelineCommand, } from "./commands/DeletePipelineCommand";
import { DeleteProjectCommand, } from "./commands/DeleteProjectCommand";
import { DeleteSpaceCommand } from "./commands/DeleteSpaceCommand";
import { DeleteStudioLifecycleConfigCommand, } from "./commands/DeleteStudioLifecycleConfigCommand";
import { DeleteTagsCommand } from "./commands/DeleteTagsCommand";
import { DeleteTrialCommand } from "./commands/DeleteTrialCommand";
import { DeleteTrialComponentCommand, } from "./commands/DeleteTrialComponentCommand";
import { DeleteUserProfileCommand, } from "./commands/DeleteUserProfileCommand";
import { DeleteWorkforceCommand, } from "./commands/DeleteWorkforceCommand";
import { DeleteWorkteamCommand, } from "./commands/DeleteWorkteamCommand";
import { DeregisterDevicesCommand, } from "./commands/DeregisterDevicesCommand";
import { DescribeActionCommand, } from "./commands/DescribeActionCommand";
import { DescribeAlgorithmCommand, } from "./commands/DescribeAlgorithmCommand";
import { DescribeAppCommand } from "./commands/DescribeAppCommand";
import { DescribeAppImageConfigCommand, } from "./commands/DescribeAppImageConfigCommand";
import { DescribeArtifactCommand, } from "./commands/DescribeArtifactCommand";
import { DescribeAutoMLJobCommand, } from "./commands/DescribeAutoMLJobCommand";
import { DescribeAutoMLJobV2Command, } from "./commands/DescribeAutoMLJobV2Command";
import { DescribeClusterCommand, } from "./commands/DescribeClusterCommand";
import { DescribeClusterNodeCommand, } from "./commands/DescribeClusterNodeCommand";
import { DescribeClusterSchedulerConfigCommand, } from "./commands/DescribeClusterSchedulerConfigCommand";
import { DescribeCodeRepositoryCommand, } from "./commands/DescribeCodeRepositoryCommand";
import { DescribeCompilationJobCommand, } from "./commands/DescribeCompilationJobCommand";
import { DescribeComputeQuotaCommand, } from "./commands/DescribeComputeQuotaCommand";
import { DescribeContextCommand, } from "./commands/DescribeContextCommand";
import { DescribeDataQualityJobDefinitionCommand, } from "./commands/DescribeDataQualityJobDefinitionCommand";
import { DescribeDeviceCommand, } from "./commands/DescribeDeviceCommand";
import { DescribeDeviceFleetCommand, } from "./commands/DescribeDeviceFleetCommand";
import { DescribeDomainCommand, } from "./commands/DescribeDomainCommand";
import { DescribeEdgeDeploymentPlanCommand, } from "./commands/DescribeEdgeDeploymentPlanCommand";
import { DescribeEdgePackagingJobCommand, } from "./commands/DescribeEdgePackagingJobCommand";
import { DescribeEndpointCommand, } from "./commands/DescribeEndpointCommand";
import { DescribeEndpointConfigCommand, } from "./commands/DescribeEndpointConfigCommand";
import { DescribeExperimentCommand, } from "./commands/DescribeExperimentCommand";
import { DescribeFeatureGroupCommand, } from "./commands/DescribeFeatureGroupCommand";
import { DescribeFeatureMetadataCommand, } from "./commands/DescribeFeatureMetadataCommand";
import { DescribeFlowDefinitionCommand, } from "./commands/DescribeFlowDefinitionCommand";
import { DescribeHubCommand } from "./commands/DescribeHubCommand";
import { DescribeHubContentCommand, } from "./commands/DescribeHubContentCommand";
import { DescribeHumanTaskUiCommand, } from "./commands/DescribeHumanTaskUiCommand";
import { DescribeHyperParameterTuningJobCommand, } from "./commands/DescribeHyperParameterTuningJobCommand";
import { DescribeImageCommand, } from "./commands/DescribeImageCommand";
import { DescribeImageVersionCommand, } from "./commands/DescribeImageVersionCommand";
import { DescribeInferenceComponentCommand, } from "./commands/DescribeInferenceComponentCommand";
import { DescribeInferenceExperimentCommand, } from "./commands/DescribeInferenceExperimentCommand";
import { DescribeInferenceRecommendationsJobCommand, } from "./commands/DescribeInferenceRecommendationsJobCommand";
import { DescribeLabelingJobCommand, } from "./commands/DescribeLabelingJobCommand";
import { DescribeLineageGroupCommand, } from "./commands/DescribeLineageGroupCommand";
import { DescribeMlflowTrackingServerCommand, } from "./commands/DescribeMlflowTrackingServerCommand";
import { DescribeModelBiasJobDefinitionCommand, } from "./commands/DescribeModelBiasJobDefinitionCommand";
import { DescribeModelCardCommand, } from "./commands/DescribeModelCardCommand";
import { DescribeModelCardExportJobCommand, } from "./commands/DescribeModelCardExportJobCommand";
import { DescribeModelCommand, } from "./commands/DescribeModelCommand";
import { DescribeModelExplainabilityJobDefinitionCommand, } from "./commands/DescribeModelExplainabilityJobDefinitionCommand";
import { DescribeModelPackageCommand, } from "./commands/DescribeModelPackageCommand";
import { DescribeModelPackageGroupCommand, } from "./commands/DescribeModelPackageGroupCommand";
import { DescribeModelQualityJobDefinitionCommand, } from "./commands/DescribeModelQualityJobDefinitionCommand";
import { DescribeMonitoringScheduleCommand, } from "./commands/DescribeMonitoringScheduleCommand";
import { DescribeNotebookInstanceCommand, } from "./commands/DescribeNotebookInstanceCommand";
import { DescribeNotebookInstanceLifecycleConfigCommand, } from "./commands/DescribeNotebookInstanceLifecycleConfigCommand";
import { DescribeOptimizationJobCommand, } from "./commands/DescribeOptimizationJobCommand";
import { DescribePartnerAppCommand, } from "./commands/DescribePartnerAppCommand";
import { DescribePipelineCommand, } from "./commands/DescribePipelineCommand";
import { DescribePipelineDefinitionForExecutionCommand, } from "./commands/DescribePipelineDefinitionForExecutionCommand";
import { DescribePipelineExecutionCommand, } from "./commands/DescribePipelineExecutionCommand";
import { DescribeProcessingJobCommand, } from "./commands/DescribeProcessingJobCommand";
import { DescribeProjectCommand, } from "./commands/DescribeProjectCommand";
import { DescribeSpaceCommand, } from "./commands/DescribeSpaceCommand";
import { DescribeStudioLifecycleConfigCommand, } from "./commands/DescribeStudioLifecycleConfigCommand";
import { DescribeSubscribedWorkteamCommand, } from "./commands/DescribeSubscribedWorkteamCommand";
import { DescribeTrainingJobCommand, } from "./commands/DescribeTrainingJobCommand";
import { DescribeTrainingPlanCommand, } from "./commands/DescribeTrainingPlanCommand";
import { DescribeTransformJobCommand, } from "./commands/DescribeTransformJobCommand";
import { DescribeTrialCommand, } from "./commands/DescribeTrialCommand";
import { DescribeTrialComponentCommand, } from "./commands/DescribeTrialComponentCommand";
import { DescribeUserProfileCommand, } from "./commands/DescribeUserProfileCommand";
import { DescribeWorkforceCommand, } from "./commands/DescribeWorkforceCommand";
import { DescribeWorkteamCommand, } from "./commands/DescribeWorkteamCommand";
import { DisableSagemakerServicecatalogPortfolioCommand, } from "./commands/DisableSagemakerServicecatalogPortfolioCommand";
import { DisassociateTrialComponentCommand, } from "./commands/DisassociateTrialComponentCommand";
import { EnableSagemakerServicecatalogPortfolioCommand, } from "./commands/EnableSagemakerServicecatalogPortfolioCommand";
import { GetDeviceFleetReportCommand, } from "./commands/GetDeviceFleetReportCommand";
import { GetLineageGroupPolicyCommand, } from "./commands/GetLineageGroupPolicyCommand";
import { GetModelPackageGroupPolicyCommand, } from "./commands/GetModelPackageGroupPolicyCommand";
import { GetSagemakerServicecatalogPortfolioStatusCommand, } from "./commands/GetSagemakerServicecatalogPortfolioStatusCommand";
import { GetScalingConfigurationRecommendationCommand, } from "./commands/GetScalingConfigurationRecommendationCommand";
import { GetSearchSuggestionsCommand, } from "./commands/GetSearchSuggestionsCommand";
import { ImportHubContentCommand, } from "./commands/ImportHubContentCommand";
import { ListActionsCommand } from "./commands/ListActionsCommand";
import { ListAlgorithmsCommand, } from "./commands/ListAlgorithmsCommand";
import { ListAliasesCommand } from "./commands/ListAliasesCommand";
import { ListAppImageConfigsCommand, } from "./commands/ListAppImageConfigsCommand";
import { ListAppsCommand } from "./commands/ListAppsCommand";
import { ListArtifactsCommand, } from "./commands/ListArtifactsCommand";
import { ListAssociationsCommand, } from "./commands/ListAssociationsCommand";
import { ListAutoMLJobsCommand, } from "./commands/ListAutoMLJobsCommand";
import { ListCandidatesForAutoMLJobCommand, } from "./commands/ListCandidatesForAutoMLJobCommand";
import { ListClusterNodesCommand, } from "./commands/ListClusterNodesCommand";
import { ListClusterSchedulerConfigsCommand, } from "./commands/ListClusterSchedulerConfigsCommand";
import { ListClustersCommand, } from "./commands/ListClustersCommand";
import { ListCodeRepositoriesCommand, } from "./commands/ListCodeRepositoriesCommand";
import { ListCompilationJobsCommand, } from "./commands/ListCompilationJobsCommand";
import { ListComputeQuotasCommand, } from "./commands/ListComputeQuotasCommand";
import { ListContextsCommand, } from "./commands/ListContextsCommand";
import { ListDataQualityJobDefinitionsCommand, } from "./commands/ListDataQualityJobDefinitionsCommand";
import { ListDeviceFleetsCommand, } from "./commands/ListDeviceFleetsCommand";
import { ListDevicesCommand } from "./commands/ListDevicesCommand";
import { ListDomainsCommand } from "./commands/ListDomainsCommand";
import { ListEdgeDeploymentPlansCommand, } from "./commands/ListEdgeDeploymentPlansCommand";
import { ListEdgePackagingJobsCommand, } from "./commands/ListEdgePackagingJobsCommand";
import { ListEndpointConfigsCommand, } from "./commands/ListEndpointConfigsCommand";
import { ListEndpointsCommand, } from "./commands/ListEndpointsCommand";
import { ListExperimentsCommand, } from "./commands/ListExperimentsCommand";
import { ListFeatureGroupsCommand, } from "./commands/ListFeatureGroupsCommand";
import { ListFlowDefinitionsCommand, } from "./commands/ListFlowDefinitionsCommand";
import { ListHubContentsCommand, } from "./commands/ListHubContentsCommand";
import { ListHubContentVersionsCommand, } from "./commands/ListHubContentVersionsCommand";
import { ListHubsCommand } from "./commands/ListHubsCommand";
import { ListHumanTaskUisCommand, } from "./commands/ListHumanTaskUisCommand";
import { ListHyperParameterTuningJobsCommand, } from "./commands/ListHyperParameterTuningJobsCommand";
import { ListImagesCommand } from "./commands/ListImagesCommand";
import { ListImageVersionsCommand, } from "./commands/ListImageVersionsCommand";
import { ListInferenceComponentsCommand, } from "./commands/ListInferenceComponentsCommand";
import { ListInferenceExperimentsCommand, } from "./commands/ListInferenceExperimentsCommand";
import { ListInferenceRecommendationsJobsCommand, } from "./commands/ListInferenceRecommendationsJobsCommand";
import { ListInferenceRecommendationsJobStepsCommand, } from "./commands/ListInferenceRecommendationsJobStepsCommand";
import { ListLabelingJobsCommand, } from "./commands/ListLabelingJobsCommand";
import { ListLabelingJobsForWorkteamCommand, } from "./commands/ListLabelingJobsForWorkteamCommand";
import { ListLineageGroupsCommand, } from "./commands/ListLineageGroupsCommand";
import { ListMlflowTrackingServersCommand, } from "./commands/ListMlflowTrackingServersCommand";
import { ListModelBiasJobDefinitionsCommand, } from "./commands/ListModelBiasJobDefinitionsCommand";
import { ListModelCardExportJobsCommand, } from "./commands/ListModelCardExportJobsCommand";
import { ListModelCardsCommand, } from "./commands/ListModelCardsCommand";
import { ListModelCardVersionsCommand, } from "./commands/ListModelCardVersionsCommand";
import { ListModelExplainabilityJobDefinitionsCommand, } from "./commands/ListModelExplainabilityJobDefinitionsCommand";
import { ListModelMetadataCommand, } from "./commands/ListModelMetadataCommand";
import { ListModelPackageGroupsCommand, } from "./commands/ListModelPackageGroupsCommand";
import { ListModelPackagesCommand, } from "./commands/ListModelPackagesCommand";
import { ListModelQualityJobDefinitionsCommand, } from "./commands/ListModelQualityJobDefinitionsCommand";
import { ListModelsCommand } from "./commands/ListModelsCommand";
import { ListMonitoringAlertHistoryCommand, } from "./commands/ListMonitoringAlertHistoryCommand";
import { ListMonitoringAlertsCommand, } from "./commands/ListMonitoringAlertsCommand";
import { ListMonitoringExecutionsCommand, } from "./commands/ListMonitoringExecutionsCommand";
import { ListMonitoringSchedulesCommand, } from "./commands/ListMonitoringSchedulesCommand";
import { ListNotebookInstanceLifecycleConfigsCommand, } from "./commands/ListNotebookInstanceLifecycleConfigsCommand";
import { ListNotebookInstancesCommand, } from "./commands/ListNotebookInstancesCommand";
import { ListOptimizationJobsCommand, } from "./commands/ListOptimizationJobsCommand";
import { ListPartnerAppsCommand, } from "./commands/ListPartnerAppsCommand";
import { ListPipelineExecutionsCommand, } from "./commands/ListPipelineExecutionsCommand";
import { ListPipelineExecutionStepsCommand, } from "./commands/ListPipelineExecutionStepsCommand";
import { ListPipelineParametersForExecutionCommand, } from "./commands/ListPipelineParametersForExecutionCommand";
import { ListPipelinesCommand, } from "./commands/ListPipelinesCommand";
import { ListProcessingJobsCommand, } from "./commands/ListProcessingJobsCommand";
import { ListProjectsCommand, } from "./commands/ListProjectsCommand";
import { ListResourceCatalogsCommand, } from "./commands/ListResourceCatalogsCommand";
import { ListSpacesCommand } from "./commands/ListSpacesCommand";
import { ListStageDevicesCommand, } from "./commands/ListStageDevicesCommand";
import { ListStudioLifecycleConfigsCommand, } from "./commands/ListStudioLifecycleConfigsCommand";
import { ListSubscribedWorkteamsCommand, } from "./commands/ListSubscribedWorkteamsCommand";
import { ListTagsCommand } from "./commands/ListTagsCommand";
import { ListTrainingJobsCommand, } from "./commands/ListTrainingJobsCommand";
import { ListTrainingJobsForHyperParameterTuningJobCommand, } from "./commands/ListTrainingJobsForHyperParameterTuningJobCommand";
import { ListTrainingPlansCommand, } from "./commands/ListTrainingPlansCommand";
import { ListTransformJobsCommand, } from "./commands/ListTransformJobsCommand";
import { ListTrialComponentsCommand, } from "./commands/ListTrialComponentsCommand";
import { ListTrialsCommand } from "./commands/ListTrialsCommand";
import { ListUserProfilesCommand, } from "./commands/ListUserProfilesCommand";
import { ListWorkforcesCommand, } from "./commands/ListWorkforcesCommand";
import { ListWorkteamsCommand, } from "./commands/ListWorkteamsCommand";
import { PutModelPackageGroupPolicyCommand, } from "./commands/PutModelPackageGroupPolicyCommand";
import { QueryLineageCommand, } from "./commands/QueryLineageCommand";
import { RegisterDevicesCommand, } from "./commands/RegisterDevicesCommand";
import { RenderUiTemplateCommand, } from "./commands/RenderUiTemplateCommand";
import { RetryPipelineExecutionCommand, } from "./commands/RetryPipelineExecutionCommand";
import { SearchCommand } from "./commands/SearchCommand";
import { SearchTrainingPlanOfferingsCommand, } from "./commands/SearchTrainingPlanOfferingsCommand";
import { SendPipelineExecutionStepFailureCommand, } from "./commands/SendPipelineExecutionStepFailureCommand";
import { SendPipelineExecutionStepSuccessCommand, } from "./commands/SendPipelineExecutionStepSuccessCommand";
import { StartEdgeDeploymentStageCommand, } from "./commands/StartEdgeDeploymentStageCommand";
import { StartInferenceExperimentCommand, } from "./commands/StartInferenceExperimentCommand";
import { StartMlflowTrackingServerCommand, } from "./commands/StartMlflowTrackingServerCommand";
import { StartMonitoringScheduleCommand, } from "./commands/StartMonitoringScheduleCommand";
import { StartNotebookInstanceCommand, } from "./commands/StartNotebookInstanceCommand";
import { StartPipelineExecutionCommand, } from "./commands/StartPipelineExecutionCommand";
import { StopAutoMLJobCommand, } from "./commands/StopAutoMLJobCommand";
import { StopCompilationJobCommand, } from "./commands/StopCompilationJobCommand";
import { StopEdgeDeploymentStageCommand, } from "./commands/StopEdgeDeploymentStageCommand";
import { StopEdgePackagingJobCommand, } from "./commands/StopEdgePackagingJobCommand";
import { StopHyperParameterTuningJobCommand, } from "./commands/StopHyperParameterTuningJobCommand";
import { StopInferenceExperimentCommand, } from "./commands/StopInferenceExperimentCommand";
import { StopInferenceRecommendationsJobCommand, } from "./commands/StopInferenceRecommendationsJobCommand";
import { StopLabelingJobCommand, } from "./commands/StopLabelingJobCommand";
import { StopMlflowTrackingServerCommand, } from "./commands/StopMlflowTrackingServerCommand";
import { StopMonitoringScheduleCommand, } from "./commands/StopMonitoringScheduleCommand";
import { StopNotebookInstanceCommand, } from "./commands/StopNotebookInstanceCommand";
import { StopOptimizationJobCommand, } from "./commands/StopOptimizationJobCommand";
import { StopPipelineExecutionCommand, } from "./commands/StopPipelineExecutionCommand";
import { StopProcessingJobCommand, } from "./commands/StopProcessingJobCommand";
import { StopTrainingJobCommand, } from "./commands/StopTrainingJobCommand";
import { StopTransformJobCommand, } from "./commands/StopTransformJobCommand";
import { UpdateActionCommand, } from "./commands/UpdateActionCommand";
import { UpdateAppImageConfigCommand, } from "./commands/UpdateAppImageConfigCommand";
import { UpdateArtifactCommand, } from "./commands/UpdateArtifactCommand";
import { UpdateClusterCommand, } from "./commands/UpdateClusterCommand";
import { UpdateClusterSchedulerConfigCommand, } from "./commands/UpdateClusterSchedulerConfigCommand";
import { UpdateClusterSoftwareCommand, } from "./commands/UpdateClusterSoftwareCommand";
import { UpdateCodeRepositoryCommand, } from "./commands/UpdateCodeRepositoryCommand";
import { UpdateComputeQuotaCommand, } from "./commands/UpdateComputeQuotaCommand";
import { UpdateContextCommand, } from "./commands/UpdateContextCommand";
import { UpdateDeviceFleetCommand, } from "./commands/UpdateDeviceFleetCommand";
import { UpdateDevicesCommand, } from "./commands/UpdateDevicesCommand";
import { UpdateDomainCommand, } from "./commands/UpdateDomainCommand";
import { UpdateEndpointCommand, } from "./commands/UpdateEndpointCommand";
import { UpdateEndpointWeightsAndCapacitiesCommand, } from "./commands/UpdateEndpointWeightsAndCapacitiesCommand";
import { UpdateExperimentCommand, } from "./commands/UpdateExperimentCommand";
import { UpdateFeatureGroupCommand, } from "./commands/UpdateFeatureGroupCommand";
import { UpdateFeatureMetadataCommand, } from "./commands/UpdateFeatureMetadataCommand";
import { UpdateHubCommand } from "./commands/UpdateHubCommand";
import { UpdateHubContentCommand, } from "./commands/UpdateHubContentCommand";
import { UpdateHubContentReferenceCommand, } from "./commands/UpdateHubContentReferenceCommand";
import { UpdateImageCommand } from "./commands/UpdateImageCommand";
import { UpdateImageVersionCommand, } from "./commands/UpdateImageVersionCommand";
import { UpdateInferenceComponentCommand, } from "./commands/UpdateInferenceComponentCommand";
import { UpdateInferenceComponentRuntimeConfigCommand, } from "./commands/UpdateInferenceComponentRuntimeConfigCommand";
import { UpdateInferenceExperimentCommand, } from "./commands/UpdateInferenceExperimentCommand";
import { UpdateMlflowTrackingServerCommand, } from "./commands/UpdateMlflowTrackingServerCommand";
import { UpdateModelCardCommand, } from "./commands/UpdateModelCardCommand";
import { UpdateModelPackageCommand, } from "./commands/UpdateModelPackageCommand";
import { UpdateMonitoringAlertCommand, } from "./commands/UpdateMonitoringAlertCommand";
import { UpdateMonitoringScheduleCommand, } from "./commands/UpdateMonitoringScheduleCommand";
import { UpdateNotebookInstanceCommand, } from "./commands/UpdateNotebookInstanceCommand";
import { UpdateNotebookInstanceLifecycleConfigCommand, } from "./commands/UpdateNotebookInstanceLifecycleConfigCommand";
import { UpdatePartnerAppCommand, } from "./commands/UpdatePartnerAppCommand";
import { UpdatePipelineCommand, } from "./commands/UpdatePipelineCommand";
import { UpdatePipelineExecutionCommand, } from "./commands/UpdatePipelineExecutionCommand";
import { UpdateProjectCommand, } from "./commands/UpdateProjectCommand";
import { UpdateSpaceCommand } from "./commands/UpdateSpaceCommand";
import { UpdateTrainingJobCommand, } from "./commands/UpdateTrainingJobCommand";
import { UpdateTrialCommand } from "./commands/UpdateTrialCommand";
import { UpdateTrialComponentCommand, } from "./commands/UpdateTrialComponentCommand";
import { UpdateUserProfileCommand, } from "./commands/UpdateUserProfileCommand";
import { UpdateWorkforceCommand, } from "./commands/UpdateWorkforceCommand";
import { UpdateWorkteamCommand, } from "./commands/UpdateWorkteamCommand";
import { SageMakerClient } from "./SageMakerClient";
const commands = {
    AddAssociationCommand,
    AddTagsCommand,
    AssociateTrialComponentCommand,
    BatchDeleteClusterNodesCommand,
    BatchDescribeModelPackageCommand,
    CreateActionCommand,
    CreateAlgorithmCommand,
    CreateAppCommand,
    CreateAppImageConfigCommand,
    CreateArtifactCommand,
    CreateAutoMLJobCommand,
    CreateAutoMLJobV2Command,
    CreateClusterCommand,
    CreateClusterSchedulerConfigCommand,
    CreateCodeRepositoryCommand,
    CreateCompilationJobCommand,
    CreateComputeQuotaCommand,
    CreateContextCommand,
    CreateDataQualityJobDefinitionCommand,
    CreateDeviceFleetCommand,
    CreateDomainCommand,
    CreateEdgeDeploymentPlanCommand,
    CreateEdgeDeploymentStageCommand,
    CreateEdgePackagingJobCommand,
    CreateEndpointCommand,
    CreateEndpointConfigCommand,
    CreateExperimentCommand,
    CreateFeatureGroupCommand,
    CreateFlowDefinitionCommand,
    CreateHubCommand,
    CreateHubContentReferenceCommand,
    CreateHumanTaskUiCommand,
    CreateHyperParameterTuningJobCommand,
    CreateImageCommand,
    CreateImageVersionCommand,
    CreateInferenceComponentCommand,
    CreateInferenceExperimentCommand,
    CreateInferenceRecommendationsJobCommand,
    CreateLabelingJobCommand,
    CreateMlflowTrackingServerCommand,
    CreateModelCommand,
    CreateModelBiasJobDefinitionCommand,
    CreateModelCardCommand,
    CreateModelCardExportJobCommand,
    CreateModelExplainabilityJobDefinitionCommand,
    CreateModelPackageCommand,
    CreateModelPackageGroupCommand,
    CreateModelQualityJobDefinitionCommand,
    CreateMonitoringScheduleCommand,
    CreateNotebookInstanceCommand,
    CreateNotebookInstanceLifecycleConfigCommand,
    CreateOptimizationJobCommand,
    CreatePartnerAppCommand,
    CreatePartnerAppPresignedUrlCommand,
    CreatePipelineCommand,
    CreatePresignedDomainUrlCommand,
    CreatePresignedMlflowTrackingServerUrlCommand,
    CreatePresignedNotebookInstanceUrlCommand,
    CreateProcessingJobCommand,
    CreateProjectCommand,
    CreateSpaceCommand,
    CreateStudioLifecycleConfigCommand,
    CreateTrainingJobCommand,
    CreateTrainingPlanCommand,
    CreateTransformJobCommand,
    CreateTrialCommand,
    CreateTrialComponentCommand,
    CreateUserProfileCommand,
    CreateWorkforceCommand,
    CreateWorkteamCommand,
    DeleteActionCommand,
    DeleteAlgorithmCommand,
    DeleteAppCommand,
    DeleteAppImageConfigCommand,
    DeleteArtifactCommand,
    DeleteAssociationCommand,
    DeleteClusterCommand,
    DeleteClusterSchedulerConfigCommand,
    DeleteCodeRepositoryCommand,
    DeleteCompilationJobCommand,
    DeleteComputeQuotaCommand,
    DeleteContextCommand,
    DeleteDataQualityJobDefinitionCommand,
    DeleteDeviceFleetCommand,
    DeleteDomainCommand,
    DeleteEdgeDeploymentPlanCommand,
    DeleteEdgeDeploymentStageCommand,
    DeleteEndpointCommand,
    DeleteEndpointConfigCommand,
    DeleteExperimentCommand,
    DeleteFeatureGroupCommand,
    DeleteFlowDefinitionCommand,
    DeleteHubCommand,
    DeleteHubContentCommand,
    DeleteHubContentReferenceCommand,
    DeleteHumanTaskUiCommand,
    DeleteHyperParameterTuningJobCommand,
    DeleteImageCommand,
    DeleteImageVersionCommand,
    DeleteInferenceComponentCommand,
    DeleteInferenceExperimentCommand,
    DeleteMlflowTrackingServerCommand,
    DeleteModelCommand,
    DeleteModelBiasJobDefinitionCommand,
    DeleteModelCardCommand,
    DeleteModelExplainabilityJobDefinitionCommand,
    DeleteModelPackageCommand,
    DeleteModelPackageGroupCommand,
    DeleteModelPackageGroupPolicyCommand,
    DeleteModelQualityJobDefinitionCommand,
    DeleteMonitoringScheduleCommand,
    DeleteNotebookInstanceCommand,
    DeleteNotebookInstanceLifecycleConfigCommand,
    DeleteOptimizationJobCommand,
    DeletePartnerAppCommand,
    DeletePipelineCommand,
    DeleteProjectCommand,
    DeleteSpaceCommand,
    DeleteStudioLifecycleConfigCommand,
    DeleteTagsCommand,
    DeleteTrialCommand,
    DeleteTrialComponentCommand,
    DeleteUserProfileCommand,
    DeleteWorkforceCommand,
    DeleteWorkteamCommand,
    DeregisterDevicesCommand,
    DescribeActionCommand,
    DescribeAlgorithmCommand,
    DescribeAppCommand,
    DescribeAppImageConfigCommand,
    DescribeArtifactCommand,
    DescribeAutoMLJobCommand,
    DescribeAutoMLJobV2Command,
    DescribeClusterCommand,
    DescribeClusterNodeCommand,
    DescribeClusterSchedulerConfigCommand,
    DescribeCodeRepositoryCommand,
    DescribeCompilationJobCommand,
    DescribeComputeQuotaCommand,
    DescribeContextCommand,
    DescribeDataQualityJobDefinitionCommand,
    DescribeDeviceCommand,
    DescribeDeviceFleetCommand,
    DescribeDomainCommand,
    DescribeEdgeDeploymentPlanCommand,
    DescribeEdgePackagingJobCommand,
    DescribeEndpointCommand,
    DescribeEndpointConfigCommand,
    DescribeExperimentCommand,
    DescribeFeatureGroupCommand,
    DescribeFeatureMetadataCommand,
    DescribeFlowDefinitionCommand,
    DescribeHubCommand,
    DescribeHubContentCommand,
    DescribeHumanTaskUiCommand,
    DescribeHyperParameterTuningJobCommand,
    DescribeImageCommand,
    DescribeImageVersionCommand,
    DescribeInferenceComponentCommand,
    DescribeInferenceExperimentCommand,
    DescribeInferenceRecommendationsJobCommand,
    DescribeLabelingJobCommand,
    DescribeLineageGroupCommand,
    DescribeMlflowTrackingServerCommand,
    DescribeModelCommand,
    DescribeModelBiasJobDefinitionCommand,
    DescribeModelCardCommand,
    DescribeModelCardExportJobCommand,
    DescribeModelExplainabilityJobDefinitionCommand,
    DescribeModelPackageCommand,
    DescribeModelPackageGroupCommand,
    DescribeModelQualityJobDefinitionCommand,
    DescribeMonitoringScheduleCommand,
    DescribeNotebookInstanceCommand,
    DescribeNotebookInstanceLifecycleConfigCommand,
    DescribeOptimizationJobCommand,
    DescribePartnerAppCommand,
    DescribePipelineCommand,
    DescribePipelineDefinitionForExecutionCommand,
    DescribePipelineExecutionCommand,
    DescribeProcessingJobCommand,
    DescribeProjectCommand,
    DescribeSpaceCommand,
    DescribeStudioLifecycleConfigCommand,
    DescribeSubscribedWorkteamCommand,
    DescribeTrainingJobCommand,
    DescribeTrainingPlanCommand,
    DescribeTransformJobCommand,
    DescribeTrialCommand,
    DescribeTrialComponentCommand,
    DescribeUserProfileCommand,
    DescribeWorkforceCommand,
    DescribeWorkteamCommand,
    DisableSagemakerServicecatalogPortfolioCommand,
    DisassociateTrialComponentCommand,
    EnableSagemakerServicecatalogPortfolioCommand,
    GetDeviceFleetReportCommand,
    GetLineageGroupPolicyCommand,
    GetModelPackageGroupPolicyCommand,
    GetSagemakerServicecatalogPortfolioStatusCommand,
    GetScalingConfigurationRecommendationCommand,
    GetSearchSuggestionsCommand,
    ImportHubContentCommand,
    ListActionsCommand,
    ListAlgorithmsCommand,
    ListAliasesCommand,
    ListAppImageConfigsCommand,
    ListAppsCommand,
    ListArtifactsCommand,
    ListAssociationsCommand,
    ListAutoMLJobsCommand,
    ListCandidatesForAutoMLJobCommand,
    ListClusterNodesCommand,
    ListClustersCommand,
    ListClusterSchedulerConfigsCommand,
    ListCodeRepositoriesCommand,
    ListCompilationJobsCommand,
    ListComputeQuotasCommand,
    ListContextsCommand,
    ListDataQualityJobDefinitionsCommand,
    ListDeviceFleetsCommand,
    ListDevicesCommand,
    ListDomainsCommand,
    ListEdgeDeploymentPlansCommand,
    ListEdgePackagingJobsCommand,
    ListEndpointConfigsCommand,
    ListEndpointsCommand,
    ListExperimentsCommand,
    ListFeatureGroupsCommand,
    ListFlowDefinitionsCommand,
    ListHubContentsCommand,
    ListHubContentVersionsCommand,
    ListHubsCommand,
    ListHumanTaskUisCommand,
    ListHyperParameterTuningJobsCommand,
    ListImagesCommand,
    ListImageVersionsCommand,
    ListInferenceComponentsCommand,
    ListInferenceExperimentsCommand,
    ListInferenceRecommendationsJobsCommand,
    ListInferenceRecommendationsJobStepsCommand,
    ListLabelingJobsCommand,
    ListLabelingJobsForWorkteamCommand,
    ListLineageGroupsCommand,
    ListMlflowTrackingServersCommand,
    ListModelBiasJobDefinitionsCommand,
    ListModelCardExportJobsCommand,
    ListModelCardsCommand,
    ListModelCardVersionsCommand,
    ListModelExplainabilityJobDefinitionsCommand,
    ListModelMetadataCommand,
    ListModelPackageGroupsCommand,
    ListModelPackagesCommand,
    ListModelQualityJobDefinitionsCommand,
    ListModelsCommand,
    ListMonitoringAlertHistoryCommand,
    ListMonitoringAlertsCommand,
    ListMonitoringExecutionsCommand,
    ListMonitoringSchedulesCommand,
    ListNotebookInstanceLifecycleConfigsCommand,
    ListNotebookInstancesCommand,
    ListOptimizationJobsCommand,
    ListPartnerAppsCommand,
    ListPipelineExecutionsCommand,
    ListPipelineExecutionStepsCommand,
    ListPipelineParametersForExecutionCommand,
    ListPipelinesCommand,
    ListProcessingJobsCommand,
    ListProjectsCommand,
    ListResourceCatalogsCommand,
    ListSpacesCommand,
    ListStageDevicesCommand,
    ListStudioLifecycleConfigsCommand,
    ListSubscribedWorkteamsCommand,
    ListTagsCommand,
    ListTrainingJobsCommand,
    ListTrainingJobsForHyperParameterTuningJobCommand,
    ListTrainingPlansCommand,
    ListTransformJobsCommand,
    ListTrialComponentsCommand,
    ListTrialsCommand,
    ListUserProfilesCommand,
    ListWorkforcesCommand,
    ListWorkteamsCommand,
    PutModelPackageGroupPolicyCommand,
    QueryLineageCommand,
    RegisterDevicesCommand,
    RenderUiTemplateCommand,
    RetryPipelineExecutionCommand,
    SearchCommand,
    SearchTrainingPlanOfferingsCommand,
    SendPipelineExecutionStepFailureCommand,
    SendPipelineExecutionStepSuccessCommand,
    StartEdgeDeploymentStageCommand,
    StartInferenceExperimentCommand,
    StartMlflowTrackingServerCommand,
    StartMonitoringScheduleCommand,
    StartNotebookInstanceCommand,
    StartPipelineExecutionCommand,
    StopAutoMLJobCommand,
    StopCompilationJobCommand,
    StopEdgeDeploymentStageCommand,
    StopEdgePackagingJobCommand,
    StopHyperParameterTuningJobCommand,
    StopInferenceExperimentCommand,
    StopInferenceRecommendationsJobCommand,
    StopLabelingJobCommand,
    StopMlflowTrackingServerCommand,
    StopMonitoringScheduleCommand,
    StopNotebookInstanceCommand,
    StopOptimizationJobCommand,
    StopPipelineExecutionCommand,
    StopProcessingJobCommand,
    StopTrainingJobCommand,
    StopTransformJobCommand,
    UpdateActionCommand,
    UpdateAppImageConfigCommand,
    UpdateArtifactCommand,
    UpdateClusterCommand,
    UpdateClusterSchedulerConfigCommand,
    UpdateClusterSoftwareCommand,
    UpdateCodeRepositoryCommand,
    UpdateComputeQuotaCommand,
    UpdateContextCommand,
    UpdateDeviceFleetCommand,
    UpdateDevicesCommand,
    UpdateDomainCommand,
    UpdateEndpointCommand,
    UpdateEndpointWeightsAndCapacitiesCommand,
    UpdateExperimentCommand,
    UpdateFeatureGroupCommand,
    UpdateFeatureMetadataCommand,
    UpdateHubCommand,
    UpdateHubContentCommand,
    UpdateHubContentReferenceCommand,
    UpdateImageCommand,
    UpdateImageVersionCommand,
    UpdateInferenceComponentCommand,
    UpdateInferenceComponentRuntimeConfigCommand,
    UpdateInferenceExperimentCommand,
    UpdateMlflowTrackingServerCommand,
    UpdateModelCardCommand,
    UpdateModelPackageCommand,
    UpdateMonitoringAlertCommand,
    UpdateMonitoringScheduleCommand,
    UpdateNotebookInstanceCommand,
    UpdateNotebookInstanceLifecycleConfigCommand,
    UpdatePartnerAppCommand,
    UpdatePipelineCommand,
    UpdatePipelineExecutionCommand,
    UpdateProjectCommand,
    UpdateSpaceCommand,
    UpdateTrainingJobCommand,
    UpdateTrialCommand,
    UpdateTrialComponentCommand,
    UpdateUserProfileCommand,
    UpdateWorkforceCommand,
    UpdateWorkteamCommand,
};
export class SageMaker extends SageMakerClient {
}
createAggregatedClient(commands, SageMaker);
