import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  GetModelPackageGroupPolicyInput,
  GetModelPackageGroupPolicyOutput,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface GetModelPackageGroupPolicyCommandInput
  extends GetModelPackageGroupPolicyInput {}
export interface GetModelPackageGroupPolicyCommandOutput
  extends GetModelPackageGroupPolicyOutput,
    __MetadataBearer {}
declare const GetModelPackageGroupPolicyCommand_base: {
  new (
    input: GetModelPackageGroupPolicyCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetModelPackageGroupPolicyCommandInput,
    GetModelPackageGroupPolicyCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetModelPackageGroupPolicyCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetModelPackageGroupPolicyCommandInput,
    GetModelPackageGroupPolicyCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetModelPackageGroupPolicyCommand extends GetModelPackageGroupPolicyCommand_base {
  protected static __types: {
    api: {
      input: GetModelPackageGroupPolicyInput;
      output: GetModelPackageGroupPolicyOutput;
    };
    sdk: {
      input: GetModelPackageGroupPolicyCommandInput;
      output: GetModelPackageGroupPolicyCommandOutput;
    };
  };
}
