import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ListTrainingJobsForHyperParameterTuningJobRequest,
  ListTrainingJobsForHyperParameterTuningJobResponse,
} from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface ListTrainingJobsForHyperParameterTuningJobCommandInput
  extends ListTrainingJobsForHyperParameterTuningJobRequest {}
export interface ListTrainingJobsForHyperParameterTuningJobCommandOutput
  extends ListTrainingJobsForHyperParameterTuningJobResponse,
    __MetadataBearer {}
declare const ListTrainingJobsForHyperParameterTuningJobCommand_base: {
  new (
    input: ListTrainingJobsForHyperParameterTuningJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListTrainingJobsForHyperParameterTuningJobCommandInput,
    ListTrainingJobsForHyperParameterTuningJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: ListTrainingJobsForHyperParameterTuningJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListTrainingJobsForHyperParameterTuningJobCommandInput,
    ListTrainingJobsForHyperParameterTuningJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListTrainingJobsForHyperParameterTuningJobCommand extends ListTrainingJobsForHyperParameterTuningJobCommand_base {
  protected static __types: {
    api: {
      input: ListTrainingJobsForHyperParameterTuningJobRequest;
      output: ListTrainingJobsForHyperParameterTuningJobResponse;
    };
    sdk: {
      input: ListTrainingJobsForHyperParameterTuningJobCommandInput;
      output: ListTrainingJobsForHyperParameterTuningJobCommandOutput;
    };
  };
}
