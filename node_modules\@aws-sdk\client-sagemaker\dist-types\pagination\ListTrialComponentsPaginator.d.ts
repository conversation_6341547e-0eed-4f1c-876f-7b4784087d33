import { Paginator } from "@smithy/types";
import { ListTrialComponentsCommandInput, ListTrialComponentsCommandOutput } from "../commands/ListTrialComponentsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListTrialComponents: (config: SageMakerPaginationConfiguration, input: ListTrialComponentsCommandInput, ...rest: any[]) => Paginator<ListTrialComponentsCommandOutput>;
