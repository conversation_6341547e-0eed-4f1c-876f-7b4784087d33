import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { StopTrainingJobRequest } from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface StopTrainingJobCommandInput extends StopTrainingJobRequest {}
export interface StopTrainingJobCommandOutput extends __MetadataBearer {}
declare const StopTrainingJobCommand_base: {
  new (
    input: StopTrainingJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StopTrainingJobCommandInput,
    StopTrainingJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: StopTrainingJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StopTrainingJobCommandInput,
    StopTrainingJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class StopTrainingJobCommand extends StopTrainingJobCommand_base {
  protected static __types: {
    api: {
      input: StopTrainingJobRequest;
      output: {};
    };
    sdk: {
      input: StopTrainingJobCommandInput;
      output: StopTrainingJobCommandOutput;
    };
  };
}
