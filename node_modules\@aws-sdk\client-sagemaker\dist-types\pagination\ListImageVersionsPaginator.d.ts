import { Paginator } from "@smithy/types";
import { ListImageVersionsCommandInput, ListImageVersionsCommandOutput } from "../commands/ListImageVersionsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListImageVersions: (config: SageMakerPaginationConfiguration, input: ListImageVersionsCommandInput, ...rest: any[]) => Paginator<ListImageVersionsCommandOutput>;
