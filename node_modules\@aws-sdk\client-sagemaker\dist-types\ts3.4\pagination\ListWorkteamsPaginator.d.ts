import { Paginator } from "@smithy/types";
import {
  ListWorkteamsCommandInput,
  ListWorkteamsCommandOutput,
} from "../commands/ListWorkteamsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
export declare const paginateListWorkteams: (
  config: SageMakerPaginationConfiguration,
  input: ListWorkteamsCommandInput,
  ...rest: any[]
) => Paginator<ListWorkteamsCommandOutput>;
