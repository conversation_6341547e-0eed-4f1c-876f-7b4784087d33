import { Paginator } from "@smithy/types";
import { ListWorkforcesCommandInput, ListWorkforcesCommandOutput } from "../commands/ListWorkforcesCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListWorkforces: (config: SageMakerPaginationConfiguration, input: ListWorkforcesCommandInput, ...rest: any[]) => Paginator<ListWorkforcesCommandOutput>;
