import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeModelPackageInput,
  DescribeModelPackageOutput,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeModelPackageCommandInput
  extends DescribeModelPackageInput {}
export interface DescribeModelPackageCommandOutput
  extends DescribeModelPackageOutput,
    __MetadataBearer {}
declare const DescribeModelPackageCommand_base: {
  new (
    input: DescribeModelPackageCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeModelPackageCommandInput,
    DescribeModelPackageCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeModelPackageCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeModelPackageCommandInput,
    DescribeModelPackageCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeModelPackageCommand extends DescribeModelPackageCommand_base {
  protected static __types: {
    api: {
      input: DescribeModelPackageInput;
      output: DescribeModelPackageOutput;
    };
    sdk: {
      input: DescribeModelPackageCommandInput;
      output: DescribeModelPackageCommandOutput;
    };
  };
}
