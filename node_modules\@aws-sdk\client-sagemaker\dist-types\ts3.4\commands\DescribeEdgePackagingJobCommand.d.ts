import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeEdgePackagingJobRequest,
  DescribeEdgePackagingJobResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeEdgePackagingJobCommandInput
  extends DescribeEdgePackagingJobRequest {}
export interface DescribeEdgePackagingJobCommandOutput
  extends DescribeEdgePackagingJobResponse,
    __MetadataBearer {}
declare const DescribeEdgePackagingJobCommand_base: {
  new (
    input: DescribeEdgePackagingJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeEdgePackagingJobCommandInput,
    DescribeEdgePackagingJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeEdgePackagingJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeEdgePackagingJobCommandInput,
    DescribeEdgePackagingJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeEdgePackagingJobCommand extends DescribeEdgePackagingJobCommand_base {
  protected static __types: {
    api: {
      input: DescribeEdgePackagingJobRequest;
      output: DescribeEdgePackagingJobResponse;
    };
    sdk: {
      input: DescribeEdgePackagingJobCommandInput;
      output: DescribeEdgePackagingJobCommandOutput;
    };
  };
}
