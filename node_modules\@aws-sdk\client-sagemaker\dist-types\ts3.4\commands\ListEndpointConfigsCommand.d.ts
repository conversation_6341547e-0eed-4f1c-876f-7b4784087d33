import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ListEndpointConfigsInput,
  ListEndpointConfigsOutput,
} from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface ListEndpointConfigsCommandInput
  extends ListEndpointConfigsInput {}
export interface ListEndpointConfigsCommandOutput
  extends ListEndpointConfigsOutput,
    __MetadataBearer {}
declare const ListEndpointConfigsCommand_base: {
  new (
    input: ListEndpointConfigsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListEndpointConfigsCommandInput,
    ListEndpointConfigsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    ...[input]: [] | [ListEndpointConfigsCommandInput]
  ): import("@smithy/smithy-client").CommandImpl<
    ListEndpointConfigsCommandInput,
    ListEndpointConfigsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListEndpointConfigsCommand extends ListEndpointConfigsCommand_base {
  protected static __types: {
    api: {
      input: ListEndpointConfigsInput;
      output: ListEndpointConfigsOutput;
    };
    sdk: {
      input: ListEndpointConfigsCommandInput;
      output: ListEndpointConfigsCommandOutput;
    };
  };
}
