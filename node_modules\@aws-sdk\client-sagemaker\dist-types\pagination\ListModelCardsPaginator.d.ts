import { Paginator } from "@smithy/types";
import { ListModelCardsCommandInput, ListModelCardsCommandOutput } from "../commands/ListModelCardsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListModelCards: (config: SageMakerPaginationConfiguration, input: ListModelCardsCommandInput, ...rest: any[]) => Paginator<ListModelCardsCommandOutput>;
