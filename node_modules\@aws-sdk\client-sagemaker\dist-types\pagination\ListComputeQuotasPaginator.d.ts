import { Paginator } from "@smithy/types";
import { ListComputeQuotasCommandInput, ListComputeQuotasCommandOutput } from "../commands/ListComputeQuotasCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListComputeQuotas: (config: SageMakerPaginationConfiguration, input: ListComputeQuotasCommandInput, ...rest: any[]) => Paginator<ListComputeQuotasCommandOutput>;
