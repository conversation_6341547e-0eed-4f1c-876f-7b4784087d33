import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateCodeRepositoryInput,
  CreateCodeRepositoryOutput,
} from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateCodeRepositoryCommandInput
  extends CreateCodeRepositoryInput {}
export interface CreateCodeRepositoryCommandOutput
  extends CreateCodeRepositoryOutput,
    __MetadataBearer {}
declare const CreateCodeRepositoryCommand_base: {
  new (
    input: CreateCodeRepositoryCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateCodeRepositoryCommandInput,
    CreateCodeRepositoryCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateCodeRepositoryCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateCodeRepositoryCommandInput,
    CreateCodeRepositoryCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateCodeRepositoryCommand extends CreateCodeRepositoryCommand_base {
  protected static __types: {
    api: {
      input: CreateCodeRepositoryInput;
      output: CreateCodeRepositoryOutput;
    };
    sdk: {
      input: CreateCodeRepositoryCommandInput;
      output: CreateCodeRepositoryCommandOutput;
    };
  };
}
