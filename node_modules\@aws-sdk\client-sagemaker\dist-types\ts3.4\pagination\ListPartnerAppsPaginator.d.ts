import { Paginator } from "@smithy/types";
import {
  ListPartnerAppsCommandInput,
  ListPartnerAppsCommandOutput,
} from "../commands/ListPartnerAppsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
export declare const paginateListPartnerApps: (
  config: SageMakerPaginationConfiguration,
  input: ListPartnerAppsCommandInput,
  ...rest: any[]
) => Paginator<ListPartnerAppsCommandOutput>;
