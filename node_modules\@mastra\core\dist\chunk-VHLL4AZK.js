// src/logger/constants.ts
var RegisteredLogger = {
  AGENT: "AGENT",
  NETWORK: "NETWORK",
  WORKFLOW: "WORKFLOW",
  LLM: "LLM",
  TTS: "TTS",
  VOICE: "VOICE",
  VECTOR: "VECTOR",
  BUNDLER: "BUNDLER",
  DEPLOYER: "DEPLOYER",
  MEMORY: "MEMORY",
  STORAGE: "STORAGE",
  EMBEDDINGS: "EMBEDDINGS",
  MCP_SERVER: "MCP_SERVER"
};
var LogLevel = {
  DEBUG: "debug",
  INFO: "info",
  WARN: "warn",
  ERROR: "error",
  NONE: "silent"
};

// src/logger/logger.ts
var MastraLogger = class {
  name;
  level;
  transports;
  constructor(options = {}) {
    this.name = options.name || "Mastra";
    this.level = options.level || LogLevel.ERROR;
    this.transports = new Map(Object.entries(options.transports || {}));
  }
  getTransports() {
    return this.transports;
  }
  async getLogs(transportId) {
    if (!transportId || !this.transports.has(transportId)) {
      return [];
    }
    return this.transports.get(transportId).getLogs() ?? [];
  }
  async getLogsByRunId({ transportId, runId }) {
    if (!transportId || !this.transports.has(transportId) || !runId) {
      return [];
    }
    return this.transports.get(transportId).getLogsByRunId({ runId }) ?? [];
  }
};

// src/logger/default-logger.ts
var createLogger = (options) => {
  const logger = new ConsoleLogger(options);
  logger.warn(`createLogger is deprecated. Please use "new ConsoleLogger()" from "@mastra/core/logger" instead.`);
  return logger;
};
var ConsoleLogger = class extends MastraLogger {
  constructor(options = {}) {
    super(options);
  }
  debug(message, ...args) {
    if (this.level === LogLevel.DEBUG) {
      console.debug(message, ...args);
    }
  }
  info(message, ...args) {
    if (this.level === LogLevel.INFO || this.level === LogLevel.DEBUG) {
      console.info(message, ...args);
    }
  }
  warn(message, ...args) {
    if (this.level === LogLevel.WARN || this.level === LogLevel.INFO || this.level === LogLevel.DEBUG) {
      console.warn(message, ...args);
    }
  }
  error(message, ...args) {
    if (this.level === LogLevel.ERROR || this.level === LogLevel.WARN || this.level === LogLevel.INFO || this.level === LogLevel.DEBUG) {
      console.error(message, ...args);
    }
  }
  async getLogs(_transportId) {
    return [];
  }
  async getLogsByRunId(_args) {
    return [];
  }
};

export { ConsoleLogger, LogLevel, MastraLogger, RegisteredLogger, createLogger };
