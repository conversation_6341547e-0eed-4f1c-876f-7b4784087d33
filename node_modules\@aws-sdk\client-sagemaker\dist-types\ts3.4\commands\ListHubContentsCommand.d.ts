import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ListHubContentsRequest,
  ListHubContentsResponse,
} from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface ListHubContentsCommandInput extends ListHubContentsRequest {}
export interface ListHubContentsCommandOutput
  extends ListHubContentsResponse,
    __MetadataBearer {}
declare const ListHubContentsCommand_base: {
  new (
    input: ListHubContentsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListHubContentsCommandInput,
    ListHubContentsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: ListHubContentsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListHubContentsCommandInput,
    ListHubContentsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListHubContentsCommand extends ListHubContentsCommand_base {
  protected static __types: {
    api: {
      input: ListHubContentsRequest;
      output: ListHubContentsResponse;
    };
    sdk: {
      input: ListHubContentsCommandInput;
      output: ListHubContentsCommandOutput;
    };
  };
}
