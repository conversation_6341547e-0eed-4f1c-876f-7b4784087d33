import { Paginator } from "@smithy/types";
import {
  ListModelBiasJobDefinitionsCommandInput,
  ListModelBiasJobDefinitionsCommandOutput,
} from "../commands/ListModelBiasJobDefinitionsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
export declare const paginateListModelBiasJobDefinitions: (
  config: SageMakerPaginationConfiguration,
  input: ListModelBiasJobDefinitionsCommandInput,
  ...rest: any[]
) => Paginator<ListModelBiasJobDefinitionsCommandOutput>;
