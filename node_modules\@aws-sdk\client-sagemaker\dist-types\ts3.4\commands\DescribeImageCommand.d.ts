import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeImageRequest,
  DescribeImageResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeImageCommandInput extends DescribeImageRequest {}
export interface DescribeImageCommandOutput
  extends DescribeImageResponse,
    __MetadataBearer {}
declare const DescribeImageCommand_base: {
  new (
    input: DescribeImageCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeImageCommandInput,
    DescribeImageCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeImageCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeImageCommandInput,
    DescribeImageCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeImageCommand extends DescribeImageCommand_base {
  protected static __types: {
    api: {
      input: DescribeImageRequest;
      output: DescribeImageResponse;
    };
    sdk: {
      input: DescribeImageCommandInput;
      output: DescribeImageCommandOutput;
    };
  };
}
