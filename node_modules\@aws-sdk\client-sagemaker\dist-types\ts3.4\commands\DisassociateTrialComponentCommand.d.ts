import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DisassociateTrialComponentRequest,
  DisassociateTrialComponentResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DisassociateTrialComponentCommandInput
  extends DisassociateTrialComponentRequest {}
export interface DisassociateTrialComponentCommandOutput
  extends DisassociateTrialComponentResponse,
    __MetadataBearer {}
declare const DisassociateTrialComponentCommand_base: {
  new (
    input: DisassociateTrialComponentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DisassociateTrialComponentCommandInput,
    DisassociateTrialComponentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DisassociateTrialComponentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DisassociateTrialComponentCommandInput,
    DisassociateTrialComponentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DisassociateTrialComponentCommand extends DisassociateTrialComponentCommand_base {
  protected static __types: {
    api: {
      input: DisassociateTrialComponentRequest;
      output: DisassociateTrialComponentResponse;
    };
    sdk: {
      input: DisassociateTrialComponentCommandInput;
      output: DisassociateTrialComponentCommandOutput;
    };
  };
}
