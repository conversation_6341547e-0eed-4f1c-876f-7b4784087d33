import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeEndpointConfigInput,
  DescribeEndpointConfigOutput,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeEndpointConfigCommandInput
  extends DescribeEndpointConfigInput {}
export interface DescribeEndpointConfigCommandOutput
  extends DescribeEndpointConfigOutput,
    __MetadataBearer {}
declare const DescribeEndpointConfigCommand_base: {
  new (
    input: DescribeEndpointConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeEndpointConfigCommandInput,
    DescribeEndpointConfigCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeEndpointConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeEndpointConfigCommandInput,
    DescribeEndpointConfigCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeEndpointConfigCommand extends DescribeEndpointConfigCommand_base {
  protected static __types: {
    api: {
      input: DescribeEndpointConfigInput;
      output: DescribeEndpointConfigOutput;
    };
    sdk: {
      input: DescribeEndpointConfigCommandInput;
      output: DescribeEndpointConfigCommandOutput;
    };
  };
}
