import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useFocusEffect } from '@react-navigation/native';
import { storageService, FavoriteRecipe } from '../services/StorageService';

export default function FavoritesScreen({ navigation }: any) {
  const [favorites, setFavorites] = useState<FavoriteRecipe[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const loadFavorites = async () => {
    try {
      const favoriteRecipes = await storageService.getFavorites();
      setFavorites(favoriteRecipes);
    } catch (error) {
      console.error('Favoriler yüklenirken hata:', error);
      Alert.alert('Hata', 'Favoriler yüklenirken bir hata oluştu.');
    } finally {
      setIsLoading(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      loadFavorites();
    }, [])
  );

  const onRefresh = async () => {
    setRefreshing(true);
    await loadFavorites();
    setRefreshing(false);
  };

  const handleRemoveFavorite = async (recipeId: string, recipeTitle: string) => {
    Alert.alert(
      'Favorilerden Kaldır',
      `"${recipeTitle}" tarifini favorilerden kaldırmak istediğinizden emin misiniz?`,
      [
        {
          text: 'İptal',
          style: 'cancel',
        },
        {
          text: 'Kaldır',
          style: 'destructive',
          onPress: async () => {
            try {
              await storageService.removeFromFavorites(recipeId);
              await loadFavorites();
              Alert.alert('Başarılı', 'Tarif favorilerden kaldırıldı.');
            } catch (error) {
              console.error('Favori kaldırılırken hata:', error);
              Alert.alert('Hata', 'Tarif kaldırılırken bir hata oluştu.');
            }
          },
        },
      ]
    );
  };

  const handleAddNewFavorite = () => {
    Alert.alert(
      'Yeni Favori Ekle',
      'Chatbot\'ta beğendiğiniz tarifleri favorilere ekleyebilirsiniz. Chatbot\'a gitmek ister misiniz?',
      [
        {
          text: 'İptal',
          style: 'cancel',
        },
        {
          text: 'Chatbot\'a Git',
          onPress: () => navigation.navigate('Chatbot'),
        },
      ]
    );
  };

  const renderFavoriteItem = (favorite: FavoriteRecipe) => (
    <View key={favorite.id} style={styles.favoriteCard}>
      <View style={styles.favoriteHeader}>
        <View style={styles.favoriteInfo}>
          <Text style={styles.favoriteTitle}>{favorite.title}</Text>
          <Text style={styles.favoriteDate}>
            {favorite.timestamp.toLocaleDateString('tr-TR')}
          </Text>
        </View>
        <TouchableOpacity
          style={styles.removeButton}
          onPress={() => handleRemoveFavorite(favorite.id, favorite.title)}
        >
          <Ionicons name="heart" size={24} color="#FF6B35" />
        </TouchableOpacity>
      </View>
      
      <Text style={styles.favoriteDescription} numberOfLines={3}>
        {favorite.description}
      </Text>
      
      <TouchableOpacity 
        style={styles.viewButton}
        onPress={() => {
          // Chatbot'a git ve bu tarifi sor
          navigation.navigate('Chatbot');
        }}
      >
        <Text style={styles.viewButtonText}>Detayları Gör</Text>
        <Ionicons name="arrow-forward" size={16} color="#FF6B35" />
      </TouchableOpacity>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="heart-outline" size={80} color="#ccc" />
      <Text style={styles.emptyTitle}>Henüz favori tarifiniz yok</Text>
      <Text style={styles.emptyDescription}>
        Chatbot'ta beğendiğiniz tarifleri favorilere ekleyerek buradan kolayca erişebilirsiniz.
      </Text>
      <TouchableOpacity style={styles.addButton} onPress={handleAddNewFavorite}>
        <Ionicons name="add" size={20} color="white" />
        <Text style={styles.addButtonText}>Tarif Keşfet</Text>
      </TouchableOpacity>
    </View>
  );

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Favoriler yükleniyor... ⌨️</Text>
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {favorites.length === 0 ? (
        renderEmptyState()
      ) : (
        <>
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.headerInfo}>
              <Text style={styles.headerTitle}>Favori Tariflerim</Text>
              <Text style={styles.headerSubtitle}>
                {favorites.length} tarif kayıtlı
              </Text>
            </View>
            <TouchableOpacity style={styles.addButton} onPress={handleAddNewFavorite}>
              <Ionicons name="add" size={20} color="white" />
            </TouchableOpacity>
          </View>

          {/* Favorites List */}
          <View style={styles.favoritesContainer}>
            {favorites.map(renderFavoriteItem)}
          </View>

          {/* Tips */}
          <View style={styles.tipsContainer}>
            <View style={styles.tipCard}>
              <Ionicons name="bulb" size={20} color="#FF6B35" />
              <Text style={styles.tipText}>
                💡 İpucu: Chatbot'ta "Bu tarifi favorilere ekle" diyerek beğendiğiniz tarifleri buraya kaydedebilirsiniz!
              </Text>
            </View>
          </View>
        </>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    fontStyle: 'italic',
  },
  header: {
    backgroundColor: 'white',
    padding: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  headerInfo: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  addButton: {
    backgroundColor: '#FF6B35',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  addButtonText: {
    color: 'white',
    fontWeight: 'bold',
    marginLeft: 5,
  },
  favoritesContainer: {
    paddingHorizontal: 20,
  },
  favoriteCard: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 15,
    marginBottom: 15,
    borderLeftWidth: 4,
    borderLeftColor: '#FF6B35',
  },
  favoriteHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 10,
  },
  favoriteInfo: {
    flex: 1,
  },
  favoriteTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 2,
  },
  favoriteDate: {
    fontSize: 12,
    color: '#999',
  },
  removeButton: {
    padding: 5,
  },
  favoriteDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 15,
  },
  viewButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFF3E0',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 8,
  },
  viewButtonText: {
    color: '#FF6B35',
    fontWeight: 'bold',
    marginRight: 5,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    backgroundColor: 'white',
    margin: 20,
    borderRadius: 15,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 20,
    marginBottom: 10,
  },
  emptyDescription: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 30,
  },
  tipsContainer: {
    padding: 20,
  },
  tipCard: {
    backgroundColor: '#FFF3E0',
    padding: 15,
    borderRadius: 10,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  tipText: {
    fontSize: 14,
    color: '#E65100',
    marginLeft: 10,
    flex: 1,
    lineHeight: 20,
  },
});
