import { Paginator } from "@smithy/types";
import { ListClustersCommandInput, ListClustersCommandOutput } from "../commands/ListClustersCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListClusters: (config: SageMakerPaginationConfiguration, input: ListClustersCommandInput, ...rest: any[]) => Paginator<ListClustersCommandOutput>;
