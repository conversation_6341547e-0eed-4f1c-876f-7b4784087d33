import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DeleteHubContentRequest } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteHubContentCommandInput extends DeleteHubContentRequest {}
export interface DeleteHubContentCommandOutput extends __MetadataBearer {}
declare const DeleteHubContentCommand_base: {
  new (
    input: DeleteHubContentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteHubContentCommandInput,
    DeleteHubContentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteHubContentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteHubContentCommandInput,
    DeleteHubContentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteHubContentCommand extends DeleteHubContentCommand_base {
  protected static __types: {
    api: {
      input: DeleteHubContentRequest;
      output: {};
    };
    sdk: {
      input: DeleteHubContentCommandInput;
      output: DeleteHubContentCommandOutput;
    };
  };
}
