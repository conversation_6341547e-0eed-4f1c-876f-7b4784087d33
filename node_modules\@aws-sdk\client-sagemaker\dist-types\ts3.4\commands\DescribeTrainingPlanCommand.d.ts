import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeTrainingPlanRequest,
  DescribeTrainingPlanResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeTrainingPlanCommandInput
  extends DescribeTrainingPlanRequest {}
export interface DescribeTrainingPlanCommandOutput
  extends DescribeTrainingPlanResponse,
    __MetadataBearer {}
declare const DescribeTrainingPlanCommand_base: {
  new (
    input: DescribeTrainingPlanCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeTrainingPlanCommandInput,
    DescribeTrainingPlanCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeTrainingPlanCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeTrainingPlanCommandInput,
    DescribeTrainingPlanCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeTrainingPlanCommand extends DescribeTrainingPlanCommand_base {
  protected static __types: {
    api: {
      input: DescribeTrainingPlanRequest;
      output: DescribeTrainingPlanResponse;
    };
    sdk: {
      input: DescribeTrainingPlanCommandInput;
      output: DescribeTrainingPlanCommandOutput;
    };
  };
}
