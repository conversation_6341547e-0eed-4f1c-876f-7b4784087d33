import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateAutoMLJobV2Request,
  CreateAutoMLJobV2Response,
} from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateAutoMLJobV2CommandInput
  extends CreateAutoMLJobV2Request {}
export interface CreateAutoMLJobV2CommandOutput
  extends CreateAutoMLJobV2Response,
    __MetadataBearer {}
declare const CreateAutoMLJobV2Command_base: {
  new (
    input: CreateAutoMLJobV2CommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateAutoMLJobV2CommandInput,
    CreateAutoMLJobV2CommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateAutoMLJobV2CommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateAutoMLJobV2CommandInput,
    CreateAutoMLJobV2CommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateAutoMLJobV2Command extends CreateAutoMLJobV2Command_base {
  protected static __types: {
    api: {
      input: CreateAutoMLJobV2Request;
      output: CreateAutoMLJobV2Response;
    };
    sdk: {
      input: CreateAutoMLJobV2CommandInput;
      output: CreateAutoMLJobV2CommandOutput;
    };
  };
}
