export declare const ALGORITHM_QUERY_PARAM = "X-Amz-Algorithm";
export declare const CREDENTIAL_QUERY_PARAM = "X-Amz-Credential";
export declare const AMZ_DATE_QUERY_PARAM = "X-Amz-Date";
export declare const SIGNED_HEADERS_QUERY_PARAM = "X-Amz-SignedHeaders";
export declare const EXPIRES_QUERY_PARAM = "X-Amz-Expires";
export declare const SIGNATURE_QUERY_PARAM = "X-Amz-Signature";
export declare const TOKEN_QUERY_PARAM = "X-Amz-Security-Token";
export declare const REGION_SET_PARAM = "X-Amz-Region-Set";
export declare const AUTH_HEADER = "authorization";
export declare const AMZ_DATE_HEADER: string;
export declare const DATE_HEADER = "date";
export declare const GENERATED_HEADERS: string[];
export declare const SIGNATURE_HEADER: string;
export declare const SHA256_HEADER = "x-amz-content-sha256";
export declare const TOKEN_HEADER: string;
export declare const HOST_HEADER = "host";
export declare const ALWAYS_UNSIGNABLE_HEADERS: {
    authorization: boolean;
    "cache-control": boolean;
    connection: boolean;
    expect: boolean;
    from: boolean;
    "keep-alive": boolean;
    "max-forwards": boolean;
    pragma: boolean;
    referer: boolean;
    te: boolean;
    trailer: boolean;
    "transfer-encoding": boolean;
    upgrade: boolean;
    "user-agent": boolean;
    "x-amzn-trace-id": boolean;
};
export declare const PROXY_HEADER_PATTERN: RegExp;
export declare const SEC_HEADER_PATTERN: RegExp;
export declare const UNSIGNABLE_PATTERNS: RegExp[];
export declare const ALGORITHM_IDENTIFIER = "AWS4-HMAC-SHA256";
export declare const ALGORITHM_IDENTIFIER_V4A = "AWS4-ECDSA-P256-SHA256";
export declare const EVENT_ALGORITHM_IDENTIFIER = "AWS4-HMAC-SHA256-PAYLOAD";
export declare const UNSIGNED_PAYLOAD = "UNSIGNED-PAYLOAD";
export declare const MAX_CACHE_SIZE = 50;
export declare const KEY_TYPE_IDENTIFIER = "aws4_request";
export declare const MAX_PRESIGNED_TTL: number;
