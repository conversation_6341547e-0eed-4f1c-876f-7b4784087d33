import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { RegisterDevicesRequest } from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface RegisterDevicesCommandInput extends RegisterDevicesRequest {}
export interface RegisterDevicesCommandOutput extends __MetadataBearer {}
declare const RegisterDevicesCommand_base: {
  new (
    input: RegisterDevicesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    RegisterDevicesCommandInput,
    RegisterDevicesCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: RegisterDevicesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    RegisterDevicesCommandInput,
    RegisterDevicesCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class RegisterDevicesCommand extends RegisterDevicesCommand_base {
  protected static __types: {
    api: {
      input: RegisterDevicesRequest;
      output: {};
    };
    sdk: {
      input: RegisterDevicesCommandInput;
      output: RegisterDevicesCommandOutput;
    };
  };
}
