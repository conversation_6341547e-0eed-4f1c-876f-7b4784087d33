import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  UpdateContextRequest,
  UpdateContextResponse,
} from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateContextCommandInput extends UpdateContextRequest {}
export interface UpdateContextCommandOutput
  extends UpdateContextResponse,
    __MetadataBearer {}
declare const UpdateContextCommand_base: {
  new (
    input: UpdateContextCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateContextCommandInput,
    UpdateContextCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateContextCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateContextCommandInput,
    UpdateContextCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateContextCommand extends UpdateContextCommand_base {
  protected static __types: {
    api: {
      input: UpdateContextRequest;
      output: UpdateContextResponse;
    };
    sdk: {
      input: UpdateContextCommandInput;
      output: UpdateContextCommandOutput;
    };
  };
}
