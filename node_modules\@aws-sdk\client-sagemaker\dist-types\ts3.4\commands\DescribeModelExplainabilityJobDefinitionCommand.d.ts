import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeModelExplainabilityJobDefinitionRequest,
  DescribeModelExplainabilityJobDefinitionResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeModelExplainabilityJobDefinitionCommandInput
  extends DescribeModelExplainabilityJobDefinitionRequest {}
export interface DescribeModelExplainabilityJobDefinitionCommandOutput
  extends DescribeModelExplainabilityJobDefinitionResponse,
    __MetadataBearer {}
declare const DescribeModelExplainabilityJobDefinitionCommand_base: {
  new (
    input: DescribeModelExplainabilityJobDefinitionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeModelExplainabilityJobDefinitionCommandInput,
    DescribeModelExplainabilityJobDefinitionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeModelExplainabilityJobDefinitionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeModelExplainabilityJobDefinitionCommandInput,
    DescribeModelExplainabilityJobDefinitionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeModelExplainabilityJobDefinitionCommand extends DescribeModelExplainabilityJobDefinitionCommand_base {
  protected static __types: {
    api: {
      input: DescribeModelExplainabilityJobDefinitionRequest;
      output: DescribeModelExplainabilityJobDefinitionResponse;
    };
    sdk: {
      input: DescribeModelExplainabilityJobDefinitionCommandInput;
      output: DescribeModelExplainabilityJobDefinitionCommandOutput;
    };
  };
}
