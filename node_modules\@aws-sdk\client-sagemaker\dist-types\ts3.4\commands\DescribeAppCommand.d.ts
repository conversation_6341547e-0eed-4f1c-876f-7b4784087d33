import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DescribeAppRequest, DescribeAppResponse } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeAppCommandInput extends DescribeAppRequest {}
export interface DescribeAppCommandOutput
  extends DescribeAppResponse,
    __MetadataBearer {}
declare const DescribeAppCommand_base: {
  new (
    input: DescribeAppCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeAppCommandInput,
    DescribeAppCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeAppCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeAppCommandInput,
    DescribeAppCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeAppCommand extends DescribeAppCommand_base {
  protected static __types: {
    api: {
      input: DescribeAppRequest;
      output: DescribeAppResponse;
    };
    sdk: {
      input: DescribeAppCommandInput;
      output: DescribeAppCommandOutput;
    };
  };
}
