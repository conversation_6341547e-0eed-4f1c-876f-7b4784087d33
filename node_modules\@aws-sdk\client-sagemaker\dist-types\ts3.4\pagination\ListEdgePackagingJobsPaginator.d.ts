import { Paginator } from "@smithy/types";
import {
  ListEdgePackagingJobsCommandInput,
  ListEdgePackagingJobsCommandOutput,
} from "../commands/ListEdgePackagingJobsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
export declare const paginateListEdgePackagingJobs: (
  config: SageMakerPaginationConfiguration,
  input: ListEdgePackagingJobsCommandInput,
  ...rest: any[]
) => Paginator<ListEdgePackagingJobsCommandOutput>;
