import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { StopEdgeDeploymentStageRequest } from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface StopEdgeDeploymentStageCommandInput
  extends StopEdgeDeploymentStageRequest {}
export interface StopEdgeDeploymentStageCommandOutput
  extends __MetadataBearer {}
declare const StopEdgeDeploymentStageCommand_base: {
  new (
    input: StopEdgeDeploymentStageCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StopEdgeDeploymentStageCommandInput,
    StopEdgeDeploymentStageCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: StopEdgeDeploymentStageCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StopEdgeDeploymentStageCommandInput,
    StopEdgeDeploymentStageCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class StopEdgeDeploymentStageCommand extends StopEdgeDeploymentStageCommand_base {
  protected static __types: {
    api: {
      input: StopEdgeDeploymentStageRequest;
      output: {};
    };
    sdk: {
      input: StopEdgeDeploymentStageCommandInput;
      output: StopEdgeDeploymentStageCommandOutput;
    };
  };
}
