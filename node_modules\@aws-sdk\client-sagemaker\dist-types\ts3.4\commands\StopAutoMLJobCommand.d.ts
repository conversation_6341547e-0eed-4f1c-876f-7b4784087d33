import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { StopAutoMLJobRequest } from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface StopAutoMLJobCommandInput extends StopAutoMLJobRequest {}
export interface StopAutoMLJobCommandOutput extends __MetadataBearer {}
declare const StopAutoMLJobCommand_base: {
  new (
    input: StopAutoMLJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StopAutoMLJobCommandInput,
    StopAutoMLJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: StopAutoMLJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StopAutoMLJobCommandInput,
    StopAutoMLJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class StopAutoMLJobCommand extends StopAutoMLJobCommand_base {
  protected static __types: {
    api: {
      input: StopAutoMLJobRequest;
      output: {};
    };
    sdk: {
      input: StopAutoMLJobCommandInput;
      output: StopAutoMLJobCommandOutput;
    };
  };
}
