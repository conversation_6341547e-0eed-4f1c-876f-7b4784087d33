import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeUserProfileRequest,
  DescribeUserProfileResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeUserProfileCommandInput
  extends DescribeUserProfileRequest {}
export interface DescribeUserProfileCommandOutput
  extends DescribeUserProfileResponse,
    __MetadataBearer {}
declare const DescribeUserProfileCommand_base: {
  new (
    input: DescribeUserProfileCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeUserProfileCommandInput,
    DescribeUserProfileCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeUserProfileCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeUserProfileCommandInput,
    DescribeUserProfileCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeUserProfileCommand extends DescribeUserProfileCommand_base {
  protected static __types: {
    api: {
      input: DescribeUserProfileRequest;
      output: DescribeUserProfileResponse;
    };
    sdk: {
      input: DescribeUserProfileCommandInput;
      output: DescribeUserProfileCommandOutput;
    };
  };
}
