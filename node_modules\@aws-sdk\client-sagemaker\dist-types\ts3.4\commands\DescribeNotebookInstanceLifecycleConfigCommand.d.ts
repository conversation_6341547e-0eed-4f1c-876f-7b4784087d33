import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeNotebookInstanceLifecycleConfigInput,
  DescribeNotebookInstanceLifecycleConfigOutput,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeNotebookInstanceLifecycleConfigCommandInput
  extends DescribeNotebookInstanceLifecycleConfigInput {}
export interface DescribeNotebookInstanceLifecycleConfigCommandOutput
  extends DescribeNotebookInstanceLifecycleConfigOutput,
    __MetadataBearer {}
declare const DescribeNotebookInstanceLifecycleConfigCommand_base: {
  new (
    input: DescribeNotebookInstanceLifecycleConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeNotebookInstanceLifecycleConfigCommandInput,
    DescribeNotebookInstanceLifecycleConfigCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeNotebookInstanceLifecycleConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeNotebookInstanceLifecycleConfigCommandInput,
    DescribeNotebookInstanceLifecycleConfigCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeNotebookInstanceLifecycleConfigCommand extends DescribeNotebookInstanceLifecycleConfigCommand_base {
  protected static __types: {
    api: {
      input: DescribeNotebookInstanceLifecycleConfigInput;
      output: DescribeNotebookInstanceLifecycleConfigOutput;
    };
    sdk: {
      input: DescribeNotebookInstanceLifecycleConfigCommandInput;
      output: DescribeNotebookInstanceLifecycleConfigCommandOutput;
    };
  };
}
