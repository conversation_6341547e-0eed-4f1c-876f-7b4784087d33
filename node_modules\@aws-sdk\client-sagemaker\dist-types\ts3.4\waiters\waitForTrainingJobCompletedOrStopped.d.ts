import { WaiterConfiguration, WaiterResult } from "@smithy/util-waiter";
import { DescribeTrainingJobCommandInput } from "../commands/DescribeTrainingJobCommand";
import { SageMakerClient } from "../SageMakerClient";
export declare const waitForTrainingJobCompletedOrStopped: (
  params: WaiterConfiguration<SageMakerClient>,
  input: DescribeTrainingJobCommandInput
) => Promise<WaiterResult>;
export declare const waitUntilTrainingJobCompletedOrStopped: (
  params: WaiterConfiguration<SageMakerClient>,
  input: DescribeTrainingJobCommandInput
) => Promise<WaiterResult>;
