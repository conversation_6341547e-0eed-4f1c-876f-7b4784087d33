import { WaiterConfiguration, WaiterResult } from "@smithy/util-waiter";
import { DescribeEndpointCommandInput } from "../commands/DescribeEndpointCommand";
import { SageMakerClient } from "../SageMakerClient";
/**
 *
 *  @deprecated Use waitUntilEndpointInService instead. waitForEndpointInService does not throw error in non-success cases.
 */
export declare const waitForEndpointInService: (params: WaiterConfiguration<SageMakerClient>, input: DescribeEndpointCommandInput) => Promise<WaiterResult>;
/**
 *
 *  @param params - Waiter configuration options.
 *  @param input - The input to DescribeEndpointCommand for polling.
 */
export declare const waitUntilEndpointInService: (params: WaiterConfiguration<SageMakerClient>, input: DescribeEndpointCommandInput) => Promise<WaiterResult>;
