import { Command as $Command } from "@smithy/smithy-client";
import { <PERSON>ada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DeleteModelPackageInput } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteModelPackageCommandInput
  extends DeleteModelPackageInput {}
export interface DeleteModelPackageCommandOutput extends __MetadataBearer {}
declare const DeleteModelPackageCommand_base: {
  new (
    input: DeleteModelPackageCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteModelPackageCommandInput,
    DeleteModelPackageCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteModelPackageCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteModelPackageCommandInput,
    DeleteModelPackageCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteModelPackageCommand extends DeleteModelPackageCommand_base {
  protected static __types: {
    api: {
      input: DeleteModelPackageInput;
      output: {};
    };
    sdk: {
      input: DeleteModelPackageCommandInput;
      output: DeleteModelPackageCommandOutput;
    };
  };
}
