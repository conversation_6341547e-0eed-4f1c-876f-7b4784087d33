import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  UpdateCodeRepositoryInput,
  UpdateCodeRepositoryOutput,
} from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateCodeRepositoryCommandInput
  extends UpdateCodeRepositoryInput {}
export interface UpdateCodeRepositoryCommandOutput
  extends UpdateCodeRepositoryOutput,
    __MetadataBearer {}
declare const UpdateCodeRepositoryCommand_base: {
  new (
    input: UpdateCodeRepositoryCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateCodeRepositoryCommandInput,
    UpdateCodeRepositoryCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateCodeRepositoryCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateCodeRepositoryCommandInput,
    UpdateCodeRepositoryCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateCodeRepositoryCommand extends UpdateCodeRepositoryCommand_base {
  protected static __types: {
    api: {
      input: UpdateCodeRepositoryInput;
      output: UpdateCodeRepositoryOutput;
    };
    sdk: {
      input: UpdateCodeRepositoryCommandInput;
      output: UpdateCodeRepositoryCommandOutput;
    };
  };
}
