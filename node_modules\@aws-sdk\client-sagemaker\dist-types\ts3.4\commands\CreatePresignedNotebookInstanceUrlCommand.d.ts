import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreatePresignedNotebookInstanceUrlInput,
  CreatePresignedNotebookInstanceUrlOutput,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreatePresignedNotebookInstanceUrlCommandInput
  extends CreatePresignedNotebookInstanceUrlInput {}
export interface CreatePresignedNotebookInstanceUrlCommandOutput
  extends CreatePresignedNotebookInstanceUrlOutput,
    __MetadataBearer {}
declare const CreatePresignedNotebookInstanceUrlCommand_base: {
  new (
    input: CreatePresignedNotebookInstanceUrlCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreatePresignedNotebookInstanceUrlCommandInput,
    CreatePresignedNotebookInstanceUrlCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreatePresignedNotebookInstanceUrlCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreatePresignedNotebookInstanceUrlCommandInput,
    CreatePresignedNotebookInstanceUrlCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreatePresignedNotebookInstanceUrlCommand extends CreatePresignedNotebookInstanceUrlCommand_base {
  protected static __types: {
    api: {
      input: CreatePresignedNotebookInstanceUrlInput;
      output: CreatePresignedNotebookInstanceUrlOutput;
    };
    sdk: {
      input: CreatePresignedNotebookInstanceUrlCommandInput;
      output: CreatePresignedNotebookInstanceUrlCommandOutput;
    };
  };
}
