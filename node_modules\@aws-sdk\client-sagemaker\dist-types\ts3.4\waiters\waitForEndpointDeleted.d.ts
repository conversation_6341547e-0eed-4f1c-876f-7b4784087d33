import { WaiterConfiguration, WaiterResult } from "@smithy/util-waiter";
import { DescribeEndpointCommandInput } from "../commands/DescribeEndpointCommand";
import { SageMakerClient } from "../SageMakerClient";
export declare const waitForEndpointDeleted: (
  params: WaiterConfiguration<SageMakerClient>,
  input: DescribeEndpointCommandInput
) => Promise<WaiterResult>;
export declare const waitUntilEndpointDeleted: (
  params: WaiterConfiguration<SageMakerClient>,
  input: DescribeEndpointCommandInput
) => Promise<WaiterResult>;
