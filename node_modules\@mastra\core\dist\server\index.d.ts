import { <PERSON><PERSON>, Context, MiddlewareHandler } from 'hono';
import { DescribeRouteOptions } from 'hono-openapi';
import { M as Methods, a as Mastra, A as ApiRoute, b as MastraAuthConfig } from '../base-QP4OC4dB.js';
export { C as ContextWithMastra } from '../base-QP4OC4dB.js';
import 'ai';
import '../base-tc5kgDTD.js';
import '@opentelemetry/api';
import '../logger-EhZkzZOr.js';
import 'stream';
import '@opentelemetry/sdk-trace-base';
import '../types-Bo1uigWx.js';
import 'sift';
import 'zod';
import 'json-schema';
import '../deployer/index.js';
import '../bundler/index.js';
import 'node:http';
import '../runtime-context/index.js';
import '../tts/index.js';
import '../vector/index.js';
import '../vector/filter/index.js';
import 'xstate';
import 'node:events';
import 'events';
import '../workflows/constants.js';
import 'hono/cors';
import 'ai/test';

type ParamsFromPath<P extends string> = {
    [K in P extends `${string}:${infer Param}/${string}` | `${string}:${infer Param}` ? Param : never]: string;
};
declare function registerApiRoute<P extends string>(path: P, options: P extends `/api/${string}` ? never : {
    method: Methods;
    openapi?: DescribeRouteOptions;
    handler?: Handler<{
        Variables: {
            mastra: Mastra;
        };
    }, P, ParamsFromPath<P>>;
    createHandler?: (c: Context) => Promise<Handler<{
        Variables: {
            mastra: Mastra;
        };
    }, P, ParamsFromPath<P>>>;
    middleware?: MiddlewareHandler | MiddlewareHandler[];
}): P extends `/api/${string}` ? never : ApiRoute;
declare function defineAuth<TUser>(config: MastraAuthConfig<TUser>): MastraAuthConfig<TUser>;

export { MastraAuthConfig, defineAuth, registerApiRoute };
