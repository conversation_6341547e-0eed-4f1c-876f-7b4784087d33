import { Paginator } from "@smithy/types";
import {
  ListEdgeDeploymentPlansCommandInput,
  ListEdgeDeploymentPlansCommandOutput,
} from "../commands/ListEdgeDeploymentPlansCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
export declare const paginateListEdgeDeploymentPlans: (
  config: SageMakerPaginationConfiguration,
  input: ListEdgeDeploymentPlansCommandInput,
  ...rest: any[]
) => Paginator<ListEdgeDeploymentPlansCommandOutput>;
