import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { DeleteModelBiasJobDefinitionRequest } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteModelBiasJobDefinitionCommandInput
  extends DeleteModelBiasJobDefinitionRequest {}
export interface DeleteModelBiasJobDefinitionCommandOutput
  extends __MetadataBearer {}
declare const DeleteModelBiasJobDefinitionCommand_base: {
  new (
    input: DeleteModelBiasJobDefinitionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteModelBiasJobDefinitionCommandInput,
    DeleteModelBiasJobDefinitionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteModelBiasJobDefinitionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteModelBiasJobDefinitionCommandInput,
    DeleteModelBiasJobDefinitionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteModelBiasJobDefinitionCommand extends DeleteModelBiasJobDefinitionCommand_base {
  protected static __types: {
    api: {
      input: DeleteModelBiasJobDefinitionRequest;
      output: {};
    };
    sdk: {
      input: DeleteModelBiasJobDefinitionCommandInput;
      output: DeleteModelBiasJobDefinitionCommandOutput;
    };
  };
}
