import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeEndpointInput,
  DescribeEndpointOutput,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeEndpointCommandInput extends DescribeEndpointInput {}
export interface DescribeEndpointCommandOutput
  extends DescribeEndpointOutput,
    __MetadataBearer {}
declare const DescribeEndpointCommand_base: {
  new (
    input: DescribeEndpointCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeEndpointCommandInput,
    DescribeEndpointCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeEndpointCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeEndpointCommandInput,
    DescribeEndpointCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeEndpointCommand extends DescribeEndpointCommand_base {
  protected static __types: {
    api: {
      input: DescribeEndpointInput;
      output: DescribeEndpointOutput;
    };
    sdk: {
      input: DescribeEndpointCommandInput;
      output: DescribeEndpointCommandOutput;
    };
  };
}
