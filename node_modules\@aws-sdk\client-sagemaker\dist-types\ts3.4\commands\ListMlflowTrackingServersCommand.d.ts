import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  ListMlflowTrackingServersRequest,
  ListMlflowTrackingServersResponse,
} from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface ListMlflowTrackingServersCommandInput
  extends ListMlflowTrackingServersRequest {}
export interface ListMlflowTrackingServersCommandOutput
  extends ListMlflowTrackingServersResponse,
    __MetadataBearer {}
declare const ListMlflowTrackingServersCommand_base: {
  new (
    input: ListMlflowTrackingServersCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListMlflowTrackingServersCommandInput,
    ListMlflowTrackingServersCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    ...[input]: [] | [ListMlflowTrackingServersCommandInput]
  ): import("@smithy/smithy-client").CommandImpl<
    ListMlflowTrackingServersCommandInput,
    ListMlflowTrackingServersCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListMlflowTrackingServersCommand extends ListMlflowTrackingServersCommand_base {
  protected static __types: {
    api: {
      input: ListMlflowTrackingServersRequest;
      output: ListMlflowTrackingServersResponse;
    };
    sdk: {
      input: ListMlflowTrackingServersCommandInput;
      output: ListMlflowTrackingServersCommandOutput;
    };
  };
}
