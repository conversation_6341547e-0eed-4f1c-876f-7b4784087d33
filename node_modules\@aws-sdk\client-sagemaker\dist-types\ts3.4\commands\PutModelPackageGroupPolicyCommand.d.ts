import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  PutModelPackageGroupPolicyInput,
  PutModelPackageGroupPolicyOutput,
} from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface PutModelPackageGroupPolicyCommandInput
  extends PutModelPackageGroupPolicyInput {}
export interface PutModelPackageGroupPolicyCommandOutput
  extends PutModelPackageGroupPolicyOutput,
    __MetadataBearer {}
declare const PutModelPackageGroupPolicyCommand_base: {
  new (
    input: PutModelPackageGroupPolicyCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutModelPackageGroupPolicyCommandInput,
    PutModelPackageGroupPolicyCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: PutModelPackageGroupPolicyCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutModelPackageGroupPolicyCommandInput,
    PutModelPackageGroupPolicyCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class PutModelPackageGroupPolicyCommand extends PutModelPackageGroupPolicyCommand_base {
  protected static __types: {
    api: {
      input: PutModelPackageGroupPolicyInput;
      output: PutModelPackageGroupPolicyOutput;
    };
    sdk: {
      input: PutModelPackageGroupPolicyCommandInput;
      output: PutModelPackageGroupPolicyCommandOutput;
    };
  };
}
