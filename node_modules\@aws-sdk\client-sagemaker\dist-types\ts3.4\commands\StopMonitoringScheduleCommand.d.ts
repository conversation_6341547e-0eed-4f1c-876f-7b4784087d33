import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { StopMonitoringScheduleRequest } from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface StopMonitoringScheduleCommandInput
  extends StopMonitoringScheduleRequest {}
export interface StopMonitoringScheduleCommandOutput extends __MetadataBearer {}
declare const StopMonitoringScheduleCommand_base: {
  new (
    input: StopMonitoringScheduleCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StopMonitoringScheduleCommandInput,
    StopMonitoringScheduleCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: StopMonitoringScheduleCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StopMonitoringScheduleCommandInput,
    StopMonitoringScheduleCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class StopMonitoringScheduleCommand extends StopMonitoringScheduleCommand_base {
  protected static __types: {
    api: {
      input: StopMonitoringScheduleRequest;
      output: {};
    };
    sdk: {
      input: StopMonitoringScheduleCommandInput;
      output: StopMonitoringScheduleCommandOutput;
    };
  };
}
