import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  UpdateUserProfileRequest,
  UpdateUserProfileResponse,
} from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateUserProfileCommandInput
  extends UpdateUserProfileRequest {}
export interface UpdateUserProfileCommandOutput
  extends UpdateUserProfileResponse,
    __MetadataBearer {}
declare const UpdateUserProfileCommand_base: {
  new (
    input: UpdateUserProfileCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateUserProfileCommandInput,
    UpdateUserProfileCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateUserProfileCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateUserProfileCommandInput,
    UpdateUserProfileCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateUserProfileCommand extends UpdateUserProfileCommand_base {
  protected static __types: {
    api: {
      input: UpdateUserProfileRequest;
      output: UpdateUserProfileResponse;
    };
    sdk: {
      input: UpdateUserProfileCommandInput;
      output: UpdateUserProfileCommandOutput;
    };
  };
}
