import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  UpdatePipelineRequest,
  UpdatePipelineResponse,
} from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdatePipelineCommandInput extends UpdatePipelineRequest {}
export interface UpdatePipelineCommandOutput
  extends UpdatePipelineResponse,
    __MetadataBearer {}
declare const UpdatePipelineCommand_base: {
  new (
    input: UpdatePipelineCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdatePipelineCommandInput,
    UpdatePipelineCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdatePipelineCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdatePipelineCommandInput,
    UpdatePipelineCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdatePipelineCommand extends UpdatePipelineCommand_base {
  protected static __types: {
    api: {
      input: UpdatePipelineRequest;
      output: UpdatePipelineResponse;
    };
    sdk: {
      input: UpdatePipelineCommandInput;
      output: UpdatePipelineCommandOutput;
    };
  };
}
