import { Command as $Command } from "@smithy/smithy-client";
import { <PERSON>ada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DescribeModelInput, DescribeModelOutput } from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeModelCommandInput extends DescribeModelInput {}
export interface DescribeModelCommandOutput
  extends DescribeModelOutput,
    __MetadataBearer {}
declare const DescribeModelCommand_base: {
  new (
    input: DescribeModelCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeModelCommandInput,
    DescribeModelCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeModelCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeModelCommandInput,
    DescribeModelCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeModelCommand extends DescribeModelCommand_base {
  protected static __types: {
    api: {
      input: DescribeModelInput;
      output: DescribeModelOutput;
    };
    sdk: {
      input: DescribeModelCommandInput;
      output: DescribeModelCommandOutput;
    };
  };
}
