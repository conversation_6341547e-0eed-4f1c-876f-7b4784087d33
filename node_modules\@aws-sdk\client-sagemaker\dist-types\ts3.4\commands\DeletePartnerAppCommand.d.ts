import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  DeletePartnerAppRequest,
  DeletePartnerAppResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeletePartnerAppCommandInput extends DeletePartnerAppRequest {}
export interface DeletePartnerAppCommandOutput
  extends DeletePartnerAppResponse,
    __MetadataBearer {}
declare const DeletePartnerAppCommand_base: {
  new (
    input: DeletePartnerAppCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeletePartnerAppCommandInput,
    DeletePartnerAppCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeletePartnerAppCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeletePartnerAppCommandInput,
    DeletePartnerAppCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeletePartnerAppCommand extends DeletePartnerAppCommand_base {
  protected static __types: {
    api: {
      input: DeletePartnerAppRequest;
      output: DeletePartnerAppResponse;
    };
    sdk: {
      input: DeletePartnerAppCommandInput;
      output: DeletePartnerAppCommandOutput;
    };
  };
}
