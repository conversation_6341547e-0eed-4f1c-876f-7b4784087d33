import { Paginator } from "@smithy/types";
import {
  ListWorkforcesCommandInput,
  ListWorkforcesCommandOutput,
} from "../commands/ListWorkforcesCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
export declare const paginateListWorkforces: (
  config: SageMakerPaginationConfiguration,
  input: ListWorkforcesCommandInput,
  ...rest: any[]
) => Paginator<ListWorkforcesCommandOutput>;
