import { Paginator } from "@smithy/types";
import { ListCodeRepositoriesCommandInput, ListCodeRepositoriesCommandOutput } from "../commands/ListCodeRepositoriesCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListCodeRepositories: (config: SageMakerPaginationConfiguration, input: ListCodeRepositoriesCommandInput, ...rest: any[]) => Paginator<ListCodeRepositoriesCommandOutput>;
