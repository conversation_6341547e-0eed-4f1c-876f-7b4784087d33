import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeClusterNodeRequest,
  DescribeClusterNodeResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeClusterNodeCommandInput
  extends DescribeClusterNodeRequest {}
export interface DescribeClusterNodeCommandOutput
  extends DescribeClusterNodeResponse,
    __MetadataBearer {}
declare const DescribeClusterNodeCommand_base: {
  new (
    input: DescribeClusterNodeCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeClusterNodeCommandInput,
    DescribeClusterNodeCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeClusterNodeCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeClusterNodeCommandInput,
    DescribeClusterNodeCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeClusterNodeCommand extends DescribeClusterNodeCommand_base {
  protected static __types: {
    api: {
      input: DescribeClusterNodeRequest;
      output: DescribeClusterNodeResponse;
    };
    sdk: {
      input: DescribeClusterNodeCommandInput;
      output: DescribeClusterNodeCommandOutput;
    };
  };
}
