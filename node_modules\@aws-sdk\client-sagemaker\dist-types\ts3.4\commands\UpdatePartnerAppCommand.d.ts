import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  UpdatePartnerAppRequest,
  UpdatePartnerAppResponse,
} from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdatePartnerAppCommandInput extends UpdatePartnerAppRequest {}
export interface UpdatePartnerAppCommandOutput
  extends UpdatePartnerAppResponse,
    __MetadataBearer {}
declare const UpdatePartnerAppCommand_base: {
  new (
    input: UpdatePartnerAppCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdatePartnerAppCommandInput,
    UpdatePartnerAppCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdatePartnerAppCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdatePartnerAppCommandInput,
    UpdatePartnerAppCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdatePartnerAppCommand extends UpdatePartnerAppCommand_base {
  protected static __types: {
    api: {
      input: UpdatePartnerAppRequest;
      output: UpdatePartnerAppResponse;
    };
    sdk: {
      input: UpdatePartnerAppCommandInput;
      output: UpdatePartnerAppCommandOutput;
    };
  };
}
