import { Command as $Command } from "@smithy/smithy-client";
import { <PERSON>ada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DeleteEndpointConfigInput } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteEndpointConfigCommandInput
  extends DeleteEndpointConfigInput {}
export interface DeleteEndpointConfigCommandOutput extends __MetadataBearer {}
declare const DeleteEndpointConfigCommand_base: {
  new (
    input: DeleteEndpointConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteEndpointConfigCommandInput,
    DeleteEndpointConfigCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteEndpointConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteEndpointConfigCommandInput,
    DeleteEndpointConfigCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteEndpointConfigCommand extends DeleteEndpointConfigCommand_base {
  protected static __types: {
    api: {
      input: DeleteEndpointConfigInput;
      output: {};
    };
    sdk: {
      input: DeleteEndpointConfigCommandInput;
      output: DeleteEndpointConfigCommandOutput;
    };
  };
}
