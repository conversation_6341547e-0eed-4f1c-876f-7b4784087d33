import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeAutoMLJobV2Request,
  DescribeAutoMLJobV2Response,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeAutoMLJobV2CommandInput
  extends DescribeAutoMLJobV2Request {}
export interface DescribeAutoMLJobV2CommandOutput
  extends DescribeAutoMLJobV2Response,
    __MetadataBearer {}
declare const DescribeAutoMLJobV2Command_base: {
  new (
    input: DescribeAutoMLJobV2CommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeAutoMLJobV2CommandInput,
    DescribeAutoMLJobV2CommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeAutoMLJobV2CommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeAutoMLJobV2CommandInput,
    DescribeAutoMLJobV2CommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeAutoMLJobV2Command extends DescribeAutoMLJobV2Command_base {
  protected static __types: {
    api: {
      input: DescribeAutoMLJobV2Request;
      output: DescribeAutoMLJobV2Response;
    };
    sdk: {
      input: DescribeAutoMLJobV2CommandInput;
      output: DescribeAutoMLJobV2CommandOutput;
    };
  };
}
