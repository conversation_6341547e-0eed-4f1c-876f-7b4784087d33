import { Paginator } from "@smithy/types";
import {
  ListInferenceExperimentsCommandInput,
  ListInferenceExperimentsCommandOutput,
} from "../commands/ListInferenceExperimentsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
export declare const paginateListInferenceExperiments: (
  config: SageMakerPaginationConfiguration,
  input: ListInferenceExperimentsCommandInput,
  ...rest: any[]
) => Paginator<ListInferenceExperimentsCommandOutput>;
