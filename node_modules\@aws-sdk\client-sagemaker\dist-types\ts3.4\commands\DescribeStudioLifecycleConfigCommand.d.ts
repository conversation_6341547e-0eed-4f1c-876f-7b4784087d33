import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeStudioLifecycleConfigRequest,
  DescribeStudioLifecycleConfigResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeStudioLifecycleConfigCommandInput
  extends DescribeStudioLifecycleConfigRequest {}
export interface DescribeStudioLifecycleConfigCommandOutput
  extends DescribeStudioLifecycleConfigResponse,
    __MetadataBearer {}
declare const DescribeStudioLifecycleConfigCommand_base: {
  new (
    input: DescribeStudioLifecycleConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeStudioLifecycleConfigCommandInput,
    DescribeStudioLifecycleConfigCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeStudioLifecycleConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeStudioLifecycleConfigCommandInput,
    DescribeStudioLifecycleConfigCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeStudioLifecycleConfigCommand extends DescribeStudioLifecycleConfigCommand_base {
  protected static __types: {
    api: {
      input: DescribeStudioLifecycleConfigRequest;
      output: DescribeStudioLifecycleConfigResponse;
    };
    sdk: {
      input: DescribeStudioLifecycleConfigCommandInput;
      output: DescribeStudioLifecycleConfigCommandOutput;
    };
  };
}
