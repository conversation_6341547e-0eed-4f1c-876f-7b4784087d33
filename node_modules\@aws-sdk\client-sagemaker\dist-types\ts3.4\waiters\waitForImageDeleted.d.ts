import { WaiterConfiguration, WaiterResult } from "@smithy/util-waiter";
import { DescribeImageCommandInput } from "../commands/DescribeImageCommand";
import { SageMakerClient } from "../SageMakerClient";
export declare const waitForImageDeleted: (
  params: WaiterConfiguration<SageMakerClient>,
  input: DescribeImageCommandInput
) => Promise<WaiterResult>;
export declare const waitUntilImageDeleted: (
  params: WaiterConfiguration<SageMakerClient>,
  input: DescribeImageCommandInput
) => Promise<WaiterResult>;
