import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { UpdateProjectInput, UpdateProjectOutput } from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateProjectCommandInput extends UpdateProjectInput {}
export interface UpdateProjectCommandOutput
  extends UpdateProjectOutput,
    __MetadataBearer {}
declare const UpdateProjectCommand_base: {
  new (
    input: UpdateProjectCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateProjectCommandInput,
    UpdateProjectCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateProjectCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateProjectCommandInput,
    UpdateProjectCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateProjectCommand extends UpdateProjectCommand_base {
  protected static __types: {
    api: {
      input: UpdateProjectInput;
      output: UpdateProjectOutput;
    };
    sdk: {
      input: UpdateProjectCommandInput;
      output: UpdateProjectCommandOutput;
    };
  };
}
