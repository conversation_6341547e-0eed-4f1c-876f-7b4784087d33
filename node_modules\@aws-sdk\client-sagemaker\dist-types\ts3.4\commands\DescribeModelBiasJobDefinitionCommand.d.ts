import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeModelBiasJobDefinitionRequest,
  DescribeModelBiasJobDefinitionResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeModelBiasJobDefinitionCommandInput
  extends DescribeModelBiasJobDefinitionRequest {}
export interface DescribeModelBiasJobDefinitionCommandOutput
  extends DescribeModelBiasJobDefinitionResponse,
    __MetadataBearer {}
declare const DescribeModelBiasJobDefinitionCommand_base: {
  new (
    input: DescribeModelBiasJobDefinitionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeModelBiasJobDefinitionCommandInput,
    DescribeModelBiasJobDefinitionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeModelBiasJobDefinitionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeModelBiasJobDefinitionCommandInput,
    DescribeModelBiasJobDefinitionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeModelBiasJobDefinitionCommand extends DescribeModelBiasJobDefinitionCommand_base {
  protected static __types: {
    api: {
      input: DescribeModelBiasJobDefinitionRequest;
      output: DescribeModelBiasJobDefinitionResponse;
    };
    sdk: {
      input: DescribeModelBiasJobDefinitionCommandInput;
      output: DescribeModelBiasJobDefinitionCommandOutput;
    };
  };
}
