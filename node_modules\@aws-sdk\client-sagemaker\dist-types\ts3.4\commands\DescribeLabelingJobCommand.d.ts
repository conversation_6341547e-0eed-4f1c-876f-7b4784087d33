import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeLabelingJobRequest,
  DescribeLabelingJobResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeLabelingJobCommandInput
  extends DescribeLabelingJobRequest {}
export interface DescribeLabelingJobCommandOutput
  extends DescribeLabelingJobResponse,
    __MetadataBearer {}
declare const DescribeLabelingJobCommand_base: {
  new (
    input: DescribeLabelingJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeLabelingJobCommandInput,
    DescribeLabelingJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeLabelingJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeLabelingJobCommandInput,
    DescribeLabelingJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeLabelingJobCommand extends DescribeLabelingJobCommand_base {
  protected static __types: {
    api: {
      input: DescribeLabelingJobRequest;
      output: DescribeLabelingJobResponse;
    };
    sdk: {
      input: DescribeLabelingJobCommandInput;
      output: DescribeLabelingJobCommandOutput;
    };
  };
}
