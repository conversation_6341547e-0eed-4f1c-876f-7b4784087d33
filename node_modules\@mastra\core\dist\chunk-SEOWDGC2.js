// src/storage/constants.ts
var TABLE_WORKFLOW_SNAPSHOT = "mastra_workflow_snapshot";
var TABLE_EVALS = "mastra_evals";
var TABLE_MESSAGES = "mastra_messages";
var TABLE_THREADS = "mastra_threads";
var TABLE_TRACES = "mastra_traces";
var TABLE_SCHEMAS = {
  [TABLE_WORKFLOW_SNAPSHOT]: {
    workflow_name: {
      type: "text"
    },
    run_id: {
      type: "text"
    },
    resourceId: { type: "text", nullable: true },
    snapshot: {
      type: "text"
    },
    createdAt: {
      type: "timestamp"
    },
    updatedAt: {
      type: "timestamp"
    }
  },
  [TABLE_EVALS]: {
    input: {
      type: "text"
    },
    output: {
      type: "text"
    },
    result: {
      type: "jsonb"
    },
    agent_name: {
      type: "text"
    },
    metric_name: {
      type: "text"
    },
    instructions: {
      type: "text"
    },
    test_info: {
      type: "jsonb",
      nullable: true
    },
    global_run_id: {
      type: "text"
    },
    run_id: {
      type: "text"
    },
    created_at: {
      type: "timestamp"
    },
    createdAt: {
      type: "timestamp",
      nullable: true
    }
  },
  [TABLE_THREADS]: {
    id: { type: "text", nullable: false, primaryKey: true },
    resourceId: { type: "text", nullable: false },
    title: { type: "text", nullable: false },
    metadata: { type: "text", nullable: true },
    createdAt: { type: "timestamp", nullable: false },
    updatedAt: { type: "timestamp", nullable: false }
  },
  [TABLE_MESSAGES]: {
    id: { type: "text", nullable: false, primaryKey: true },
    thread_id: { type: "text", nullable: false },
    content: { type: "text", nullable: false },
    role: { type: "text", nullable: false },
    type: { type: "text", nullable: false },
    createdAt: { type: "timestamp", nullable: false }
  },
  [TABLE_TRACES]: {
    id: { type: "text", nullable: false, primaryKey: true },
    parentSpanId: { type: "text", nullable: true },
    name: { type: "text", nullable: false },
    traceId: { type: "text", nullable: false },
    scope: { type: "text", nullable: false },
    kind: { type: "integer", nullable: false },
    attributes: { type: "jsonb", nullable: true },
    status: { type: "jsonb", nullable: true },
    events: { type: "jsonb", nullable: true },
    links: { type: "jsonb", nullable: true },
    other: { type: "text", nullable: true },
    startTime: { type: "bigint", nullable: false },
    endTime: { type: "bigint", nullable: false },
    createdAt: { type: "timestamp", nullable: false }
  }
};

export { TABLE_EVALS, TABLE_MESSAGES, TABLE_SCHEMAS, TABLE_THREADS, TABLE_TRACES, TABLE_WORKFLOW_SNAPSHOT };
