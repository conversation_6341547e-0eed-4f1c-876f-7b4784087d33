import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeInferenceComponentInput,
  DescribeInferenceComponentOutput,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeInferenceComponentCommandInput
  extends DescribeInferenceComponentInput {}
export interface DescribeInferenceComponentCommandOutput
  extends DescribeInferenceComponentOutput,
    __MetadataBearer {}
declare const DescribeInferenceComponentCommand_base: {
  new (
    input: DescribeInferenceComponentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeInferenceComponentCommandInput,
    DescribeInferenceComponentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeInferenceComponentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeInferenceComponentCommandInput,
    DescribeInferenceComponentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeInferenceComponentCommand extends DescribeInferenceComponentCommand_base {
  protected static __types: {
    api: {
      input: DescribeInferenceComponentInput;
      output: DescribeInferenceComponentOutput;
    };
    sdk: {
      input: DescribeInferenceComponentCommandInput;
      output: DescribeInferenceComponentCommandOutput;
    };
  };
}
