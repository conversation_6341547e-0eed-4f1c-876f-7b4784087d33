import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateWorkforceRequest,
  CreateWorkforceResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateWorkforceCommandInput extends CreateWorkforceRequest {}
export interface CreateWorkforceCommandOutput
  extends CreateWorkforceResponse,
    __MetadataBearer {}
declare const CreateWorkforceCommand_base: {
  new (
    input: CreateWorkforceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateWorkforceCommandInput,
    CreateWorkforceCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateWorkforceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateWorkforceCommandInput,
    CreateWorkforceCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateWorkforceCommand extends CreateWorkforceCommand_base {
  protected static __types: {
    api: {
      input: CreateWorkforceRequest;
      output: CreateWorkforceResponse;
    };
    sdk: {
      input: CreateWorkforceCommandInput;
      output: CreateWorkforceCommandOutput;
    };
  };
}
