import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { CreateEdgeDeploymentStageRequest } from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateEdgeDeploymentStageCommandInput
  extends CreateEdgeDeploymentStageRequest {}
export interface CreateEdgeDeploymentStageCommandOutput
  extends __MetadataBearer {}
declare const CreateEdgeDeploymentStageCommand_base: {
  new (
    input: CreateEdgeDeploymentStageCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateEdgeDeploymentStageCommandInput,
    CreateEdgeDeploymentStageCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateEdgeDeploymentStageCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateEdgeDeploymentStageCommandInput,
    CreateEdgeDeploymentStageCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateEdgeDeploymentStageCommand extends CreateEdgeDeploymentStageCommand_base {
  protected static __types: {
    api: {
      input: CreateEdgeDeploymentStageRequest;
      output: {};
    };
    sdk: {
      input: CreateEdgeDeploymentStageCommandInput;
      output: CreateEdgeDeploymentStageCommandOutput;
    };
  };
}
