import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateUserProfileRequest,
  CreateUserProfileResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateUserProfileCommandInput
  extends CreateUserProfileRequest {}
export interface CreateUserProfileCommandOutput
  extends CreateUserProfileResponse,
    __MetadataBearer {}
declare const CreateUserProfileCommand_base: {
  new (
    input: CreateUserProfileCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateUserProfileCommandInput,
    CreateUserProfileCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateUserProfileCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateUserProfileCommandInput,
    CreateUserProfileCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateUserProfileCommand extends CreateUserProfileCommand_base {
  protected static __types: {
    api: {
      input: CreateUserProfileRequest;
      output: CreateUserProfileResponse;
    };
    sdk: {
      input: CreateUserProfileCommandInput;
      output: CreateUserProfileCommandOutput;
    };
  };
}
