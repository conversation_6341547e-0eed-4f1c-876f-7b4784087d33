import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ListNotebookInstanceLifecycleConfigsInput,
  ListNotebookInstanceLifecycleConfigsOutput,
} from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface ListNotebookInstanceLifecycleConfigsCommandInput
  extends ListNotebookInstanceLifecycleConfigsInput {}
export interface ListNotebookInstanceLifecycleConfigsCommandOutput
  extends ListNotebookInstanceLifecycleConfigsOutput,
    __MetadataBearer {}
declare const ListNotebookInstanceLifecycleConfigsCommand_base: {
  new (
    input: ListNotebookInstanceLifecycleConfigsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListNotebookInstanceLifecycleConfigsCommandInput,
    ListNotebookInstanceLifecycleConfigsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    ...[input]: [] | [ListNotebookInstanceLifecycleConfigsCommandInput]
  ): import("@smithy/smithy-client").CommandImpl<
    ListNotebookInstanceLifecycleConfigsCommandInput,
    ListNotebookInstanceLifecycleConfigsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListNotebookInstanceLifecycleConfigsCommand extends ListNotebookInstanceLifecycleConfigsCommand_base {
  protected static __types: {
    api: {
      input: ListNotebookInstanceLifecycleConfigsInput;
      output: ListNotebookInstanceLifecycleConfigsOutput;
    };
    sdk: {
      input: ListNotebookInstanceLifecycleConfigsCommandInput;
      output: ListNotebookInstanceLifecycleConfigsCommandOutput;
    };
  };
}
