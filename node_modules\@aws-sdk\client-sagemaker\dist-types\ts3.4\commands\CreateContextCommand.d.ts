import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateContextRequest,
  CreateContextResponse,
} from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateContextCommandInput extends CreateContextRequest {}
export interface CreateContextCommandOutput
  extends CreateContextResponse,
    __MetadataBearer {}
declare const CreateContextCommand_base: {
  new (
    input: CreateContextCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateContextCommandInput,
    CreateContextCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateContextCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateContextCommandInput,
    CreateContextCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateContextCommand extends CreateContextCommand_base {
  protected static __types: {
    api: {
      input: CreateContextRequest;
      output: CreateContextResponse;
    };
    sdk: {
      input: CreateContextCommandInput;
      output: CreateContextCommandOutput;
    };
  };
}
