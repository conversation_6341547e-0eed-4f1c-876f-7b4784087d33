import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  UpdateMonitoringAlertRequest,
  UpdateMonitoringAlertResponse,
} from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateMonitoringAlertCommandInput
  extends UpdateMonitoringAlertRequest {}
export interface UpdateMonitoringAlertCommandOutput
  extends UpdateMonitoringAlertResponse,
    __MetadataBearer {}
declare const UpdateMonitoringAlertCommand_base: {
  new (
    input: UpdateMonitoringAlertCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateMonitoringAlertCommandInput,
    UpdateMonitoringAlertCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateMonitoringAlertCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateMonitoringAlertCommandInput,
    UpdateMonitoringAlertCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateMonitoringAlertCommand extends UpdateMonitoringAlertCommand_base {
  protected static __types: {
    api: {
      input: UpdateMonitoringAlertRequest;
      output: UpdateMonitoringAlertResponse;
    };
    sdk: {
      input: UpdateMonitoringAlertCommandInput;
      output: UpdateMonitoringAlertCommandOutput;
    };
  };
}
