import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateArtifactRequest,
  CreateArtifactResponse,
} from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateArtifactCommandInput extends CreateArtifactRequest {}
export interface CreateArtifactCommandOutput
  extends CreateArtifactResponse,
    __MetadataBearer {}
declare const CreateArtifactCommand_base: {
  new (
    input: CreateArtifactCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateArtifactCommandInput,
    CreateArtifactCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateArtifactCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateArtifactCommandInput,
    CreateArtifactCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateArtifactCommand extends CreateArtifactCommand_base {
  protected static __types: {
    api: {
      input: CreateArtifactRequest;
      output: CreateArtifactResponse;
    };
    sdk: {
      input: CreateArtifactCommandInput;
      output: CreateArtifactCommandOutput;
    };
  };
}
