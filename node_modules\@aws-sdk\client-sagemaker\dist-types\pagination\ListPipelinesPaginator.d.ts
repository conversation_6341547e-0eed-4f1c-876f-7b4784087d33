import { Paginator } from "@smithy/types";
import { ListPipelinesCommandInput, ListPipelinesCommandOutput } from "../commands/ListPipelinesCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListPipelines: (config: SageMakerPaginationConfiguration, input: ListPipelinesCommandInput, ...rest: any[]) => Paginator<ListPipelinesCommandOutput>;
