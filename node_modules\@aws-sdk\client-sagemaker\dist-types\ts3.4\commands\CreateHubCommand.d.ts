import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { CreateHubRequest, CreateHubResponse } from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateHubCommandInput extends CreateHubRequest {}
export interface CreateHubCommandOutput
  extends CreateHubResponse,
    __MetadataBearer {}
declare const CreateHubCommand_base: {
  new (
    input: CreateHubCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateHubCommandInput,
    CreateHubCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateHubCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateHubCommandInput,
    CreateHubCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateHubCommand extends CreateHubCommand_base {
  protected static __types: {
    api: {
      input: CreateHubRequest;
      output: CreateHubResponse;
    };
    sdk: {
      input: CreateHubCommandInput;
      output: CreateHubCommandOutput;
    };
  };
}
