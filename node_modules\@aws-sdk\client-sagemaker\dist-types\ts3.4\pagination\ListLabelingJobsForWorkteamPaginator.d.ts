import { Paginator } from "@smithy/types";
import {
  ListLabelingJobsForWorkteamCommandInput,
  ListLabelingJobsForWorkteamCommandOutput,
} from "../commands/ListLabelingJobsForWorkteamCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
export declare const paginateListLabelingJobsForWorkteam: (
  config: SageMakerPaginationConfiguration,
  input: ListLabelingJobsForWorkteamCommandInput,
  ...rest: any[]
) => Paginator<ListLabelingJobsForWorkteamCommandOutput>;
