import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateProcessingJobRequest,
  CreateProcessingJobResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateProcessingJobCommandInput
  extends CreateProcessingJobRequest {}
export interface CreateProcessingJobCommandOutput
  extends CreateProcessingJobResponse,
    __MetadataBearer {}
declare const CreateProcessingJobCommand_base: {
  new (
    input: CreateProcessingJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateProcessingJobCommandInput,
    CreateProcessingJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateProcessingJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateProcessingJobCommandInput,
    CreateProcessingJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateProcessingJobCommand extends CreateProcessingJobCommand_base {
  protected static __types: {
    api: {
      input: CreateProcessingJobRequest;
      output: CreateProcessingJobResponse;
    };
    sdk: {
      input: CreateProcessingJobCommandInput;
      output: CreateProcessingJobCommandOutput;
    };
  };
}
