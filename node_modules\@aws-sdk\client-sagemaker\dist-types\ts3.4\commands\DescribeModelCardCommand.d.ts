import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeModelCardRequest,
  DescribeModelCardResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeModelCardCommandInput
  extends DescribeModelCardRequest {}
export interface DescribeModelCardCommandOutput
  extends DescribeModelCardResponse,
    __MetadataBearer {}
declare const DescribeModelCardCommand_base: {
  new (
    input: DescribeModelCardCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeModelCardCommandInput,
    DescribeModelCardCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeModelCardCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeModelCardCommandInput,
    DescribeModelCardCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeModelCardCommand extends DescribeModelCardCommand_base {
  protected static __types: {
    api: {
      input: DescribeModelCardRequest;
      output: DescribeModelCardResponse;
    };
    sdk: {
      input: DescribeModelCardCommandInput;
      output: DescribeModelCardCommandOutput;
    };
  };
}
