import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateInferenceComponentInput,
  CreateInferenceComponentOutput,
} from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateInferenceComponentCommandInput
  extends CreateInferenceComponentInput {}
export interface CreateInferenceComponentCommandOutput
  extends CreateInferenceComponentOutput,
    __MetadataBearer {}
declare const CreateInferenceComponentCommand_base: {
  new (
    input: CreateInferenceComponentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateInferenceComponentCommandInput,
    CreateInferenceComponentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateInferenceComponentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateInferenceComponentCommandInput,
    CreateInferenceComponentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateInferenceComponentCommand extends CreateInferenceComponentCommand_base {
  protected static __types: {
    api: {
      input: CreateInferenceComponentInput;
      output: CreateInferenceComponentOutput;
    };
    sdk: {
      input: CreateInferenceComponentCommandInput;
      output: CreateInferenceComponentCommandOutput;
    };
  };
}
