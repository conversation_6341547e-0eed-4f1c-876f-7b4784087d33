import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeArtifactRequest,
  DescribeArtifactResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeArtifactCommandInput extends DescribeArtifactRequest {}
export interface DescribeArtifactCommandOutput
  extends DescribeArtifactResponse,
    __MetadataBearer {}
declare const DescribeArtifactCommand_base: {
  new (
    input: DescribeArtifactCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeArtifactCommandInput,
    DescribeArtifactCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeArtifactCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeArtifactCommandInput,
    DescribeArtifactCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeArtifactCommand extends DescribeArtifactCommand_base {
  protected static __types: {
    api: {
      input: DescribeArtifactRequest;
      output: DescribeArtifactResponse;
    };
    sdk: {
      input: DescribeArtifactCommandInput;
      output: DescribeArtifactCommandOutput;
    };
  };
}
