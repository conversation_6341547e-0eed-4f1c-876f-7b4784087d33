import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  UpdateWorkteamRequest,
  UpdateWorkteamResponse,
} from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateWorkteamCommandInput extends UpdateWorkteamRequest {}
export interface UpdateWorkteamCommandOutput
  extends UpdateWorkteamResponse,
    __MetadataBearer {}
declare const UpdateWorkteamCommand_base: {
  new (
    input: UpdateWorkteamCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateWorkteamCommandInput,
    UpdateWorkteamCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateWorkteamCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateWorkteamCommandInput,
    UpdateWorkteamCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateWorkteamCommand extends UpdateWorkteamCommand_base {
  protected static __types: {
    api: {
      input: UpdateWorkteamRequest;
      output: UpdateWorkteamResponse;
    };
    sdk: {
      input: UpdateWorkteamCommandInput;
      output: UpdateWorkteamCommandOutput;
    };
  };
}
