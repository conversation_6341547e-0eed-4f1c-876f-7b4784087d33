import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DeleteOptimizationJobRequest } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteOptimizationJobCommandInput
  extends DeleteOptimizationJobRequest {}
export interface DeleteOptimizationJobCommandOutput extends __MetadataBearer {}
declare const DeleteOptimizationJobCommand_base: {
  new (
    input: DeleteOptimizationJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteOptimizationJobCommandInput,
    DeleteOptimizationJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteOptimizationJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteOptimizationJobCommandInput,
    DeleteOptimizationJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteOptimizationJobCommand extends DeleteOptimizationJobCommand_base {
  protected static __types: {
    api: {
      input: DeleteOptimizationJobRequest;
      output: {};
    };
    sdk: {
      input: DeleteOptimizationJobCommandInput;
      output: DeleteOptimizationJobCommandOutput;
    };
  };
}
