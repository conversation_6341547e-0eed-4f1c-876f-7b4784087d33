import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ListDeviceFleetsRequest,
  ListDeviceFleetsResponse,
} from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface ListDeviceFleetsCommandInput extends ListDeviceFleetsRequest {}
export interface ListDeviceFleetsCommandOutput
  extends ListDeviceFleetsResponse,
    __MetadataBearer {}
declare const ListDeviceFleetsCommand_base: {
  new (
    input: ListDeviceFleetsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListDeviceFleetsCommandInput,
    ListDeviceFleetsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    ...[input]: [] | [ListDeviceFleetsCommandInput]
  ): import("@smithy/smithy-client").CommandImpl<
    ListDeviceFleetsCommandInput,
    ListDeviceFleetsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListDeviceFleetsCommand extends ListDeviceFleetsCommand_base {
  protected static __types: {
    api: {
      input: ListDeviceFleetsRequest;
      output: ListDeviceFleetsResponse;
    };
    sdk: {
      input: ListDeviceFleetsCommandInput;
      output: ListDeviceFleetsCommandOutput;
    };
  };
}
