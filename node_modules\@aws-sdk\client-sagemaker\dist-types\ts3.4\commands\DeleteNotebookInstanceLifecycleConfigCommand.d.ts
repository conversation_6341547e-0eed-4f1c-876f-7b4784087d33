import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { DeleteNotebookInstanceLifecycleConfigInput } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteNotebookInstanceLifecycleConfigCommandInput
  extends DeleteNotebookInstanceLifecycleConfigInput {}
export interface DeleteNotebookInstanceLifecycleConfigCommandOutput
  extends __MetadataBearer {}
declare const DeleteNotebookInstanceLifecycleConfigCommand_base: {
  new (
    input: DeleteNotebookInstanceLifecycleConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteNotebookInstanceLifecycleConfigCommandInput,
    DeleteNotebookInstanceLifecycleConfigCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteNotebookInstanceLifecycleConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteNotebookInstanceLifecycleConfigCommandInput,
    DeleteNotebookInstanceLifecycleConfigCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteNotebookInstanceLifecycleConfigCommand extends DeleteNotebookInstanceLifecycleConfigCommand_base {
  protected static __types: {
    api: {
      input: DeleteNotebookInstanceLifecycleConfigInput;
      output: {};
    };
    sdk: {
      input: DeleteNotebookInstanceLifecycleConfigCommandInput;
      output: DeleteNotebookInstanceLifecycleConfigCommandOutput;
    };
  };
}
