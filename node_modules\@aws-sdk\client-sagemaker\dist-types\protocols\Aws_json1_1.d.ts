import { HttpRequest as __HttpRequest, HttpResponse as __HttpResponse } from "@smithy/protocol-http";
import { SerdeContext as __SerdeContext } from "@smithy/types";
import { AddAssociationCommandInput, AddAssociationCommandOutput } from "../commands/AddAssociationCommand";
import { AddTagsCommandInput, AddTagsCommandOutput } from "../commands/AddTagsCommand";
import { AssociateTrialComponentCommandInput, AssociateTrialComponentCommandOutput } from "../commands/AssociateTrialComponentCommand";
import { BatchDeleteClusterNodesCommandInput, BatchDeleteClusterNodesCommandOutput } from "../commands/BatchDeleteClusterNodesCommand";
import { BatchDescribeModelPackageCommandInput, BatchDescribeModelPackageCommandOutput } from "../commands/BatchDescribeModelPackageCommand";
import { CreateActionCommandInput, CreateActionCommandOutput } from "../commands/CreateActionCommand";
import { CreateAlgorithmCommandInput, CreateAlgorithmCommandOutput } from "../commands/CreateAlgorithmCommand";
import { CreateAppCommandInput, CreateAppCommandOutput } from "../commands/CreateAppCommand";
import { CreateAppImageConfigCommandInput, CreateAppImageConfigCommandOutput } from "../commands/CreateAppImageConfigCommand";
import { CreateArtifactCommandInput, CreateArtifactCommandOutput } from "../commands/CreateArtifactCommand";
import { CreateAutoMLJobCommandInput, CreateAutoMLJobCommandOutput } from "../commands/CreateAutoMLJobCommand";
import { CreateAutoMLJobV2CommandInput, CreateAutoMLJobV2CommandOutput } from "../commands/CreateAutoMLJobV2Command";
import { CreateClusterCommandInput, CreateClusterCommandOutput } from "../commands/CreateClusterCommand";
import { CreateClusterSchedulerConfigCommandInput, CreateClusterSchedulerConfigCommandOutput } from "../commands/CreateClusterSchedulerConfigCommand";
import { CreateCodeRepositoryCommandInput, CreateCodeRepositoryCommandOutput } from "../commands/CreateCodeRepositoryCommand";
import { CreateCompilationJobCommandInput, CreateCompilationJobCommandOutput } from "../commands/CreateCompilationJobCommand";
import { CreateComputeQuotaCommandInput, CreateComputeQuotaCommandOutput } from "../commands/CreateComputeQuotaCommand";
import { CreateContextCommandInput, CreateContextCommandOutput } from "../commands/CreateContextCommand";
import { CreateDataQualityJobDefinitionCommandInput, CreateDataQualityJobDefinitionCommandOutput } from "../commands/CreateDataQualityJobDefinitionCommand";
import { CreateDeviceFleetCommandInput, CreateDeviceFleetCommandOutput } from "../commands/CreateDeviceFleetCommand";
import { CreateDomainCommandInput, CreateDomainCommandOutput } from "../commands/CreateDomainCommand";
import { CreateEdgeDeploymentPlanCommandInput, CreateEdgeDeploymentPlanCommandOutput } from "../commands/CreateEdgeDeploymentPlanCommand";
import { CreateEdgeDeploymentStageCommandInput, CreateEdgeDeploymentStageCommandOutput } from "../commands/CreateEdgeDeploymentStageCommand";
import { CreateEdgePackagingJobCommandInput, CreateEdgePackagingJobCommandOutput } from "../commands/CreateEdgePackagingJobCommand";
import { CreateEndpointCommandInput, CreateEndpointCommandOutput } from "../commands/CreateEndpointCommand";
import { CreateEndpointConfigCommandInput, CreateEndpointConfigCommandOutput } from "../commands/CreateEndpointConfigCommand";
import { CreateExperimentCommandInput, CreateExperimentCommandOutput } from "../commands/CreateExperimentCommand";
import { CreateFeatureGroupCommandInput, CreateFeatureGroupCommandOutput } from "../commands/CreateFeatureGroupCommand";
import { CreateFlowDefinitionCommandInput, CreateFlowDefinitionCommandOutput } from "../commands/CreateFlowDefinitionCommand";
import { CreateHubCommandInput, CreateHubCommandOutput } from "../commands/CreateHubCommand";
import { CreateHubContentReferenceCommandInput, CreateHubContentReferenceCommandOutput } from "../commands/CreateHubContentReferenceCommand";
import { CreateHumanTaskUiCommandInput, CreateHumanTaskUiCommandOutput } from "../commands/CreateHumanTaskUiCommand";
import { CreateHyperParameterTuningJobCommandInput, CreateHyperParameterTuningJobCommandOutput } from "../commands/CreateHyperParameterTuningJobCommand";
import { CreateImageCommandInput, CreateImageCommandOutput } from "../commands/CreateImageCommand";
import { CreateImageVersionCommandInput, CreateImageVersionCommandOutput } from "../commands/CreateImageVersionCommand";
import { CreateInferenceComponentCommandInput, CreateInferenceComponentCommandOutput } from "../commands/CreateInferenceComponentCommand";
import { CreateInferenceExperimentCommandInput, CreateInferenceExperimentCommandOutput } from "../commands/CreateInferenceExperimentCommand";
import { CreateInferenceRecommendationsJobCommandInput, CreateInferenceRecommendationsJobCommandOutput } from "../commands/CreateInferenceRecommendationsJobCommand";
import { CreateLabelingJobCommandInput, CreateLabelingJobCommandOutput } from "../commands/CreateLabelingJobCommand";
import { CreateMlflowTrackingServerCommandInput, CreateMlflowTrackingServerCommandOutput } from "../commands/CreateMlflowTrackingServerCommand";
import { CreateModelBiasJobDefinitionCommandInput, CreateModelBiasJobDefinitionCommandOutput } from "../commands/CreateModelBiasJobDefinitionCommand";
import { CreateModelCardCommandInput, CreateModelCardCommandOutput } from "../commands/CreateModelCardCommand";
import { CreateModelCardExportJobCommandInput, CreateModelCardExportJobCommandOutput } from "../commands/CreateModelCardExportJobCommand";
import { CreateModelCommandInput, CreateModelCommandOutput } from "../commands/CreateModelCommand";
import { CreateModelExplainabilityJobDefinitionCommandInput, CreateModelExplainabilityJobDefinitionCommandOutput } from "../commands/CreateModelExplainabilityJobDefinitionCommand";
import { CreateModelPackageCommandInput, CreateModelPackageCommandOutput } from "../commands/CreateModelPackageCommand";
import { CreateModelPackageGroupCommandInput, CreateModelPackageGroupCommandOutput } from "../commands/CreateModelPackageGroupCommand";
import { CreateModelQualityJobDefinitionCommandInput, CreateModelQualityJobDefinitionCommandOutput } from "../commands/CreateModelQualityJobDefinitionCommand";
import { CreateMonitoringScheduleCommandInput, CreateMonitoringScheduleCommandOutput } from "../commands/CreateMonitoringScheduleCommand";
import { CreateNotebookInstanceCommandInput, CreateNotebookInstanceCommandOutput } from "../commands/CreateNotebookInstanceCommand";
import { CreateNotebookInstanceLifecycleConfigCommandInput, CreateNotebookInstanceLifecycleConfigCommandOutput } from "../commands/CreateNotebookInstanceLifecycleConfigCommand";
import { CreateOptimizationJobCommandInput, CreateOptimizationJobCommandOutput } from "../commands/CreateOptimizationJobCommand";
import { CreatePartnerAppCommandInput, CreatePartnerAppCommandOutput } from "../commands/CreatePartnerAppCommand";
import { CreatePartnerAppPresignedUrlCommandInput, CreatePartnerAppPresignedUrlCommandOutput } from "../commands/CreatePartnerAppPresignedUrlCommand";
import { CreatePipelineCommandInput, CreatePipelineCommandOutput } from "../commands/CreatePipelineCommand";
import { CreatePresignedDomainUrlCommandInput, CreatePresignedDomainUrlCommandOutput } from "../commands/CreatePresignedDomainUrlCommand";
import { CreatePresignedMlflowTrackingServerUrlCommandInput, CreatePresignedMlflowTrackingServerUrlCommandOutput } from "../commands/CreatePresignedMlflowTrackingServerUrlCommand";
import { CreatePresignedNotebookInstanceUrlCommandInput, CreatePresignedNotebookInstanceUrlCommandOutput } from "../commands/CreatePresignedNotebookInstanceUrlCommand";
import { CreateProcessingJobCommandInput, CreateProcessingJobCommandOutput } from "../commands/CreateProcessingJobCommand";
import { CreateProjectCommandInput, CreateProjectCommandOutput } from "../commands/CreateProjectCommand";
import { CreateSpaceCommandInput, CreateSpaceCommandOutput } from "../commands/CreateSpaceCommand";
import { CreateStudioLifecycleConfigCommandInput, CreateStudioLifecycleConfigCommandOutput } from "../commands/CreateStudioLifecycleConfigCommand";
import { CreateTrainingJobCommandInput, CreateTrainingJobCommandOutput } from "../commands/CreateTrainingJobCommand";
import { CreateTrainingPlanCommandInput, CreateTrainingPlanCommandOutput } from "../commands/CreateTrainingPlanCommand";
import { CreateTransformJobCommandInput, CreateTransformJobCommandOutput } from "../commands/CreateTransformJobCommand";
import { CreateTrialCommandInput, CreateTrialCommandOutput } from "../commands/CreateTrialCommand";
import { CreateTrialComponentCommandInput, CreateTrialComponentCommandOutput } from "../commands/CreateTrialComponentCommand";
import { CreateUserProfileCommandInput, CreateUserProfileCommandOutput } from "../commands/CreateUserProfileCommand";
import { CreateWorkforceCommandInput, CreateWorkforceCommandOutput } from "../commands/CreateWorkforceCommand";
import { CreateWorkteamCommandInput, CreateWorkteamCommandOutput } from "../commands/CreateWorkteamCommand";
import { DeleteActionCommandInput, DeleteActionCommandOutput } from "../commands/DeleteActionCommand";
import { DeleteAlgorithmCommandInput, DeleteAlgorithmCommandOutput } from "../commands/DeleteAlgorithmCommand";
import { DeleteAppCommandInput, DeleteAppCommandOutput } from "../commands/DeleteAppCommand";
import { DeleteAppImageConfigCommandInput, DeleteAppImageConfigCommandOutput } from "../commands/DeleteAppImageConfigCommand";
import { DeleteArtifactCommandInput, DeleteArtifactCommandOutput } from "../commands/DeleteArtifactCommand";
import { DeleteAssociationCommandInput, DeleteAssociationCommandOutput } from "../commands/DeleteAssociationCommand";
import { DeleteClusterCommandInput, DeleteClusterCommandOutput } from "../commands/DeleteClusterCommand";
import { DeleteClusterSchedulerConfigCommandInput, DeleteClusterSchedulerConfigCommandOutput } from "../commands/DeleteClusterSchedulerConfigCommand";
import { DeleteCodeRepositoryCommandInput, DeleteCodeRepositoryCommandOutput } from "../commands/DeleteCodeRepositoryCommand";
import { DeleteCompilationJobCommandInput, DeleteCompilationJobCommandOutput } from "../commands/DeleteCompilationJobCommand";
import { DeleteComputeQuotaCommandInput, DeleteComputeQuotaCommandOutput } from "../commands/DeleteComputeQuotaCommand";
import { DeleteContextCommandInput, DeleteContextCommandOutput } from "../commands/DeleteContextCommand";
import { DeleteDataQualityJobDefinitionCommandInput, DeleteDataQualityJobDefinitionCommandOutput } from "../commands/DeleteDataQualityJobDefinitionCommand";
import { DeleteDeviceFleetCommandInput, DeleteDeviceFleetCommandOutput } from "../commands/DeleteDeviceFleetCommand";
import { DeleteDomainCommandInput, DeleteDomainCommandOutput } from "../commands/DeleteDomainCommand";
import { DeleteEdgeDeploymentPlanCommandInput, DeleteEdgeDeploymentPlanCommandOutput } from "../commands/DeleteEdgeDeploymentPlanCommand";
import { DeleteEdgeDeploymentStageCommandInput, DeleteEdgeDeploymentStageCommandOutput } from "../commands/DeleteEdgeDeploymentStageCommand";
import { DeleteEndpointCommandInput, DeleteEndpointCommandOutput } from "../commands/DeleteEndpointCommand";
import { DeleteEndpointConfigCommandInput, DeleteEndpointConfigCommandOutput } from "../commands/DeleteEndpointConfigCommand";
import { DeleteExperimentCommandInput, DeleteExperimentCommandOutput } from "../commands/DeleteExperimentCommand";
import { DeleteFeatureGroupCommandInput, DeleteFeatureGroupCommandOutput } from "../commands/DeleteFeatureGroupCommand";
import { DeleteFlowDefinitionCommandInput, DeleteFlowDefinitionCommandOutput } from "../commands/DeleteFlowDefinitionCommand";
import { DeleteHubCommandInput, DeleteHubCommandOutput } from "../commands/DeleteHubCommand";
import { DeleteHubContentCommandInput, DeleteHubContentCommandOutput } from "../commands/DeleteHubContentCommand";
import { DeleteHubContentReferenceCommandInput, DeleteHubContentReferenceCommandOutput } from "../commands/DeleteHubContentReferenceCommand";
import { DeleteHumanTaskUiCommandInput, DeleteHumanTaskUiCommandOutput } from "../commands/DeleteHumanTaskUiCommand";
import { DeleteHyperParameterTuningJobCommandInput, DeleteHyperParameterTuningJobCommandOutput } from "../commands/DeleteHyperParameterTuningJobCommand";
import { DeleteImageCommandInput, DeleteImageCommandOutput } from "../commands/DeleteImageCommand";
import { DeleteImageVersionCommandInput, DeleteImageVersionCommandOutput } from "../commands/DeleteImageVersionCommand";
import { DeleteInferenceComponentCommandInput, DeleteInferenceComponentCommandOutput } from "../commands/DeleteInferenceComponentCommand";
import { DeleteInferenceExperimentCommandInput, DeleteInferenceExperimentCommandOutput } from "../commands/DeleteInferenceExperimentCommand";
import { DeleteMlflowTrackingServerCommandInput, DeleteMlflowTrackingServerCommandOutput } from "../commands/DeleteMlflowTrackingServerCommand";
import { DeleteModelBiasJobDefinitionCommandInput, DeleteModelBiasJobDefinitionCommandOutput } from "../commands/DeleteModelBiasJobDefinitionCommand";
import { DeleteModelCardCommandInput, DeleteModelCardCommandOutput } from "../commands/DeleteModelCardCommand";
import { DeleteModelCommandInput, DeleteModelCommandOutput } from "../commands/DeleteModelCommand";
import { DeleteModelExplainabilityJobDefinitionCommandInput, DeleteModelExplainabilityJobDefinitionCommandOutput } from "../commands/DeleteModelExplainabilityJobDefinitionCommand";
import { DeleteModelPackageCommandInput, DeleteModelPackageCommandOutput } from "../commands/DeleteModelPackageCommand";
import { DeleteModelPackageGroupCommandInput, DeleteModelPackageGroupCommandOutput } from "../commands/DeleteModelPackageGroupCommand";
import { DeleteModelPackageGroupPolicyCommandInput, DeleteModelPackageGroupPolicyCommandOutput } from "../commands/DeleteModelPackageGroupPolicyCommand";
import { DeleteModelQualityJobDefinitionCommandInput, DeleteModelQualityJobDefinitionCommandOutput } from "../commands/DeleteModelQualityJobDefinitionCommand";
import { DeleteMonitoringScheduleCommandInput, DeleteMonitoringScheduleCommandOutput } from "../commands/DeleteMonitoringScheduleCommand";
import { DeleteNotebookInstanceCommandInput, DeleteNotebookInstanceCommandOutput } from "../commands/DeleteNotebookInstanceCommand";
import { DeleteNotebookInstanceLifecycleConfigCommandInput, DeleteNotebookInstanceLifecycleConfigCommandOutput } from "../commands/DeleteNotebookInstanceLifecycleConfigCommand";
import { DeleteOptimizationJobCommandInput, DeleteOptimizationJobCommandOutput } from "../commands/DeleteOptimizationJobCommand";
import { DeletePartnerAppCommandInput, DeletePartnerAppCommandOutput } from "../commands/DeletePartnerAppCommand";
import { DeletePipelineCommandInput, DeletePipelineCommandOutput } from "../commands/DeletePipelineCommand";
import { DeleteProjectCommandInput, DeleteProjectCommandOutput } from "../commands/DeleteProjectCommand";
import { DeleteSpaceCommandInput, DeleteSpaceCommandOutput } from "../commands/DeleteSpaceCommand";
import { DeleteStudioLifecycleConfigCommandInput, DeleteStudioLifecycleConfigCommandOutput } from "../commands/DeleteStudioLifecycleConfigCommand";
import { DeleteTagsCommandInput, DeleteTagsCommandOutput } from "../commands/DeleteTagsCommand";
import { DeleteTrialCommandInput, DeleteTrialCommandOutput } from "../commands/DeleteTrialCommand";
import { DeleteTrialComponentCommandInput, DeleteTrialComponentCommandOutput } from "../commands/DeleteTrialComponentCommand";
import { DeleteUserProfileCommandInput, DeleteUserProfileCommandOutput } from "../commands/DeleteUserProfileCommand";
import { DeleteWorkforceCommandInput, DeleteWorkforceCommandOutput } from "../commands/DeleteWorkforceCommand";
import { DeleteWorkteamCommandInput, DeleteWorkteamCommandOutput } from "../commands/DeleteWorkteamCommand";
import { DeregisterDevicesCommandInput, DeregisterDevicesCommandOutput } from "../commands/DeregisterDevicesCommand";
import { DescribeActionCommandInput, DescribeActionCommandOutput } from "../commands/DescribeActionCommand";
import { DescribeAlgorithmCommandInput, DescribeAlgorithmCommandOutput } from "../commands/DescribeAlgorithmCommand";
import { DescribeAppCommandInput, DescribeAppCommandOutput } from "../commands/DescribeAppCommand";
import { DescribeAppImageConfigCommandInput, DescribeAppImageConfigCommandOutput } from "../commands/DescribeAppImageConfigCommand";
import { DescribeArtifactCommandInput, DescribeArtifactCommandOutput } from "../commands/DescribeArtifactCommand";
import { DescribeAutoMLJobCommandInput, DescribeAutoMLJobCommandOutput } from "../commands/DescribeAutoMLJobCommand";
import { DescribeAutoMLJobV2CommandInput, DescribeAutoMLJobV2CommandOutput } from "../commands/DescribeAutoMLJobV2Command";
import { DescribeClusterCommandInput, DescribeClusterCommandOutput } from "../commands/DescribeClusterCommand";
import { DescribeClusterNodeCommandInput, DescribeClusterNodeCommandOutput } from "../commands/DescribeClusterNodeCommand";
import { DescribeClusterSchedulerConfigCommandInput, DescribeClusterSchedulerConfigCommandOutput } from "../commands/DescribeClusterSchedulerConfigCommand";
import { DescribeCodeRepositoryCommandInput, DescribeCodeRepositoryCommandOutput } from "../commands/DescribeCodeRepositoryCommand";
import { DescribeCompilationJobCommandInput, DescribeCompilationJobCommandOutput } from "../commands/DescribeCompilationJobCommand";
import { DescribeComputeQuotaCommandInput, DescribeComputeQuotaCommandOutput } from "../commands/DescribeComputeQuotaCommand";
import { DescribeContextCommandInput, DescribeContextCommandOutput } from "../commands/DescribeContextCommand";
import { DescribeDataQualityJobDefinitionCommandInput, DescribeDataQualityJobDefinitionCommandOutput } from "../commands/DescribeDataQualityJobDefinitionCommand";
import { DescribeDeviceCommandInput, DescribeDeviceCommandOutput } from "../commands/DescribeDeviceCommand";
import { DescribeDeviceFleetCommandInput, DescribeDeviceFleetCommandOutput } from "../commands/DescribeDeviceFleetCommand";
import { DescribeDomainCommandInput, DescribeDomainCommandOutput } from "../commands/DescribeDomainCommand";
import { DescribeEdgeDeploymentPlanCommandInput, DescribeEdgeDeploymentPlanCommandOutput } from "../commands/DescribeEdgeDeploymentPlanCommand";
import { DescribeEdgePackagingJobCommandInput, DescribeEdgePackagingJobCommandOutput } from "../commands/DescribeEdgePackagingJobCommand";
import { DescribeEndpointCommandInput, DescribeEndpointCommandOutput } from "../commands/DescribeEndpointCommand";
import { DescribeEndpointConfigCommandInput, DescribeEndpointConfigCommandOutput } from "../commands/DescribeEndpointConfigCommand";
import { DescribeExperimentCommandInput, DescribeExperimentCommandOutput } from "../commands/DescribeExperimentCommand";
import { DescribeFeatureGroupCommandInput, DescribeFeatureGroupCommandOutput } from "../commands/DescribeFeatureGroupCommand";
import { DescribeFeatureMetadataCommandInput, DescribeFeatureMetadataCommandOutput } from "../commands/DescribeFeatureMetadataCommand";
import { DescribeFlowDefinitionCommandInput, DescribeFlowDefinitionCommandOutput } from "../commands/DescribeFlowDefinitionCommand";
import { DescribeHubCommandInput, DescribeHubCommandOutput } from "../commands/DescribeHubCommand";
import { DescribeHubContentCommandInput, DescribeHubContentCommandOutput } from "../commands/DescribeHubContentCommand";
import { DescribeHumanTaskUiCommandInput, DescribeHumanTaskUiCommandOutput } from "../commands/DescribeHumanTaskUiCommand";
import { DescribeHyperParameterTuningJobCommandInput, DescribeHyperParameterTuningJobCommandOutput } from "../commands/DescribeHyperParameterTuningJobCommand";
import { DescribeImageCommandInput, DescribeImageCommandOutput } from "../commands/DescribeImageCommand";
import { DescribeImageVersionCommandInput, DescribeImageVersionCommandOutput } from "../commands/DescribeImageVersionCommand";
import { DescribeInferenceComponentCommandInput, DescribeInferenceComponentCommandOutput } from "../commands/DescribeInferenceComponentCommand";
import { DescribeInferenceExperimentCommandInput, DescribeInferenceExperimentCommandOutput } from "../commands/DescribeInferenceExperimentCommand";
import { DescribeInferenceRecommendationsJobCommandInput, DescribeInferenceRecommendationsJobCommandOutput } from "../commands/DescribeInferenceRecommendationsJobCommand";
import { DescribeLabelingJobCommandInput, DescribeLabelingJobCommandOutput } from "../commands/DescribeLabelingJobCommand";
import { DescribeLineageGroupCommandInput, DescribeLineageGroupCommandOutput } from "../commands/DescribeLineageGroupCommand";
import { DescribeMlflowTrackingServerCommandInput, DescribeMlflowTrackingServerCommandOutput } from "../commands/DescribeMlflowTrackingServerCommand";
import { DescribeModelBiasJobDefinitionCommandInput, DescribeModelBiasJobDefinitionCommandOutput } from "../commands/DescribeModelBiasJobDefinitionCommand";
import { DescribeModelCardCommandInput, DescribeModelCardCommandOutput } from "../commands/DescribeModelCardCommand";
import { DescribeModelCardExportJobCommandInput, DescribeModelCardExportJobCommandOutput } from "../commands/DescribeModelCardExportJobCommand";
import { DescribeModelCommandInput, DescribeModelCommandOutput } from "../commands/DescribeModelCommand";
import { DescribeModelExplainabilityJobDefinitionCommandInput, DescribeModelExplainabilityJobDefinitionCommandOutput } from "../commands/DescribeModelExplainabilityJobDefinitionCommand";
import { DescribeModelPackageCommandInput, DescribeModelPackageCommandOutput } from "../commands/DescribeModelPackageCommand";
import { DescribeModelPackageGroupCommandInput, DescribeModelPackageGroupCommandOutput } from "../commands/DescribeModelPackageGroupCommand";
import { DescribeModelQualityJobDefinitionCommandInput, DescribeModelQualityJobDefinitionCommandOutput } from "../commands/DescribeModelQualityJobDefinitionCommand";
import { DescribeMonitoringScheduleCommandInput, DescribeMonitoringScheduleCommandOutput } from "../commands/DescribeMonitoringScheduleCommand";
import { DescribeNotebookInstanceCommandInput, DescribeNotebookInstanceCommandOutput } from "../commands/DescribeNotebookInstanceCommand";
import { DescribeNotebookInstanceLifecycleConfigCommandInput, DescribeNotebookInstanceLifecycleConfigCommandOutput } from "../commands/DescribeNotebookInstanceLifecycleConfigCommand";
import { DescribeOptimizationJobCommandInput, DescribeOptimizationJobCommandOutput } from "../commands/DescribeOptimizationJobCommand";
import { DescribePartnerAppCommandInput, DescribePartnerAppCommandOutput } from "../commands/DescribePartnerAppCommand";
import { DescribePipelineCommandInput, DescribePipelineCommandOutput } from "../commands/DescribePipelineCommand";
import { DescribePipelineDefinitionForExecutionCommandInput, DescribePipelineDefinitionForExecutionCommandOutput } from "../commands/DescribePipelineDefinitionForExecutionCommand";
import { DescribePipelineExecutionCommandInput, DescribePipelineExecutionCommandOutput } from "../commands/DescribePipelineExecutionCommand";
import { DescribeProcessingJobCommandInput, DescribeProcessingJobCommandOutput } from "../commands/DescribeProcessingJobCommand";
import { DescribeProjectCommandInput, DescribeProjectCommandOutput } from "../commands/DescribeProjectCommand";
import { DescribeSpaceCommandInput, DescribeSpaceCommandOutput } from "../commands/DescribeSpaceCommand";
import { DescribeStudioLifecycleConfigCommandInput, DescribeStudioLifecycleConfigCommandOutput } from "../commands/DescribeStudioLifecycleConfigCommand";
import { DescribeSubscribedWorkteamCommandInput, DescribeSubscribedWorkteamCommandOutput } from "../commands/DescribeSubscribedWorkteamCommand";
import { DescribeTrainingJobCommandInput, DescribeTrainingJobCommandOutput } from "../commands/DescribeTrainingJobCommand";
import { DescribeTrainingPlanCommandInput, DescribeTrainingPlanCommandOutput } from "../commands/DescribeTrainingPlanCommand";
import { DescribeTransformJobCommandInput, DescribeTransformJobCommandOutput } from "../commands/DescribeTransformJobCommand";
import { DescribeTrialCommandInput, DescribeTrialCommandOutput } from "../commands/DescribeTrialCommand";
import { DescribeTrialComponentCommandInput, DescribeTrialComponentCommandOutput } from "../commands/DescribeTrialComponentCommand";
import { DescribeUserProfileCommandInput, DescribeUserProfileCommandOutput } from "../commands/DescribeUserProfileCommand";
import { DescribeWorkforceCommandInput, DescribeWorkforceCommandOutput } from "../commands/DescribeWorkforceCommand";
import { DescribeWorkteamCommandInput, DescribeWorkteamCommandOutput } from "../commands/DescribeWorkteamCommand";
import { DisableSagemakerServicecatalogPortfolioCommandInput, DisableSagemakerServicecatalogPortfolioCommandOutput } from "../commands/DisableSagemakerServicecatalogPortfolioCommand";
import { DisassociateTrialComponentCommandInput, DisassociateTrialComponentCommandOutput } from "../commands/DisassociateTrialComponentCommand";
import { EnableSagemakerServicecatalogPortfolioCommandInput, EnableSagemakerServicecatalogPortfolioCommandOutput } from "../commands/EnableSagemakerServicecatalogPortfolioCommand";
import { GetDeviceFleetReportCommandInput, GetDeviceFleetReportCommandOutput } from "../commands/GetDeviceFleetReportCommand";
import { GetLineageGroupPolicyCommandInput, GetLineageGroupPolicyCommandOutput } from "../commands/GetLineageGroupPolicyCommand";
import { GetModelPackageGroupPolicyCommandInput, GetModelPackageGroupPolicyCommandOutput } from "../commands/GetModelPackageGroupPolicyCommand";
import { GetSagemakerServicecatalogPortfolioStatusCommandInput, GetSagemakerServicecatalogPortfolioStatusCommandOutput } from "../commands/GetSagemakerServicecatalogPortfolioStatusCommand";
import { GetScalingConfigurationRecommendationCommandInput, GetScalingConfigurationRecommendationCommandOutput } from "../commands/GetScalingConfigurationRecommendationCommand";
import { GetSearchSuggestionsCommandInput, GetSearchSuggestionsCommandOutput } from "../commands/GetSearchSuggestionsCommand";
import { ImportHubContentCommandInput, ImportHubContentCommandOutput } from "../commands/ImportHubContentCommand";
import { ListActionsCommandInput, ListActionsCommandOutput } from "../commands/ListActionsCommand";
import { ListAlgorithmsCommandInput, ListAlgorithmsCommandOutput } from "../commands/ListAlgorithmsCommand";
import { ListAliasesCommandInput, ListAliasesCommandOutput } from "../commands/ListAliasesCommand";
import { ListAppImageConfigsCommandInput, ListAppImageConfigsCommandOutput } from "../commands/ListAppImageConfigsCommand";
import { ListAppsCommandInput, ListAppsCommandOutput } from "../commands/ListAppsCommand";
import { ListArtifactsCommandInput, ListArtifactsCommandOutput } from "../commands/ListArtifactsCommand";
import { ListAssociationsCommandInput, ListAssociationsCommandOutput } from "../commands/ListAssociationsCommand";
import { ListAutoMLJobsCommandInput, ListAutoMLJobsCommandOutput } from "../commands/ListAutoMLJobsCommand";
import { ListCandidatesForAutoMLJobCommandInput, ListCandidatesForAutoMLJobCommandOutput } from "../commands/ListCandidatesForAutoMLJobCommand";
import { ListClusterNodesCommandInput, ListClusterNodesCommandOutput } from "../commands/ListClusterNodesCommand";
import { ListClusterSchedulerConfigsCommandInput, ListClusterSchedulerConfigsCommandOutput } from "../commands/ListClusterSchedulerConfigsCommand";
import { ListClustersCommandInput, ListClustersCommandOutput } from "../commands/ListClustersCommand";
import { ListCodeRepositoriesCommandInput, ListCodeRepositoriesCommandOutput } from "../commands/ListCodeRepositoriesCommand";
import { ListCompilationJobsCommandInput, ListCompilationJobsCommandOutput } from "../commands/ListCompilationJobsCommand";
import { ListComputeQuotasCommandInput, ListComputeQuotasCommandOutput } from "../commands/ListComputeQuotasCommand";
import { ListContextsCommandInput, ListContextsCommandOutput } from "../commands/ListContextsCommand";
import { ListDataQualityJobDefinitionsCommandInput, ListDataQualityJobDefinitionsCommandOutput } from "../commands/ListDataQualityJobDefinitionsCommand";
import { ListDeviceFleetsCommandInput, ListDeviceFleetsCommandOutput } from "../commands/ListDeviceFleetsCommand";
import { ListDevicesCommandInput, ListDevicesCommandOutput } from "../commands/ListDevicesCommand";
import { ListDomainsCommandInput, ListDomainsCommandOutput } from "../commands/ListDomainsCommand";
import { ListEdgeDeploymentPlansCommandInput, ListEdgeDeploymentPlansCommandOutput } from "../commands/ListEdgeDeploymentPlansCommand";
import { ListEdgePackagingJobsCommandInput, ListEdgePackagingJobsCommandOutput } from "../commands/ListEdgePackagingJobsCommand";
import { ListEndpointConfigsCommandInput, ListEndpointConfigsCommandOutput } from "../commands/ListEndpointConfigsCommand";
import { ListEndpointsCommandInput, ListEndpointsCommandOutput } from "../commands/ListEndpointsCommand";
import { ListExperimentsCommandInput, ListExperimentsCommandOutput } from "../commands/ListExperimentsCommand";
import { ListFeatureGroupsCommandInput, ListFeatureGroupsCommandOutput } from "../commands/ListFeatureGroupsCommand";
import { ListFlowDefinitionsCommandInput, ListFlowDefinitionsCommandOutput } from "../commands/ListFlowDefinitionsCommand";
import { ListHubContentsCommandInput, ListHubContentsCommandOutput } from "../commands/ListHubContentsCommand";
import { ListHubContentVersionsCommandInput, ListHubContentVersionsCommandOutput } from "../commands/ListHubContentVersionsCommand";
import { ListHubsCommandInput, ListHubsCommandOutput } from "../commands/ListHubsCommand";
import { ListHumanTaskUisCommandInput, ListHumanTaskUisCommandOutput } from "../commands/ListHumanTaskUisCommand";
import { ListHyperParameterTuningJobsCommandInput, ListHyperParameterTuningJobsCommandOutput } from "../commands/ListHyperParameterTuningJobsCommand";
import { ListImagesCommandInput, ListImagesCommandOutput } from "../commands/ListImagesCommand";
import { ListImageVersionsCommandInput, ListImageVersionsCommandOutput } from "../commands/ListImageVersionsCommand";
import { ListInferenceComponentsCommandInput, ListInferenceComponentsCommandOutput } from "../commands/ListInferenceComponentsCommand";
import { ListInferenceExperimentsCommandInput, ListInferenceExperimentsCommandOutput } from "../commands/ListInferenceExperimentsCommand";
import { ListInferenceRecommendationsJobsCommandInput, ListInferenceRecommendationsJobsCommandOutput } from "../commands/ListInferenceRecommendationsJobsCommand";
import { ListInferenceRecommendationsJobStepsCommandInput, ListInferenceRecommendationsJobStepsCommandOutput } from "../commands/ListInferenceRecommendationsJobStepsCommand";
import { ListLabelingJobsCommandInput, ListLabelingJobsCommandOutput } from "../commands/ListLabelingJobsCommand";
import { ListLabelingJobsForWorkteamCommandInput, ListLabelingJobsForWorkteamCommandOutput } from "../commands/ListLabelingJobsForWorkteamCommand";
import { ListLineageGroupsCommandInput, ListLineageGroupsCommandOutput } from "../commands/ListLineageGroupsCommand";
import { ListMlflowTrackingServersCommandInput, ListMlflowTrackingServersCommandOutput } from "../commands/ListMlflowTrackingServersCommand";
import { ListModelBiasJobDefinitionsCommandInput, ListModelBiasJobDefinitionsCommandOutput } from "../commands/ListModelBiasJobDefinitionsCommand";
import { ListModelCardExportJobsCommandInput, ListModelCardExportJobsCommandOutput } from "../commands/ListModelCardExportJobsCommand";
import { ListModelCardsCommandInput, ListModelCardsCommandOutput } from "../commands/ListModelCardsCommand";
import { ListModelCardVersionsCommandInput, ListModelCardVersionsCommandOutput } from "../commands/ListModelCardVersionsCommand";
import { ListModelExplainabilityJobDefinitionsCommandInput, ListModelExplainabilityJobDefinitionsCommandOutput } from "../commands/ListModelExplainabilityJobDefinitionsCommand";
import { ListModelMetadataCommandInput, ListModelMetadataCommandOutput } from "../commands/ListModelMetadataCommand";
import { ListModelPackageGroupsCommandInput, ListModelPackageGroupsCommandOutput } from "../commands/ListModelPackageGroupsCommand";
import { ListModelPackagesCommandInput, ListModelPackagesCommandOutput } from "../commands/ListModelPackagesCommand";
import { ListModelQualityJobDefinitionsCommandInput, ListModelQualityJobDefinitionsCommandOutput } from "../commands/ListModelQualityJobDefinitionsCommand";
import { ListModelsCommandInput, ListModelsCommandOutput } from "../commands/ListModelsCommand";
import { ListMonitoringAlertHistoryCommandInput, ListMonitoringAlertHistoryCommandOutput } from "../commands/ListMonitoringAlertHistoryCommand";
import { ListMonitoringAlertsCommandInput, ListMonitoringAlertsCommandOutput } from "../commands/ListMonitoringAlertsCommand";
import { ListMonitoringExecutionsCommandInput, ListMonitoringExecutionsCommandOutput } from "../commands/ListMonitoringExecutionsCommand";
import { ListMonitoringSchedulesCommandInput, ListMonitoringSchedulesCommandOutput } from "../commands/ListMonitoringSchedulesCommand";
import { ListNotebookInstanceLifecycleConfigsCommandInput, ListNotebookInstanceLifecycleConfigsCommandOutput } from "../commands/ListNotebookInstanceLifecycleConfigsCommand";
import { ListNotebookInstancesCommandInput, ListNotebookInstancesCommandOutput } from "../commands/ListNotebookInstancesCommand";
import { ListOptimizationJobsCommandInput, ListOptimizationJobsCommandOutput } from "../commands/ListOptimizationJobsCommand";
import { ListPartnerAppsCommandInput, ListPartnerAppsCommandOutput } from "../commands/ListPartnerAppsCommand";
import { ListPipelineExecutionsCommandInput, ListPipelineExecutionsCommandOutput } from "../commands/ListPipelineExecutionsCommand";
import { ListPipelineExecutionStepsCommandInput, ListPipelineExecutionStepsCommandOutput } from "../commands/ListPipelineExecutionStepsCommand";
import { ListPipelineParametersForExecutionCommandInput, ListPipelineParametersForExecutionCommandOutput } from "../commands/ListPipelineParametersForExecutionCommand";
import { ListPipelinesCommandInput, ListPipelinesCommandOutput } from "../commands/ListPipelinesCommand";
import { ListProcessingJobsCommandInput, ListProcessingJobsCommandOutput } from "../commands/ListProcessingJobsCommand";
import { ListProjectsCommandInput, ListProjectsCommandOutput } from "../commands/ListProjectsCommand";
import { ListResourceCatalogsCommandInput, ListResourceCatalogsCommandOutput } from "../commands/ListResourceCatalogsCommand";
import { ListSpacesCommandInput, ListSpacesCommandOutput } from "../commands/ListSpacesCommand";
import { ListStageDevicesCommandInput, ListStageDevicesCommandOutput } from "../commands/ListStageDevicesCommand";
import { ListStudioLifecycleConfigsCommandInput, ListStudioLifecycleConfigsCommandOutput } from "../commands/ListStudioLifecycleConfigsCommand";
import { ListSubscribedWorkteamsCommandInput, ListSubscribedWorkteamsCommandOutput } from "../commands/ListSubscribedWorkteamsCommand";
import { ListTagsCommandInput, ListTagsCommandOutput } from "../commands/ListTagsCommand";
import { ListTrainingJobsCommandInput, ListTrainingJobsCommandOutput } from "../commands/ListTrainingJobsCommand";
import { ListTrainingJobsForHyperParameterTuningJobCommandInput, ListTrainingJobsForHyperParameterTuningJobCommandOutput } from "../commands/ListTrainingJobsForHyperParameterTuningJobCommand";
import { ListTrainingPlansCommandInput, ListTrainingPlansCommandOutput } from "../commands/ListTrainingPlansCommand";
import { ListTransformJobsCommandInput, ListTransformJobsCommandOutput } from "../commands/ListTransformJobsCommand";
import { ListTrialComponentsCommandInput, ListTrialComponentsCommandOutput } from "../commands/ListTrialComponentsCommand";
import { ListTrialsCommandInput, ListTrialsCommandOutput } from "../commands/ListTrialsCommand";
import { ListUserProfilesCommandInput, ListUserProfilesCommandOutput } from "../commands/ListUserProfilesCommand";
import { ListWorkforcesCommandInput, ListWorkforcesCommandOutput } from "../commands/ListWorkforcesCommand";
import { ListWorkteamsCommandInput, ListWorkteamsCommandOutput } from "../commands/ListWorkteamsCommand";
import { PutModelPackageGroupPolicyCommandInput, PutModelPackageGroupPolicyCommandOutput } from "../commands/PutModelPackageGroupPolicyCommand";
import { QueryLineageCommandInput, QueryLineageCommandOutput } from "../commands/QueryLineageCommand";
import { RegisterDevicesCommandInput, RegisterDevicesCommandOutput } from "../commands/RegisterDevicesCommand";
import { RenderUiTemplateCommandInput, RenderUiTemplateCommandOutput } from "../commands/RenderUiTemplateCommand";
import { RetryPipelineExecutionCommandInput, RetryPipelineExecutionCommandOutput } from "../commands/RetryPipelineExecutionCommand";
import { SearchCommandInput, SearchCommandOutput } from "../commands/SearchCommand";
import { SearchTrainingPlanOfferingsCommandInput, SearchTrainingPlanOfferingsCommandOutput } from "../commands/SearchTrainingPlanOfferingsCommand";
import { SendPipelineExecutionStepFailureCommandInput, SendPipelineExecutionStepFailureCommandOutput } from "../commands/SendPipelineExecutionStepFailureCommand";
import { SendPipelineExecutionStepSuccessCommandInput, SendPipelineExecutionStepSuccessCommandOutput } from "../commands/SendPipelineExecutionStepSuccessCommand";
import { StartEdgeDeploymentStageCommandInput, StartEdgeDeploymentStageCommandOutput } from "../commands/StartEdgeDeploymentStageCommand";
import { StartInferenceExperimentCommandInput, StartInferenceExperimentCommandOutput } from "../commands/StartInferenceExperimentCommand";
import { StartMlflowTrackingServerCommandInput, StartMlflowTrackingServerCommandOutput } from "../commands/StartMlflowTrackingServerCommand";
import { StartMonitoringScheduleCommandInput, StartMonitoringScheduleCommandOutput } from "../commands/StartMonitoringScheduleCommand";
import { StartNotebookInstanceCommandInput, StartNotebookInstanceCommandOutput } from "../commands/StartNotebookInstanceCommand";
import { StartPipelineExecutionCommandInput, StartPipelineExecutionCommandOutput } from "../commands/StartPipelineExecutionCommand";
import { StopAutoMLJobCommandInput, StopAutoMLJobCommandOutput } from "../commands/StopAutoMLJobCommand";
import { StopCompilationJobCommandInput, StopCompilationJobCommandOutput } from "../commands/StopCompilationJobCommand";
import { StopEdgeDeploymentStageCommandInput, StopEdgeDeploymentStageCommandOutput } from "../commands/StopEdgeDeploymentStageCommand";
import { StopEdgePackagingJobCommandInput, StopEdgePackagingJobCommandOutput } from "../commands/StopEdgePackagingJobCommand";
import { StopHyperParameterTuningJobCommandInput, StopHyperParameterTuningJobCommandOutput } from "../commands/StopHyperParameterTuningJobCommand";
import { StopInferenceExperimentCommandInput, StopInferenceExperimentCommandOutput } from "../commands/StopInferenceExperimentCommand";
import { StopInferenceRecommendationsJobCommandInput, StopInferenceRecommendationsJobCommandOutput } from "../commands/StopInferenceRecommendationsJobCommand";
import { StopLabelingJobCommandInput, StopLabelingJobCommandOutput } from "../commands/StopLabelingJobCommand";
import { StopMlflowTrackingServerCommandInput, StopMlflowTrackingServerCommandOutput } from "../commands/StopMlflowTrackingServerCommand";
import { StopMonitoringScheduleCommandInput, StopMonitoringScheduleCommandOutput } from "../commands/StopMonitoringScheduleCommand";
import { StopNotebookInstanceCommandInput, StopNotebookInstanceCommandOutput } from "../commands/StopNotebookInstanceCommand";
import { StopOptimizationJobCommandInput, StopOptimizationJobCommandOutput } from "../commands/StopOptimizationJobCommand";
import { StopPipelineExecutionCommandInput, StopPipelineExecutionCommandOutput } from "../commands/StopPipelineExecutionCommand";
import { StopProcessingJobCommandInput, StopProcessingJobCommandOutput } from "../commands/StopProcessingJobCommand";
import { StopTrainingJobCommandInput, StopTrainingJobCommandOutput } from "../commands/StopTrainingJobCommand";
import { StopTransformJobCommandInput, StopTransformJobCommandOutput } from "../commands/StopTransformJobCommand";
import { UpdateActionCommandInput, UpdateActionCommandOutput } from "../commands/UpdateActionCommand";
import { UpdateAppImageConfigCommandInput, UpdateAppImageConfigCommandOutput } from "../commands/UpdateAppImageConfigCommand";
import { UpdateArtifactCommandInput, UpdateArtifactCommandOutput } from "../commands/UpdateArtifactCommand";
import { UpdateClusterCommandInput, UpdateClusterCommandOutput } from "../commands/UpdateClusterCommand";
import { UpdateClusterSchedulerConfigCommandInput, UpdateClusterSchedulerConfigCommandOutput } from "../commands/UpdateClusterSchedulerConfigCommand";
import { UpdateClusterSoftwareCommandInput, UpdateClusterSoftwareCommandOutput } from "../commands/UpdateClusterSoftwareCommand";
import { UpdateCodeRepositoryCommandInput, UpdateCodeRepositoryCommandOutput } from "../commands/UpdateCodeRepositoryCommand";
import { UpdateComputeQuotaCommandInput, UpdateComputeQuotaCommandOutput } from "../commands/UpdateComputeQuotaCommand";
import { UpdateContextCommandInput, UpdateContextCommandOutput } from "../commands/UpdateContextCommand";
import { UpdateDeviceFleetCommandInput, UpdateDeviceFleetCommandOutput } from "../commands/UpdateDeviceFleetCommand";
import { UpdateDevicesCommandInput, UpdateDevicesCommandOutput } from "../commands/UpdateDevicesCommand";
import { UpdateDomainCommandInput, UpdateDomainCommandOutput } from "../commands/UpdateDomainCommand";
import { UpdateEndpointCommandInput, UpdateEndpointCommandOutput } from "../commands/UpdateEndpointCommand";
import { UpdateEndpointWeightsAndCapacitiesCommandInput, UpdateEndpointWeightsAndCapacitiesCommandOutput } from "../commands/UpdateEndpointWeightsAndCapacitiesCommand";
import { UpdateExperimentCommandInput, UpdateExperimentCommandOutput } from "../commands/UpdateExperimentCommand";
import { UpdateFeatureGroupCommandInput, UpdateFeatureGroupCommandOutput } from "../commands/UpdateFeatureGroupCommand";
import { UpdateFeatureMetadataCommandInput, UpdateFeatureMetadataCommandOutput } from "../commands/UpdateFeatureMetadataCommand";
import { UpdateHubCommandInput, UpdateHubCommandOutput } from "../commands/UpdateHubCommand";
import { UpdateHubContentCommandInput, UpdateHubContentCommandOutput } from "../commands/UpdateHubContentCommand";
import { UpdateHubContentReferenceCommandInput, UpdateHubContentReferenceCommandOutput } from "../commands/UpdateHubContentReferenceCommand";
import { UpdateImageCommandInput, UpdateImageCommandOutput } from "../commands/UpdateImageCommand";
import { UpdateImageVersionCommandInput, UpdateImageVersionCommandOutput } from "../commands/UpdateImageVersionCommand";
import { UpdateInferenceComponentCommandInput, UpdateInferenceComponentCommandOutput } from "../commands/UpdateInferenceComponentCommand";
import { UpdateInferenceComponentRuntimeConfigCommandInput, UpdateInferenceComponentRuntimeConfigCommandOutput } from "../commands/UpdateInferenceComponentRuntimeConfigCommand";
import { UpdateInferenceExperimentCommandInput, UpdateInferenceExperimentCommandOutput } from "../commands/UpdateInferenceExperimentCommand";
import { UpdateMlflowTrackingServerCommandInput, UpdateMlflowTrackingServerCommandOutput } from "../commands/UpdateMlflowTrackingServerCommand";
import { UpdateModelCardCommandInput, UpdateModelCardCommandOutput } from "../commands/UpdateModelCardCommand";
import { UpdateModelPackageCommandInput, UpdateModelPackageCommandOutput } from "../commands/UpdateModelPackageCommand";
import { UpdateMonitoringAlertCommandInput, UpdateMonitoringAlertCommandOutput } from "../commands/UpdateMonitoringAlertCommand";
import { UpdateMonitoringScheduleCommandInput, UpdateMonitoringScheduleCommandOutput } from "../commands/UpdateMonitoringScheduleCommand";
import { UpdateNotebookInstanceCommandInput, UpdateNotebookInstanceCommandOutput } from "../commands/UpdateNotebookInstanceCommand";
import { UpdateNotebookInstanceLifecycleConfigCommandInput, UpdateNotebookInstanceLifecycleConfigCommandOutput } from "../commands/UpdateNotebookInstanceLifecycleConfigCommand";
import { UpdatePartnerAppCommandInput, UpdatePartnerAppCommandOutput } from "../commands/UpdatePartnerAppCommand";
import { UpdatePipelineCommandInput, UpdatePipelineCommandOutput } from "../commands/UpdatePipelineCommand";
import { UpdatePipelineExecutionCommandInput, UpdatePipelineExecutionCommandOutput } from "../commands/UpdatePipelineExecutionCommand";
import { UpdateProjectCommandInput, UpdateProjectCommandOutput } from "../commands/UpdateProjectCommand";
import { UpdateSpaceCommandInput, UpdateSpaceCommandOutput } from "../commands/UpdateSpaceCommand";
import { UpdateTrainingJobCommandInput, UpdateTrainingJobCommandOutput } from "../commands/UpdateTrainingJobCommand";
import { UpdateTrialCommandInput, UpdateTrialCommandOutput } from "../commands/UpdateTrialCommand";
import { UpdateTrialComponentCommandInput, UpdateTrialComponentCommandOutput } from "../commands/UpdateTrialComponentCommand";
import { UpdateUserProfileCommandInput, UpdateUserProfileCommandOutput } from "../commands/UpdateUserProfileCommand";
import { UpdateWorkforceCommandInput, UpdateWorkforceCommandOutput } from "../commands/UpdateWorkforceCommand";
import { UpdateWorkteamCommandInput, UpdateWorkteamCommandOutput } from "../commands/UpdateWorkteamCommand";
/**
 * serializeAws_json1_1AddAssociationCommand
 */
export declare const se_AddAssociationCommand: (input: AddAssociationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1AddTagsCommand
 */
export declare const se_AddTagsCommand: (input: AddTagsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1AssociateTrialComponentCommand
 */
export declare const se_AssociateTrialComponentCommand: (input: AssociateTrialComponentCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1BatchDeleteClusterNodesCommand
 */
export declare const se_BatchDeleteClusterNodesCommand: (input: BatchDeleteClusterNodesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1BatchDescribeModelPackageCommand
 */
export declare const se_BatchDescribeModelPackageCommand: (input: BatchDescribeModelPackageCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateActionCommand
 */
export declare const se_CreateActionCommand: (input: CreateActionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateAlgorithmCommand
 */
export declare const se_CreateAlgorithmCommand: (input: CreateAlgorithmCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateAppCommand
 */
export declare const se_CreateAppCommand: (input: CreateAppCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateAppImageConfigCommand
 */
export declare const se_CreateAppImageConfigCommand: (input: CreateAppImageConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateArtifactCommand
 */
export declare const se_CreateArtifactCommand: (input: CreateArtifactCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateAutoMLJobCommand
 */
export declare const se_CreateAutoMLJobCommand: (input: CreateAutoMLJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateAutoMLJobV2Command
 */
export declare const se_CreateAutoMLJobV2Command: (input: CreateAutoMLJobV2CommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateClusterCommand
 */
export declare const se_CreateClusterCommand: (input: CreateClusterCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateClusterSchedulerConfigCommand
 */
export declare const se_CreateClusterSchedulerConfigCommand: (input: CreateClusterSchedulerConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateCodeRepositoryCommand
 */
export declare const se_CreateCodeRepositoryCommand: (input: CreateCodeRepositoryCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateCompilationJobCommand
 */
export declare const se_CreateCompilationJobCommand: (input: CreateCompilationJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateComputeQuotaCommand
 */
export declare const se_CreateComputeQuotaCommand: (input: CreateComputeQuotaCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateContextCommand
 */
export declare const se_CreateContextCommand: (input: CreateContextCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateDataQualityJobDefinitionCommand
 */
export declare const se_CreateDataQualityJobDefinitionCommand: (input: CreateDataQualityJobDefinitionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateDeviceFleetCommand
 */
export declare const se_CreateDeviceFleetCommand: (input: CreateDeviceFleetCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateDomainCommand
 */
export declare const se_CreateDomainCommand: (input: CreateDomainCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateEdgeDeploymentPlanCommand
 */
export declare const se_CreateEdgeDeploymentPlanCommand: (input: CreateEdgeDeploymentPlanCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateEdgeDeploymentStageCommand
 */
export declare const se_CreateEdgeDeploymentStageCommand: (input: CreateEdgeDeploymentStageCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateEdgePackagingJobCommand
 */
export declare const se_CreateEdgePackagingJobCommand: (input: CreateEdgePackagingJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateEndpointCommand
 */
export declare const se_CreateEndpointCommand: (input: CreateEndpointCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateEndpointConfigCommand
 */
export declare const se_CreateEndpointConfigCommand: (input: CreateEndpointConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateExperimentCommand
 */
export declare const se_CreateExperimentCommand: (input: CreateExperimentCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateFeatureGroupCommand
 */
export declare const se_CreateFeatureGroupCommand: (input: CreateFeatureGroupCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateFlowDefinitionCommand
 */
export declare const se_CreateFlowDefinitionCommand: (input: CreateFlowDefinitionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateHubCommand
 */
export declare const se_CreateHubCommand: (input: CreateHubCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateHubContentReferenceCommand
 */
export declare const se_CreateHubContentReferenceCommand: (input: CreateHubContentReferenceCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateHumanTaskUiCommand
 */
export declare const se_CreateHumanTaskUiCommand: (input: CreateHumanTaskUiCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateHyperParameterTuningJobCommand
 */
export declare const se_CreateHyperParameterTuningJobCommand: (input: CreateHyperParameterTuningJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateImageCommand
 */
export declare const se_CreateImageCommand: (input: CreateImageCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateImageVersionCommand
 */
export declare const se_CreateImageVersionCommand: (input: CreateImageVersionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateInferenceComponentCommand
 */
export declare const se_CreateInferenceComponentCommand: (input: CreateInferenceComponentCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateInferenceExperimentCommand
 */
export declare const se_CreateInferenceExperimentCommand: (input: CreateInferenceExperimentCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateInferenceRecommendationsJobCommand
 */
export declare const se_CreateInferenceRecommendationsJobCommand: (input: CreateInferenceRecommendationsJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateLabelingJobCommand
 */
export declare const se_CreateLabelingJobCommand: (input: CreateLabelingJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateMlflowTrackingServerCommand
 */
export declare const se_CreateMlflowTrackingServerCommand: (input: CreateMlflowTrackingServerCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateModelCommand
 */
export declare const se_CreateModelCommand: (input: CreateModelCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateModelBiasJobDefinitionCommand
 */
export declare const se_CreateModelBiasJobDefinitionCommand: (input: CreateModelBiasJobDefinitionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateModelCardCommand
 */
export declare const se_CreateModelCardCommand: (input: CreateModelCardCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateModelCardExportJobCommand
 */
export declare const se_CreateModelCardExportJobCommand: (input: CreateModelCardExportJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateModelExplainabilityJobDefinitionCommand
 */
export declare const se_CreateModelExplainabilityJobDefinitionCommand: (input: CreateModelExplainabilityJobDefinitionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateModelPackageCommand
 */
export declare const se_CreateModelPackageCommand: (input: CreateModelPackageCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateModelPackageGroupCommand
 */
export declare const se_CreateModelPackageGroupCommand: (input: CreateModelPackageGroupCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateModelQualityJobDefinitionCommand
 */
export declare const se_CreateModelQualityJobDefinitionCommand: (input: CreateModelQualityJobDefinitionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateMonitoringScheduleCommand
 */
export declare const se_CreateMonitoringScheduleCommand: (input: CreateMonitoringScheduleCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateNotebookInstanceCommand
 */
export declare const se_CreateNotebookInstanceCommand: (input: CreateNotebookInstanceCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateNotebookInstanceLifecycleConfigCommand
 */
export declare const se_CreateNotebookInstanceLifecycleConfigCommand: (input: CreateNotebookInstanceLifecycleConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateOptimizationJobCommand
 */
export declare const se_CreateOptimizationJobCommand: (input: CreateOptimizationJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreatePartnerAppCommand
 */
export declare const se_CreatePartnerAppCommand: (input: CreatePartnerAppCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreatePartnerAppPresignedUrlCommand
 */
export declare const se_CreatePartnerAppPresignedUrlCommand: (input: CreatePartnerAppPresignedUrlCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreatePipelineCommand
 */
export declare const se_CreatePipelineCommand: (input: CreatePipelineCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreatePresignedDomainUrlCommand
 */
export declare const se_CreatePresignedDomainUrlCommand: (input: CreatePresignedDomainUrlCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreatePresignedMlflowTrackingServerUrlCommand
 */
export declare const se_CreatePresignedMlflowTrackingServerUrlCommand: (input: CreatePresignedMlflowTrackingServerUrlCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreatePresignedNotebookInstanceUrlCommand
 */
export declare const se_CreatePresignedNotebookInstanceUrlCommand: (input: CreatePresignedNotebookInstanceUrlCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateProcessingJobCommand
 */
export declare const se_CreateProcessingJobCommand: (input: CreateProcessingJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateProjectCommand
 */
export declare const se_CreateProjectCommand: (input: CreateProjectCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateSpaceCommand
 */
export declare const se_CreateSpaceCommand: (input: CreateSpaceCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateStudioLifecycleConfigCommand
 */
export declare const se_CreateStudioLifecycleConfigCommand: (input: CreateStudioLifecycleConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateTrainingJobCommand
 */
export declare const se_CreateTrainingJobCommand: (input: CreateTrainingJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateTrainingPlanCommand
 */
export declare const se_CreateTrainingPlanCommand: (input: CreateTrainingPlanCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateTransformJobCommand
 */
export declare const se_CreateTransformJobCommand: (input: CreateTransformJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateTrialCommand
 */
export declare const se_CreateTrialCommand: (input: CreateTrialCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateTrialComponentCommand
 */
export declare const se_CreateTrialComponentCommand: (input: CreateTrialComponentCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateUserProfileCommand
 */
export declare const se_CreateUserProfileCommand: (input: CreateUserProfileCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateWorkforceCommand
 */
export declare const se_CreateWorkforceCommand: (input: CreateWorkforceCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1CreateWorkteamCommand
 */
export declare const se_CreateWorkteamCommand: (input: CreateWorkteamCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteActionCommand
 */
export declare const se_DeleteActionCommand: (input: DeleteActionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteAlgorithmCommand
 */
export declare const se_DeleteAlgorithmCommand: (input: DeleteAlgorithmCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteAppCommand
 */
export declare const se_DeleteAppCommand: (input: DeleteAppCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteAppImageConfigCommand
 */
export declare const se_DeleteAppImageConfigCommand: (input: DeleteAppImageConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteArtifactCommand
 */
export declare const se_DeleteArtifactCommand: (input: DeleteArtifactCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteAssociationCommand
 */
export declare const se_DeleteAssociationCommand: (input: DeleteAssociationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteClusterCommand
 */
export declare const se_DeleteClusterCommand: (input: DeleteClusterCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteClusterSchedulerConfigCommand
 */
export declare const se_DeleteClusterSchedulerConfigCommand: (input: DeleteClusterSchedulerConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteCodeRepositoryCommand
 */
export declare const se_DeleteCodeRepositoryCommand: (input: DeleteCodeRepositoryCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteCompilationJobCommand
 */
export declare const se_DeleteCompilationJobCommand: (input: DeleteCompilationJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteComputeQuotaCommand
 */
export declare const se_DeleteComputeQuotaCommand: (input: DeleteComputeQuotaCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteContextCommand
 */
export declare const se_DeleteContextCommand: (input: DeleteContextCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteDataQualityJobDefinitionCommand
 */
export declare const se_DeleteDataQualityJobDefinitionCommand: (input: DeleteDataQualityJobDefinitionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteDeviceFleetCommand
 */
export declare const se_DeleteDeviceFleetCommand: (input: DeleteDeviceFleetCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteDomainCommand
 */
export declare const se_DeleteDomainCommand: (input: DeleteDomainCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteEdgeDeploymentPlanCommand
 */
export declare const se_DeleteEdgeDeploymentPlanCommand: (input: DeleteEdgeDeploymentPlanCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteEdgeDeploymentStageCommand
 */
export declare const se_DeleteEdgeDeploymentStageCommand: (input: DeleteEdgeDeploymentStageCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteEndpointCommand
 */
export declare const se_DeleteEndpointCommand: (input: DeleteEndpointCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteEndpointConfigCommand
 */
export declare const se_DeleteEndpointConfigCommand: (input: DeleteEndpointConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteExperimentCommand
 */
export declare const se_DeleteExperimentCommand: (input: DeleteExperimentCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteFeatureGroupCommand
 */
export declare const se_DeleteFeatureGroupCommand: (input: DeleteFeatureGroupCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteFlowDefinitionCommand
 */
export declare const se_DeleteFlowDefinitionCommand: (input: DeleteFlowDefinitionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteHubCommand
 */
export declare const se_DeleteHubCommand: (input: DeleteHubCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteHubContentCommand
 */
export declare const se_DeleteHubContentCommand: (input: DeleteHubContentCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteHubContentReferenceCommand
 */
export declare const se_DeleteHubContentReferenceCommand: (input: DeleteHubContentReferenceCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteHumanTaskUiCommand
 */
export declare const se_DeleteHumanTaskUiCommand: (input: DeleteHumanTaskUiCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteHyperParameterTuningJobCommand
 */
export declare const se_DeleteHyperParameterTuningJobCommand: (input: DeleteHyperParameterTuningJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteImageCommand
 */
export declare const se_DeleteImageCommand: (input: DeleteImageCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteImageVersionCommand
 */
export declare const se_DeleteImageVersionCommand: (input: DeleteImageVersionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteInferenceComponentCommand
 */
export declare const se_DeleteInferenceComponentCommand: (input: DeleteInferenceComponentCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteInferenceExperimentCommand
 */
export declare const se_DeleteInferenceExperimentCommand: (input: DeleteInferenceExperimentCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteMlflowTrackingServerCommand
 */
export declare const se_DeleteMlflowTrackingServerCommand: (input: DeleteMlflowTrackingServerCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteModelCommand
 */
export declare const se_DeleteModelCommand: (input: DeleteModelCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteModelBiasJobDefinitionCommand
 */
export declare const se_DeleteModelBiasJobDefinitionCommand: (input: DeleteModelBiasJobDefinitionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteModelCardCommand
 */
export declare const se_DeleteModelCardCommand: (input: DeleteModelCardCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteModelExplainabilityJobDefinitionCommand
 */
export declare const se_DeleteModelExplainabilityJobDefinitionCommand: (input: DeleteModelExplainabilityJobDefinitionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteModelPackageCommand
 */
export declare const se_DeleteModelPackageCommand: (input: DeleteModelPackageCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteModelPackageGroupCommand
 */
export declare const se_DeleteModelPackageGroupCommand: (input: DeleteModelPackageGroupCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteModelPackageGroupPolicyCommand
 */
export declare const se_DeleteModelPackageGroupPolicyCommand: (input: DeleteModelPackageGroupPolicyCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteModelQualityJobDefinitionCommand
 */
export declare const se_DeleteModelQualityJobDefinitionCommand: (input: DeleteModelQualityJobDefinitionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteMonitoringScheduleCommand
 */
export declare const se_DeleteMonitoringScheduleCommand: (input: DeleteMonitoringScheduleCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteNotebookInstanceCommand
 */
export declare const se_DeleteNotebookInstanceCommand: (input: DeleteNotebookInstanceCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteNotebookInstanceLifecycleConfigCommand
 */
export declare const se_DeleteNotebookInstanceLifecycleConfigCommand: (input: DeleteNotebookInstanceLifecycleConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteOptimizationJobCommand
 */
export declare const se_DeleteOptimizationJobCommand: (input: DeleteOptimizationJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeletePartnerAppCommand
 */
export declare const se_DeletePartnerAppCommand: (input: DeletePartnerAppCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeletePipelineCommand
 */
export declare const se_DeletePipelineCommand: (input: DeletePipelineCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteProjectCommand
 */
export declare const se_DeleteProjectCommand: (input: DeleteProjectCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteSpaceCommand
 */
export declare const se_DeleteSpaceCommand: (input: DeleteSpaceCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteStudioLifecycleConfigCommand
 */
export declare const se_DeleteStudioLifecycleConfigCommand: (input: DeleteStudioLifecycleConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteTagsCommand
 */
export declare const se_DeleteTagsCommand: (input: DeleteTagsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteTrialCommand
 */
export declare const se_DeleteTrialCommand: (input: DeleteTrialCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteTrialComponentCommand
 */
export declare const se_DeleteTrialComponentCommand: (input: DeleteTrialComponentCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteUserProfileCommand
 */
export declare const se_DeleteUserProfileCommand: (input: DeleteUserProfileCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteWorkforceCommand
 */
export declare const se_DeleteWorkforceCommand: (input: DeleteWorkforceCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteWorkteamCommand
 */
export declare const se_DeleteWorkteamCommand: (input: DeleteWorkteamCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeregisterDevicesCommand
 */
export declare const se_DeregisterDevicesCommand: (input: DeregisterDevicesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeActionCommand
 */
export declare const se_DescribeActionCommand: (input: DescribeActionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeAlgorithmCommand
 */
export declare const se_DescribeAlgorithmCommand: (input: DescribeAlgorithmCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeAppCommand
 */
export declare const se_DescribeAppCommand: (input: DescribeAppCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeAppImageConfigCommand
 */
export declare const se_DescribeAppImageConfigCommand: (input: DescribeAppImageConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeArtifactCommand
 */
export declare const se_DescribeArtifactCommand: (input: DescribeArtifactCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeAutoMLJobCommand
 */
export declare const se_DescribeAutoMLJobCommand: (input: DescribeAutoMLJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeAutoMLJobV2Command
 */
export declare const se_DescribeAutoMLJobV2Command: (input: DescribeAutoMLJobV2CommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeClusterCommand
 */
export declare const se_DescribeClusterCommand: (input: DescribeClusterCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeClusterNodeCommand
 */
export declare const se_DescribeClusterNodeCommand: (input: DescribeClusterNodeCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeClusterSchedulerConfigCommand
 */
export declare const se_DescribeClusterSchedulerConfigCommand: (input: DescribeClusterSchedulerConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeCodeRepositoryCommand
 */
export declare const se_DescribeCodeRepositoryCommand: (input: DescribeCodeRepositoryCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeCompilationJobCommand
 */
export declare const se_DescribeCompilationJobCommand: (input: DescribeCompilationJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeComputeQuotaCommand
 */
export declare const se_DescribeComputeQuotaCommand: (input: DescribeComputeQuotaCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeContextCommand
 */
export declare const se_DescribeContextCommand: (input: DescribeContextCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeDataQualityJobDefinitionCommand
 */
export declare const se_DescribeDataQualityJobDefinitionCommand: (input: DescribeDataQualityJobDefinitionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeDeviceCommand
 */
export declare const se_DescribeDeviceCommand: (input: DescribeDeviceCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeDeviceFleetCommand
 */
export declare const se_DescribeDeviceFleetCommand: (input: DescribeDeviceFleetCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeDomainCommand
 */
export declare const se_DescribeDomainCommand: (input: DescribeDomainCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeEdgeDeploymentPlanCommand
 */
export declare const se_DescribeEdgeDeploymentPlanCommand: (input: DescribeEdgeDeploymentPlanCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeEdgePackagingJobCommand
 */
export declare const se_DescribeEdgePackagingJobCommand: (input: DescribeEdgePackagingJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeEndpointCommand
 */
export declare const se_DescribeEndpointCommand: (input: DescribeEndpointCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeEndpointConfigCommand
 */
export declare const se_DescribeEndpointConfigCommand: (input: DescribeEndpointConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeExperimentCommand
 */
export declare const se_DescribeExperimentCommand: (input: DescribeExperimentCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeFeatureGroupCommand
 */
export declare const se_DescribeFeatureGroupCommand: (input: DescribeFeatureGroupCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeFeatureMetadataCommand
 */
export declare const se_DescribeFeatureMetadataCommand: (input: DescribeFeatureMetadataCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeFlowDefinitionCommand
 */
export declare const se_DescribeFlowDefinitionCommand: (input: DescribeFlowDefinitionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeHubCommand
 */
export declare const se_DescribeHubCommand: (input: DescribeHubCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeHubContentCommand
 */
export declare const se_DescribeHubContentCommand: (input: DescribeHubContentCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeHumanTaskUiCommand
 */
export declare const se_DescribeHumanTaskUiCommand: (input: DescribeHumanTaskUiCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeHyperParameterTuningJobCommand
 */
export declare const se_DescribeHyperParameterTuningJobCommand: (input: DescribeHyperParameterTuningJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeImageCommand
 */
export declare const se_DescribeImageCommand: (input: DescribeImageCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeImageVersionCommand
 */
export declare const se_DescribeImageVersionCommand: (input: DescribeImageVersionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeInferenceComponentCommand
 */
export declare const se_DescribeInferenceComponentCommand: (input: DescribeInferenceComponentCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeInferenceExperimentCommand
 */
export declare const se_DescribeInferenceExperimentCommand: (input: DescribeInferenceExperimentCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeInferenceRecommendationsJobCommand
 */
export declare const se_DescribeInferenceRecommendationsJobCommand: (input: DescribeInferenceRecommendationsJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeLabelingJobCommand
 */
export declare const se_DescribeLabelingJobCommand: (input: DescribeLabelingJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeLineageGroupCommand
 */
export declare const se_DescribeLineageGroupCommand: (input: DescribeLineageGroupCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeMlflowTrackingServerCommand
 */
export declare const se_DescribeMlflowTrackingServerCommand: (input: DescribeMlflowTrackingServerCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeModelCommand
 */
export declare const se_DescribeModelCommand: (input: DescribeModelCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeModelBiasJobDefinitionCommand
 */
export declare const se_DescribeModelBiasJobDefinitionCommand: (input: DescribeModelBiasJobDefinitionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeModelCardCommand
 */
export declare const se_DescribeModelCardCommand: (input: DescribeModelCardCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeModelCardExportJobCommand
 */
export declare const se_DescribeModelCardExportJobCommand: (input: DescribeModelCardExportJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeModelExplainabilityJobDefinitionCommand
 */
export declare const se_DescribeModelExplainabilityJobDefinitionCommand: (input: DescribeModelExplainabilityJobDefinitionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeModelPackageCommand
 */
export declare const se_DescribeModelPackageCommand: (input: DescribeModelPackageCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeModelPackageGroupCommand
 */
export declare const se_DescribeModelPackageGroupCommand: (input: DescribeModelPackageGroupCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeModelQualityJobDefinitionCommand
 */
export declare const se_DescribeModelQualityJobDefinitionCommand: (input: DescribeModelQualityJobDefinitionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeMonitoringScheduleCommand
 */
export declare const se_DescribeMonitoringScheduleCommand: (input: DescribeMonitoringScheduleCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeNotebookInstanceCommand
 */
export declare const se_DescribeNotebookInstanceCommand: (input: DescribeNotebookInstanceCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeNotebookInstanceLifecycleConfigCommand
 */
export declare const se_DescribeNotebookInstanceLifecycleConfigCommand: (input: DescribeNotebookInstanceLifecycleConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeOptimizationJobCommand
 */
export declare const se_DescribeOptimizationJobCommand: (input: DescribeOptimizationJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribePartnerAppCommand
 */
export declare const se_DescribePartnerAppCommand: (input: DescribePartnerAppCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribePipelineCommand
 */
export declare const se_DescribePipelineCommand: (input: DescribePipelineCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribePipelineDefinitionForExecutionCommand
 */
export declare const se_DescribePipelineDefinitionForExecutionCommand: (input: DescribePipelineDefinitionForExecutionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribePipelineExecutionCommand
 */
export declare const se_DescribePipelineExecutionCommand: (input: DescribePipelineExecutionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeProcessingJobCommand
 */
export declare const se_DescribeProcessingJobCommand: (input: DescribeProcessingJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeProjectCommand
 */
export declare const se_DescribeProjectCommand: (input: DescribeProjectCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeSpaceCommand
 */
export declare const se_DescribeSpaceCommand: (input: DescribeSpaceCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeStudioLifecycleConfigCommand
 */
export declare const se_DescribeStudioLifecycleConfigCommand: (input: DescribeStudioLifecycleConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeSubscribedWorkteamCommand
 */
export declare const se_DescribeSubscribedWorkteamCommand: (input: DescribeSubscribedWorkteamCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeTrainingJobCommand
 */
export declare const se_DescribeTrainingJobCommand: (input: DescribeTrainingJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeTrainingPlanCommand
 */
export declare const se_DescribeTrainingPlanCommand: (input: DescribeTrainingPlanCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeTransformJobCommand
 */
export declare const se_DescribeTransformJobCommand: (input: DescribeTransformJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeTrialCommand
 */
export declare const se_DescribeTrialCommand: (input: DescribeTrialCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeTrialComponentCommand
 */
export declare const se_DescribeTrialComponentCommand: (input: DescribeTrialComponentCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeUserProfileCommand
 */
export declare const se_DescribeUserProfileCommand: (input: DescribeUserProfileCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeWorkforceCommand
 */
export declare const se_DescribeWorkforceCommand: (input: DescribeWorkforceCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeWorkteamCommand
 */
export declare const se_DescribeWorkteamCommand: (input: DescribeWorkteamCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DisableSagemakerServicecatalogPortfolioCommand
 */
export declare const se_DisableSagemakerServicecatalogPortfolioCommand: (input: DisableSagemakerServicecatalogPortfolioCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DisassociateTrialComponentCommand
 */
export declare const se_DisassociateTrialComponentCommand: (input: DisassociateTrialComponentCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1EnableSagemakerServicecatalogPortfolioCommand
 */
export declare const se_EnableSagemakerServicecatalogPortfolioCommand: (input: EnableSagemakerServicecatalogPortfolioCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1GetDeviceFleetReportCommand
 */
export declare const se_GetDeviceFleetReportCommand: (input: GetDeviceFleetReportCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1GetLineageGroupPolicyCommand
 */
export declare const se_GetLineageGroupPolicyCommand: (input: GetLineageGroupPolicyCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1GetModelPackageGroupPolicyCommand
 */
export declare const se_GetModelPackageGroupPolicyCommand: (input: GetModelPackageGroupPolicyCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1GetSagemakerServicecatalogPortfolioStatusCommand
 */
export declare const se_GetSagemakerServicecatalogPortfolioStatusCommand: (input: GetSagemakerServicecatalogPortfolioStatusCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1GetScalingConfigurationRecommendationCommand
 */
export declare const se_GetScalingConfigurationRecommendationCommand: (input: GetScalingConfigurationRecommendationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1GetSearchSuggestionsCommand
 */
export declare const se_GetSearchSuggestionsCommand: (input: GetSearchSuggestionsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ImportHubContentCommand
 */
export declare const se_ImportHubContentCommand: (input: ImportHubContentCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListActionsCommand
 */
export declare const se_ListActionsCommand: (input: ListActionsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListAlgorithmsCommand
 */
export declare const se_ListAlgorithmsCommand: (input: ListAlgorithmsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListAliasesCommand
 */
export declare const se_ListAliasesCommand: (input: ListAliasesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListAppImageConfigsCommand
 */
export declare const se_ListAppImageConfigsCommand: (input: ListAppImageConfigsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListAppsCommand
 */
export declare const se_ListAppsCommand: (input: ListAppsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListArtifactsCommand
 */
export declare const se_ListArtifactsCommand: (input: ListArtifactsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListAssociationsCommand
 */
export declare const se_ListAssociationsCommand: (input: ListAssociationsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListAutoMLJobsCommand
 */
export declare const se_ListAutoMLJobsCommand: (input: ListAutoMLJobsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListCandidatesForAutoMLJobCommand
 */
export declare const se_ListCandidatesForAutoMLJobCommand: (input: ListCandidatesForAutoMLJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListClusterNodesCommand
 */
export declare const se_ListClusterNodesCommand: (input: ListClusterNodesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListClustersCommand
 */
export declare const se_ListClustersCommand: (input: ListClustersCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListClusterSchedulerConfigsCommand
 */
export declare const se_ListClusterSchedulerConfigsCommand: (input: ListClusterSchedulerConfigsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListCodeRepositoriesCommand
 */
export declare const se_ListCodeRepositoriesCommand: (input: ListCodeRepositoriesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListCompilationJobsCommand
 */
export declare const se_ListCompilationJobsCommand: (input: ListCompilationJobsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListComputeQuotasCommand
 */
export declare const se_ListComputeQuotasCommand: (input: ListComputeQuotasCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListContextsCommand
 */
export declare const se_ListContextsCommand: (input: ListContextsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListDataQualityJobDefinitionsCommand
 */
export declare const se_ListDataQualityJobDefinitionsCommand: (input: ListDataQualityJobDefinitionsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListDeviceFleetsCommand
 */
export declare const se_ListDeviceFleetsCommand: (input: ListDeviceFleetsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListDevicesCommand
 */
export declare const se_ListDevicesCommand: (input: ListDevicesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListDomainsCommand
 */
export declare const se_ListDomainsCommand: (input: ListDomainsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListEdgeDeploymentPlansCommand
 */
export declare const se_ListEdgeDeploymentPlansCommand: (input: ListEdgeDeploymentPlansCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListEdgePackagingJobsCommand
 */
export declare const se_ListEdgePackagingJobsCommand: (input: ListEdgePackagingJobsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListEndpointConfigsCommand
 */
export declare const se_ListEndpointConfigsCommand: (input: ListEndpointConfigsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListEndpointsCommand
 */
export declare const se_ListEndpointsCommand: (input: ListEndpointsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListExperimentsCommand
 */
export declare const se_ListExperimentsCommand: (input: ListExperimentsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListFeatureGroupsCommand
 */
export declare const se_ListFeatureGroupsCommand: (input: ListFeatureGroupsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListFlowDefinitionsCommand
 */
export declare const se_ListFlowDefinitionsCommand: (input: ListFlowDefinitionsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListHubContentsCommand
 */
export declare const se_ListHubContentsCommand: (input: ListHubContentsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListHubContentVersionsCommand
 */
export declare const se_ListHubContentVersionsCommand: (input: ListHubContentVersionsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListHubsCommand
 */
export declare const se_ListHubsCommand: (input: ListHubsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListHumanTaskUisCommand
 */
export declare const se_ListHumanTaskUisCommand: (input: ListHumanTaskUisCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListHyperParameterTuningJobsCommand
 */
export declare const se_ListHyperParameterTuningJobsCommand: (input: ListHyperParameterTuningJobsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListImagesCommand
 */
export declare const se_ListImagesCommand: (input: ListImagesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListImageVersionsCommand
 */
export declare const se_ListImageVersionsCommand: (input: ListImageVersionsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListInferenceComponentsCommand
 */
export declare const se_ListInferenceComponentsCommand: (input: ListInferenceComponentsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListInferenceExperimentsCommand
 */
export declare const se_ListInferenceExperimentsCommand: (input: ListInferenceExperimentsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListInferenceRecommendationsJobsCommand
 */
export declare const se_ListInferenceRecommendationsJobsCommand: (input: ListInferenceRecommendationsJobsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListInferenceRecommendationsJobStepsCommand
 */
export declare const se_ListInferenceRecommendationsJobStepsCommand: (input: ListInferenceRecommendationsJobStepsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListLabelingJobsCommand
 */
export declare const se_ListLabelingJobsCommand: (input: ListLabelingJobsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListLabelingJobsForWorkteamCommand
 */
export declare const se_ListLabelingJobsForWorkteamCommand: (input: ListLabelingJobsForWorkteamCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListLineageGroupsCommand
 */
export declare const se_ListLineageGroupsCommand: (input: ListLineageGroupsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListMlflowTrackingServersCommand
 */
export declare const se_ListMlflowTrackingServersCommand: (input: ListMlflowTrackingServersCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListModelBiasJobDefinitionsCommand
 */
export declare const se_ListModelBiasJobDefinitionsCommand: (input: ListModelBiasJobDefinitionsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListModelCardExportJobsCommand
 */
export declare const se_ListModelCardExportJobsCommand: (input: ListModelCardExportJobsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListModelCardsCommand
 */
export declare const se_ListModelCardsCommand: (input: ListModelCardsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListModelCardVersionsCommand
 */
export declare const se_ListModelCardVersionsCommand: (input: ListModelCardVersionsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListModelExplainabilityJobDefinitionsCommand
 */
export declare const se_ListModelExplainabilityJobDefinitionsCommand: (input: ListModelExplainabilityJobDefinitionsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListModelMetadataCommand
 */
export declare const se_ListModelMetadataCommand: (input: ListModelMetadataCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListModelPackageGroupsCommand
 */
export declare const se_ListModelPackageGroupsCommand: (input: ListModelPackageGroupsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListModelPackagesCommand
 */
export declare const se_ListModelPackagesCommand: (input: ListModelPackagesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListModelQualityJobDefinitionsCommand
 */
export declare const se_ListModelQualityJobDefinitionsCommand: (input: ListModelQualityJobDefinitionsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListModelsCommand
 */
export declare const se_ListModelsCommand: (input: ListModelsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListMonitoringAlertHistoryCommand
 */
export declare const se_ListMonitoringAlertHistoryCommand: (input: ListMonitoringAlertHistoryCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListMonitoringAlertsCommand
 */
export declare const se_ListMonitoringAlertsCommand: (input: ListMonitoringAlertsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListMonitoringExecutionsCommand
 */
export declare const se_ListMonitoringExecutionsCommand: (input: ListMonitoringExecutionsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListMonitoringSchedulesCommand
 */
export declare const se_ListMonitoringSchedulesCommand: (input: ListMonitoringSchedulesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListNotebookInstanceLifecycleConfigsCommand
 */
export declare const se_ListNotebookInstanceLifecycleConfigsCommand: (input: ListNotebookInstanceLifecycleConfigsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListNotebookInstancesCommand
 */
export declare const se_ListNotebookInstancesCommand: (input: ListNotebookInstancesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListOptimizationJobsCommand
 */
export declare const se_ListOptimizationJobsCommand: (input: ListOptimizationJobsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListPartnerAppsCommand
 */
export declare const se_ListPartnerAppsCommand: (input: ListPartnerAppsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListPipelineExecutionsCommand
 */
export declare const se_ListPipelineExecutionsCommand: (input: ListPipelineExecutionsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListPipelineExecutionStepsCommand
 */
export declare const se_ListPipelineExecutionStepsCommand: (input: ListPipelineExecutionStepsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListPipelineParametersForExecutionCommand
 */
export declare const se_ListPipelineParametersForExecutionCommand: (input: ListPipelineParametersForExecutionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListPipelinesCommand
 */
export declare const se_ListPipelinesCommand: (input: ListPipelinesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListProcessingJobsCommand
 */
export declare const se_ListProcessingJobsCommand: (input: ListProcessingJobsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListProjectsCommand
 */
export declare const se_ListProjectsCommand: (input: ListProjectsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListResourceCatalogsCommand
 */
export declare const se_ListResourceCatalogsCommand: (input: ListResourceCatalogsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListSpacesCommand
 */
export declare const se_ListSpacesCommand: (input: ListSpacesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListStageDevicesCommand
 */
export declare const se_ListStageDevicesCommand: (input: ListStageDevicesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListStudioLifecycleConfigsCommand
 */
export declare const se_ListStudioLifecycleConfigsCommand: (input: ListStudioLifecycleConfigsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListSubscribedWorkteamsCommand
 */
export declare const se_ListSubscribedWorkteamsCommand: (input: ListSubscribedWorkteamsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListTagsCommand
 */
export declare const se_ListTagsCommand: (input: ListTagsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListTrainingJobsCommand
 */
export declare const se_ListTrainingJobsCommand: (input: ListTrainingJobsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListTrainingJobsForHyperParameterTuningJobCommand
 */
export declare const se_ListTrainingJobsForHyperParameterTuningJobCommand: (input: ListTrainingJobsForHyperParameterTuningJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListTrainingPlansCommand
 */
export declare const se_ListTrainingPlansCommand: (input: ListTrainingPlansCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListTransformJobsCommand
 */
export declare const se_ListTransformJobsCommand: (input: ListTransformJobsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListTrialComponentsCommand
 */
export declare const se_ListTrialComponentsCommand: (input: ListTrialComponentsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListTrialsCommand
 */
export declare const se_ListTrialsCommand: (input: ListTrialsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListUserProfilesCommand
 */
export declare const se_ListUserProfilesCommand: (input: ListUserProfilesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListWorkforcesCommand
 */
export declare const se_ListWorkforcesCommand: (input: ListWorkforcesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListWorkteamsCommand
 */
export declare const se_ListWorkteamsCommand: (input: ListWorkteamsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1PutModelPackageGroupPolicyCommand
 */
export declare const se_PutModelPackageGroupPolicyCommand: (input: PutModelPackageGroupPolicyCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1QueryLineageCommand
 */
export declare const se_QueryLineageCommand: (input: QueryLineageCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1RegisterDevicesCommand
 */
export declare const se_RegisterDevicesCommand: (input: RegisterDevicesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1RenderUiTemplateCommand
 */
export declare const se_RenderUiTemplateCommand: (input: RenderUiTemplateCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1RetryPipelineExecutionCommand
 */
export declare const se_RetryPipelineExecutionCommand: (input: RetryPipelineExecutionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1SearchCommand
 */
export declare const se_SearchCommand: (input: SearchCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1SearchTrainingPlanOfferingsCommand
 */
export declare const se_SearchTrainingPlanOfferingsCommand: (input: SearchTrainingPlanOfferingsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1SendPipelineExecutionStepFailureCommand
 */
export declare const se_SendPipelineExecutionStepFailureCommand: (input: SendPipelineExecutionStepFailureCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1SendPipelineExecutionStepSuccessCommand
 */
export declare const se_SendPipelineExecutionStepSuccessCommand: (input: SendPipelineExecutionStepSuccessCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1StartEdgeDeploymentStageCommand
 */
export declare const se_StartEdgeDeploymentStageCommand: (input: StartEdgeDeploymentStageCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1StartInferenceExperimentCommand
 */
export declare const se_StartInferenceExperimentCommand: (input: StartInferenceExperimentCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1StartMlflowTrackingServerCommand
 */
export declare const se_StartMlflowTrackingServerCommand: (input: StartMlflowTrackingServerCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1StartMonitoringScheduleCommand
 */
export declare const se_StartMonitoringScheduleCommand: (input: StartMonitoringScheduleCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1StartNotebookInstanceCommand
 */
export declare const se_StartNotebookInstanceCommand: (input: StartNotebookInstanceCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1StartPipelineExecutionCommand
 */
export declare const se_StartPipelineExecutionCommand: (input: StartPipelineExecutionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1StopAutoMLJobCommand
 */
export declare const se_StopAutoMLJobCommand: (input: StopAutoMLJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1StopCompilationJobCommand
 */
export declare const se_StopCompilationJobCommand: (input: StopCompilationJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1StopEdgeDeploymentStageCommand
 */
export declare const se_StopEdgeDeploymentStageCommand: (input: StopEdgeDeploymentStageCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1StopEdgePackagingJobCommand
 */
export declare const se_StopEdgePackagingJobCommand: (input: StopEdgePackagingJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1StopHyperParameterTuningJobCommand
 */
export declare const se_StopHyperParameterTuningJobCommand: (input: StopHyperParameterTuningJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1StopInferenceExperimentCommand
 */
export declare const se_StopInferenceExperimentCommand: (input: StopInferenceExperimentCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1StopInferenceRecommendationsJobCommand
 */
export declare const se_StopInferenceRecommendationsJobCommand: (input: StopInferenceRecommendationsJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1StopLabelingJobCommand
 */
export declare const se_StopLabelingJobCommand: (input: StopLabelingJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1StopMlflowTrackingServerCommand
 */
export declare const se_StopMlflowTrackingServerCommand: (input: StopMlflowTrackingServerCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1StopMonitoringScheduleCommand
 */
export declare const se_StopMonitoringScheduleCommand: (input: StopMonitoringScheduleCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1StopNotebookInstanceCommand
 */
export declare const se_StopNotebookInstanceCommand: (input: StopNotebookInstanceCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1StopOptimizationJobCommand
 */
export declare const se_StopOptimizationJobCommand: (input: StopOptimizationJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1StopPipelineExecutionCommand
 */
export declare const se_StopPipelineExecutionCommand: (input: StopPipelineExecutionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1StopProcessingJobCommand
 */
export declare const se_StopProcessingJobCommand: (input: StopProcessingJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1StopTrainingJobCommand
 */
export declare const se_StopTrainingJobCommand: (input: StopTrainingJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1StopTransformJobCommand
 */
export declare const se_StopTransformJobCommand: (input: StopTransformJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateActionCommand
 */
export declare const se_UpdateActionCommand: (input: UpdateActionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateAppImageConfigCommand
 */
export declare const se_UpdateAppImageConfigCommand: (input: UpdateAppImageConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateArtifactCommand
 */
export declare const se_UpdateArtifactCommand: (input: UpdateArtifactCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateClusterCommand
 */
export declare const se_UpdateClusterCommand: (input: UpdateClusterCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateClusterSchedulerConfigCommand
 */
export declare const se_UpdateClusterSchedulerConfigCommand: (input: UpdateClusterSchedulerConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateClusterSoftwareCommand
 */
export declare const se_UpdateClusterSoftwareCommand: (input: UpdateClusterSoftwareCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateCodeRepositoryCommand
 */
export declare const se_UpdateCodeRepositoryCommand: (input: UpdateCodeRepositoryCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateComputeQuotaCommand
 */
export declare const se_UpdateComputeQuotaCommand: (input: UpdateComputeQuotaCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateContextCommand
 */
export declare const se_UpdateContextCommand: (input: UpdateContextCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateDeviceFleetCommand
 */
export declare const se_UpdateDeviceFleetCommand: (input: UpdateDeviceFleetCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateDevicesCommand
 */
export declare const se_UpdateDevicesCommand: (input: UpdateDevicesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateDomainCommand
 */
export declare const se_UpdateDomainCommand: (input: UpdateDomainCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateEndpointCommand
 */
export declare const se_UpdateEndpointCommand: (input: UpdateEndpointCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateEndpointWeightsAndCapacitiesCommand
 */
export declare const se_UpdateEndpointWeightsAndCapacitiesCommand: (input: UpdateEndpointWeightsAndCapacitiesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateExperimentCommand
 */
export declare const se_UpdateExperimentCommand: (input: UpdateExperimentCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateFeatureGroupCommand
 */
export declare const se_UpdateFeatureGroupCommand: (input: UpdateFeatureGroupCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateFeatureMetadataCommand
 */
export declare const se_UpdateFeatureMetadataCommand: (input: UpdateFeatureMetadataCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateHubCommand
 */
export declare const se_UpdateHubCommand: (input: UpdateHubCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateHubContentCommand
 */
export declare const se_UpdateHubContentCommand: (input: UpdateHubContentCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateHubContentReferenceCommand
 */
export declare const se_UpdateHubContentReferenceCommand: (input: UpdateHubContentReferenceCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateImageCommand
 */
export declare const se_UpdateImageCommand: (input: UpdateImageCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateImageVersionCommand
 */
export declare const se_UpdateImageVersionCommand: (input: UpdateImageVersionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateInferenceComponentCommand
 */
export declare const se_UpdateInferenceComponentCommand: (input: UpdateInferenceComponentCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateInferenceComponentRuntimeConfigCommand
 */
export declare const se_UpdateInferenceComponentRuntimeConfigCommand: (input: UpdateInferenceComponentRuntimeConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateInferenceExperimentCommand
 */
export declare const se_UpdateInferenceExperimentCommand: (input: UpdateInferenceExperimentCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateMlflowTrackingServerCommand
 */
export declare const se_UpdateMlflowTrackingServerCommand: (input: UpdateMlflowTrackingServerCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateModelCardCommand
 */
export declare const se_UpdateModelCardCommand: (input: UpdateModelCardCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateModelPackageCommand
 */
export declare const se_UpdateModelPackageCommand: (input: UpdateModelPackageCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateMonitoringAlertCommand
 */
export declare const se_UpdateMonitoringAlertCommand: (input: UpdateMonitoringAlertCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateMonitoringScheduleCommand
 */
export declare const se_UpdateMonitoringScheduleCommand: (input: UpdateMonitoringScheduleCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateNotebookInstanceCommand
 */
export declare const se_UpdateNotebookInstanceCommand: (input: UpdateNotebookInstanceCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateNotebookInstanceLifecycleConfigCommand
 */
export declare const se_UpdateNotebookInstanceLifecycleConfigCommand: (input: UpdateNotebookInstanceLifecycleConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdatePartnerAppCommand
 */
export declare const se_UpdatePartnerAppCommand: (input: UpdatePartnerAppCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdatePipelineCommand
 */
export declare const se_UpdatePipelineCommand: (input: UpdatePipelineCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdatePipelineExecutionCommand
 */
export declare const se_UpdatePipelineExecutionCommand: (input: UpdatePipelineExecutionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateProjectCommand
 */
export declare const se_UpdateProjectCommand: (input: UpdateProjectCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateSpaceCommand
 */
export declare const se_UpdateSpaceCommand: (input: UpdateSpaceCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateTrainingJobCommand
 */
export declare const se_UpdateTrainingJobCommand: (input: UpdateTrainingJobCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateTrialCommand
 */
export declare const se_UpdateTrialCommand: (input: UpdateTrialCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateTrialComponentCommand
 */
export declare const se_UpdateTrialComponentCommand: (input: UpdateTrialComponentCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateUserProfileCommand
 */
export declare const se_UpdateUserProfileCommand: (input: UpdateUserProfileCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateWorkforceCommand
 */
export declare const se_UpdateWorkforceCommand: (input: UpdateWorkforceCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateWorkteamCommand
 */
export declare const se_UpdateWorkteamCommand: (input: UpdateWorkteamCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * deserializeAws_json1_1AddAssociationCommand
 */
export declare const de_AddAssociationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<AddAssociationCommandOutput>;
/**
 * deserializeAws_json1_1AddTagsCommand
 */
export declare const de_AddTagsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<AddTagsCommandOutput>;
/**
 * deserializeAws_json1_1AssociateTrialComponentCommand
 */
export declare const de_AssociateTrialComponentCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<AssociateTrialComponentCommandOutput>;
/**
 * deserializeAws_json1_1BatchDeleteClusterNodesCommand
 */
export declare const de_BatchDeleteClusterNodesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<BatchDeleteClusterNodesCommandOutput>;
/**
 * deserializeAws_json1_1BatchDescribeModelPackageCommand
 */
export declare const de_BatchDescribeModelPackageCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<BatchDescribeModelPackageCommandOutput>;
/**
 * deserializeAws_json1_1CreateActionCommand
 */
export declare const de_CreateActionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateActionCommandOutput>;
/**
 * deserializeAws_json1_1CreateAlgorithmCommand
 */
export declare const de_CreateAlgorithmCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateAlgorithmCommandOutput>;
/**
 * deserializeAws_json1_1CreateAppCommand
 */
export declare const de_CreateAppCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateAppCommandOutput>;
/**
 * deserializeAws_json1_1CreateAppImageConfigCommand
 */
export declare const de_CreateAppImageConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateAppImageConfigCommandOutput>;
/**
 * deserializeAws_json1_1CreateArtifactCommand
 */
export declare const de_CreateArtifactCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateArtifactCommandOutput>;
/**
 * deserializeAws_json1_1CreateAutoMLJobCommand
 */
export declare const de_CreateAutoMLJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateAutoMLJobCommandOutput>;
/**
 * deserializeAws_json1_1CreateAutoMLJobV2Command
 */
export declare const de_CreateAutoMLJobV2Command: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateAutoMLJobV2CommandOutput>;
/**
 * deserializeAws_json1_1CreateClusterCommand
 */
export declare const de_CreateClusterCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateClusterCommandOutput>;
/**
 * deserializeAws_json1_1CreateClusterSchedulerConfigCommand
 */
export declare const de_CreateClusterSchedulerConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateClusterSchedulerConfigCommandOutput>;
/**
 * deserializeAws_json1_1CreateCodeRepositoryCommand
 */
export declare const de_CreateCodeRepositoryCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateCodeRepositoryCommandOutput>;
/**
 * deserializeAws_json1_1CreateCompilationJobCommand
 */
export declare const de_CreateCompilationJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateCompilationJobCommandOutput>;
/**
 * deserializeAws_json1_1CreateComputeQuotaCommand
 */
export declare const de_CreateComputeQuotaCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateComputeQuotaCommandOutput>;
/**
 * deserializeAws_json1_1CreateContextCommand
 */
export declare const de_CreateContextCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateContextCommandOutput>;
/**
 * deserializeAws_json1_1CreateDataQualityJobDefinitionCommand
 */
export declare const de_CreateDataQualityJobDefinitionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateDataQualityJobDefinitionCommandOutput>;
/**
 * deserializeAws_json1_1CreateDeviceFleetCommand
 */
export declare const de_CreateDeviceFleetCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateDeviceFleetCommandOutput>;
/**
 * deserializeAws_json1_1CreateDomainCommand
 */
export declare const de_CreateDomainCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateDomainCommandOutput>;
/**
 * deserializeAws_json1_1CreateEdgeDeploymentPlanCommand
 */
export declare const de_CreateEdgeDeploymentPlanCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateEdgeDeploymentPlanCommandOutput>;
/**
 * deserializeAws_json1_1CreateEdgeDeploymentStageCommand
 */
export declare const de_CreateEdgeDeploymentStageCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateEdgeDeploymentStageCommandOutput>;
/**
 * deserializeAws_json1_1CreateEdgePackagingJobCommand
 */
export declare const de_CreateEdgePackagingJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateEdgePackagingJobCommandOutput>;
/**
 * deserializeAws_json1_1CreateEndpointCommand
 */
export declare const de_CreateEndpointCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateEndpointCommandOutput>;
/**
 * deserializeAws_json1_1CreateEndpointConfigCommand
 */
export declare const de_CreateEndpointConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateEndpointConfigCommandOutput>;
/**
 * deserializeAws_json1_1CreateExperimentCommand
 */
export declare const de_CreateExperimentCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateExperimentCommandOutput>;
/**
 * deserializeAws_json1_1CreateFeatureGroupCommand
 */
export declare const de_CreateFeatureGroupCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateFeatureGroupCommandOutput>;
/**
 * deserializeAws_json1_1CreateFlowDefinitionCommand
 */
export declare const de_CreateFlowDefinitionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateFlowDefinitionCommandOutput>;
/**
 * deserializeAws_json1_1CreateHubCommand
 */
export declare const de_CreateHubCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateHubCommandOutput>;
/**
 * deserializeAws_json1_1CreateHubContentReferenceCommand
 */
export declare const de_CreateHubContentReferenceCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateHubContentReferenceCommandOutput>;
/**
 * deserializeAws_json1_1CreateHumanTaskUiCommand
 */
export declare const de_CreateHumanTaskUiCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateHumanTaskUiCommandOutput>;
/**
 * deserializeAws_json1_1CreateHyperParameterTuningJobCommand
 */
export declare const de_CreateHyperParameterTuningJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateHyperParameterTuningJobCommandOutput>;
/**
 * deserializeAws_json1_1CreateImageCommand
 */
export declare const de_CreateImageCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateImageCommandOutput>;
/**
 * deserializeAws_json1_1CreateImageVersionCommand
 */
export declare const de_CreateImageVersionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateImageVersionCommandOutput>;
/**
 * deserializeAws_json1_1CreateInferenceComponentCommand
 */
export declare const de_CreateInferenceComponentCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateInferenceComponentCommandOutput>;
/**
 * deserializeAws_json1_1CreateInferenceExperimentCommand
 */
export declare const de_CreateInferenceExperimentCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateInferenceExperimentCommandOutput>;
/**
 * deserializeAws_json1_1CreateInferenceRecommendationsJobCommand
 */
export declare const de_CreateInferenceRecommendationsJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateInferenceRecommendationsJobCommandOutput>;
/**
 * deserializeAws_json1_1CreateLabelingJobCommand
 */
export declare const de_CreateLabelingJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateLabelingJobCommandOutput>;
/**
 * deserializeAws_json1_1CreateMlflowTrackingServerCommand
 */
export declare const de_CreateMlflowTrackingServerCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateMlflowTrackingServerCommandOutput>;
/**
 * deserializeAws_json1_1CreateModelCommand
 */
export declare const de_CreateModelCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateModelCommandOutput>;
/**
 * deserializeAws_json1_1CreateModelBiasJobDefinitionCommand
 */
export declare const de_CreateModelBiasJobDefinitionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateModelBiasJobDefinitionCommandOutput>;
/**
 * deserializeAws_json1_1CreateModelCardCommand
 */
export declare const de_CreateModelCardCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateModelCardCommandOutput>;
/**
 * deserializeAws_json1_1CreateModelCardExportJobCommand
 */
export declare const de_CreateModelCardExportJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateModelCardExportJobCommandOutput>;
/**
 * deserializeAws_json1_1CreateModelExplainabilityJobDefinitionCommand
 */
export declare const de_CreateModelExplainabilityJobDefinitionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateModelExplainabilityJobDefinitionCommandOutput>;
/**
 * deserializeAws_json1_1CreateModelPackageCommand
 */
export declare const de_CreateModelPackageCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateModelPackageCommandOutput>;
/**
 * deserializeAws_json1_1CreateModelPackageGroupCommand
 */
export declare const de_CreateModelPackageGroupCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateModelPackageGroupCommandOutput>;
/**
 * deserializeAws_json1_1CreateModelQualityJobDefinitionCommand
 */
export declare const de_CreateModelQualityJobDefinitionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateModelQualityJobDefinitionCommandOutput>;
/**
 * deserializeAws_json1_1CreateMonitoringScheduleCommand
 */
export declare const de_CreateMonitoringScheduleCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateMonitoringScheduleCommandOutput>;
/**
 * deserializeAws_json1_1CreateNotebookInstanceCommand
 */
export declare const de_CreateNotebookInstanceCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateNotebookInstanceCommandOutput>;
/**
 * deserializeAws_json1_1CreateNotebookInstanceLifecycleConfigCommand
 */
export declare const de_CreateNotebookInstanceLifecycleConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateNotebookInstanceLifecycleConfigCommandOutput>;
/**
 * deserializeAws_json1_1CreateOptimizationJobCommand
 */
export declare const de_CreateOptimizationJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateOptimizationJobCommandOutput>;
/**
 * deserializeAws_json1_1CreatePartnerAppCommand
 */
export declare const de_CreatePartnerAppCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreatePartnerAppCommandOutput>;
/**
 * deserializeAws_json1_1CreatePartnerAppPresignedUrlCommand
 */
export declare const de_CreatePartnerAppPresignedUrlCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreatePartnerAppPresignedUrlCommandOutput>;
/**
 * deserializeAws_json1_1CreatePipelineCommand
 */
export declare const de_CreatePipelineCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreatePipelineCommandOutput>;
/**
 * deserializeAws_json1_1CreatePresignedDomainUrlCommand
 */
export declare const de_CreatePresignedDomainUrlCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreatePresignedDomainUrlCommandOutput>;
/**
 * deserializeAws_json1_1CreatePresignedMlflowTrackingServerUrlCommand
 */
export declare const de_CreatePresignedMlflowTrackingServerUrlCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreatePresignedMlflowTrackingServerUrlCommandOutput>;
/**
 * deserializeAws_json1_1CreatePresignedNotebookInstanceUrlCommand
 */
export declare const de_CreatePresignedNotebookInstanceUrlCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreatePresignedNotebookInstanceUrlCommandOutput>;
/**
 * deserializeAws_json1_1CreateProcessingJobCommand
 */
export declare const de_CreateProcessingJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateProcessingJobCommandOutput>;
/**
 * deserializeAws_json1_1CreateProjectCommand
 */
export declare const de_CreateProjectCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateProjectCommandOutput>;
/**
 * deserializeAws_json1_1CreateSpaceCommand
 */
export declare const de_CreateSpaceCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateSpaceCommandOutput>;
/**
 * deserializeAws_json1_1CreateStudioLifecycleConfigCommand
 */
export declare const de_CreateStudioLifecycleConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateStudioLifecycleConfigCommandOutput>;
/**
 * deserializeAws_json1_1CreateTrainingJobCommand
 */
export declare const de_CreateTrainingJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateTrainingJobCommandOutput>;
/**
 * deserializeAws_json1_1CreateTrainingPlanCommand
 */
export declare const de_CreateTrainingPlanCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateTrainingPlanCommandOutput>;
/**
 * deserializeAws_json1_1CreateTransformJobCommand
 */
export declare const de_CreateTransformJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateTransformJobCommandOutput>;
/**
 * deserializeAws_json1_1CreateTrialCommand
 */
export declare const de_CreateTrialCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateTrialCommandOutput>;
/**
 * deserializeAws_json1_1CreateTrialComponentCommand
 */
export declare const de_CreateTrialComponentCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateTrialComponentCommandOutput>;
/**
 * deserializeAws_json1_1CreateUserProfileCommand
 */
export declare const de_CreateUserProfileCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateUserProfileCommandOutput>;
/**
 * deserializeAws_json1_1CreateWorkforceCommand
 */
export declare const de_CreateWorkforceCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateWorkforceCommandOutput>;
/**
 * deserializeAws_json1_1CreateWorkteamCommand
 */
export declare const de_CreateWorkteamCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateWorkteamCommandOutput>;
/**
 * deserializeAws_json1_1DeleteActionCommand
 */
export declare const de_DeleteActionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteActionCommandOutput>;
/**
 * deserializeAws_json1_1DeleteAlgorithmCommand
 */
export declare const de_DeleteAlgorithmCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteAlgorithmCommandOutput>;
/**
 * deserializeAws_json1_1DeleteAppCommand
 */
export declare const de_DeleteAppCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteAppCommandOutput>;
/**
 * deserializeAws_json1_1DeleteAppImageConfigCommand
 */
export declare const de_DeleteAppImageConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteAppImageConfigCommandOutput>;
/**
 * deserializeAws_json1_1DeleteArtifactCommand
 */
export declare const de_DeleteArtifactCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteArtifactCommandOutput>;
/**
 * deserializeAws_json1_1DeleteAssociationCommand
 */
export declare const de_DeleteAssociationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteAssociationCommandOutput>;
/**
 * deserializeAws_json1_1DeleteClusterCommand
 */
export declare const de_DeleteClusterCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteClusterCommandOutput>;
/**
 * deserializeAws_json1_1DeleteClusterSchedulerConfigCommand
 */
export declare const de_DeleteClusterSchedulerConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteClusterSchedulerConfigCommandOutput>;
/**
 * deserializeAws_json1_1DeleteCodeRepositoryCommand
 */
export declare const de_DeleteCodeRepositoryCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteCodeRepositoryCommandOutput>;
/**
 * deserializeAws_json1_1DeleteCompilationJobCommand
 */
export declare const de_DeleteCompilationJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteCompilationJobCommandOutput>;
/**
 * deserializeAws_json1_1DeleteComputeQuotaCommand
 */
export declare const de_DeleteComputeQuotaCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteComputeQuotaCommandOutput>;
/**
 * deserializeAws_json1_1DeleteContextCommand
 */
export declare const de_DeleteContextCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteContextCommandOutput>;
/**
 * deserializeAws_json1_1DeleteDataQualityJobDefinitionCommand
 */
export declare const de_DeleteDataQualityJobDefinitionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteDataQualityJobDefinitionCommandOutput>;
/**
 * deserializeAws_json1_1DeleteDeviceFleetCommand
 */
export declare const de_DeleteDeviceFleetCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteDeviceFleetCommandOutput>;
/**
 * deserializeAws_json1_1DeleteDomainCommand
 */
export declare const de_DeleteDomainCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteDomainCommandOutput>;
/**
 * deserializeAws_json1_1DeleteEdgeDeploymentPlanCommand
 */
export declare const de_DeleteEdgeDeploymentPlanCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteEdgeDeploymentPlanCommandOutput>;
/**
 * deserializeAws_json1_1DeleteEdgeDeploymentStageCommand
 */
export declare const de_DeleteEdgeDeploymentStageCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteEdgeDeploymentStageCommandOutput>;
/**
 * deserializeAws_json1_1DeleteEndpointCommand
 */
export declare const de_DeleteEndpointCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteEndpointCommandOutput>;
/**
 * deserializeAws_json1_1DeleteEndpointConfigCommand
 */
export declare const de_DeleteEndpointConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteEndpointConfigCommandOutput>;
/**
 * deserializeAws_json1_1DeleteExperimentCommand
 */
export declare const de_DeleteExperimentCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteExperimentCommandOutput>;
/**
 * deserializeAws_json1_1DeleteFeatureGroupCommand
 */
export declare const de_DeleteFeatureGroupCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteFeatureGroupCommandOutput>;
/**
 * deserializeAws_json1_1DeleteFlowDefinitionCommand
 */
export declare const de_DeleteFlowDefinitionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteFlowDefinitionCommandOutput>;
/**
 * deserializeAws_json1_1DeleteHubCommand
 */
export declare const de_DeleteHubCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteHubCommandOutput>;
/**
 * deserializeAws_json1_1DeleteHubContentCommand
 */
export declare const de_DeleteHubContentCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteHubContentCommandOutput>;
/**
 * deserializeAws_json1_1DeleteHubContentReferenceCommand
 */
export declare const de_DeleteHubContentReferenceCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteHubContentReferenceCommandOutput>;
/**
 * deserializeAws_json1_1DeleteHumanTaskUiCommand
 */
export declare const de_DeleteHumanTaskUiCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteHumanTaskUiCommandOutput>;
/**
 * deserializeAws_json1_1DeleteHyperParameterTuningJobCommand
 */
export declare const de_DeleteHyperParameterTuningJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteHyperParameterTuningJobCommandOutput>;
/**
 * deserializeAws_json1_1DeleteImageCommand
 */
export declare const de_DeleteImageCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteImageCommandOutput>;
/**
 * deserializeAws_json1_1DeleteImageVersionCommand
 */
export declare const de_DeleteImageVersionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteImageVersionCommandOutput>;
/**
 * deserializeAws_json1_1DeleteInferenceComponentCommand
 */
export declare const de_DeleteInferenceComponentCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteInferenceComponentCommandOutput>;
/**
 * deserializeAws_json1_1DeleteInferenceExperimentCommand
 */
export declare const de_DeleteInferenceExperimentCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteInferenceExperimentCommandOutput>;
/**
 * deserializeAws_json1_1DeleteMlflowTrackingServerCommand
 */
export declare const de_DeleteMlflowTrackingServerCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteMlflowTrackingServerCommandOutput>;
/**
 * deserializeAws_json1_1DeleteModelCommand
 */
export declare const de_DeleteModelCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteModelCommandOutput>;
/**
 * deserializeAws_json1_1DeleteModelBiasJobDefinitionCommand
 */
export declare const de_DeleteModelBiasJobDefinitionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteModelBiasJobDefinitionCommandOutput>;
/**
 * deserializeAws_json1_1DeleteModelCardCommand
 */
export declare const de_DeleteModelCardCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteModelCardCommandOutput>;
/**
 * deserializeAws_json1_1DeleteModelExplainabilityJobDefinitionCommand
 */
export declare const de_DeleteModelExplainabilityJobDefinitionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteModelExplainabilityJobDefinitionCommandOutput>;
/**
 * deserializeAws_json1_1DeleteModelPackageCommand
 */
export declare const de_DeleteModelPackageCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteModelPackageCommandOutput>;
/**
 * deserializeAws_json1_1DeleteModelPackageGroupCommand
 */
export declare const de_DeleteModelPackageGroupCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteModelPackageGroupCommandOutput>;
/**
 * deserializeAws_json1_1DeleteModelPackageGroupPolicyCommand
 */
export declare const de_DeleteModelPackageGroupPolicyCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteModelPackageGroupPolicyCommandOutput>;
/**
 * deserializeAws_json1_1DeleteModelQualityJobDefinitionCommand
 */
export declare const de_DeleteModelQualityJobDefinitionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteModelQualityJobDefinitionCommandOutput>;
/**
 * deserializeAws_json1_1DeleteMonitoringScheduleCommand
 */
export declare const de_DeleteMonitoringScheduleCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteMonitoringScheduleCommandOutput>;
/**
 * deserializeAws_json1_1DeleteNotebookInstanceCommand
 */
export declare const de_DeleteNotebookInstanceCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteNotebookInstanceCommandOutput>;
/**
 * deserializeAws_json1_1DeleteNotebookInstanceLifecycleConfigCommand
 */
export declare const de_DeleteNotebookInstanceLifecycleConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteNotebookInstanceLifecycleConfigCommandOutput>;
/**
 * deserializeAws_json1_1DeleteOptimizationJobCommand
 */
export declare const de_DeleteOptimizationJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteOptimizationJobCommandOutput>;
/**
 * deserializeAws_json1_1DeletePartnerAppCommand
 */
export declare const de_DeletePartnerAppCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeletePartnerAppCommandOutput>;
/**
 * deserializeAws_json1_1DeletePipelineCommand
 */
export declare const de_DeletePipelineCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeletePipelineCommandOutput>;
/**
 * deserializeAws_json1_1DeleteProjectCommand
 */
export declare const de_DeleteProjectCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteProjectCommandOutput>;
/**
 * deserializeAws_json1_1DeleteSpaceCommand
 */
export declare const de_DeleteSpaceCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteSpaceCommandOutput>;
/**
 * deserializeAws_json1_1DeleteStudioLifecycleConfigCommand
 */
export declare const de_DeleteStudioLifecycleConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteStudioLifecycleConfigCommandOutput>;
/**
 * deserializeAws_json1_1DeleteTagsCommand
 */
export declare const de_DeleteTagsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteTagsCommandOutput>;
/**
 * deserializeAws_json1_1DeleteTrialCommand
 */
export declare const de_DeleteTrialCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteTrialCommandOutput>;
/**
 * deserializeAws_json1_1DeleteTrialComponentCommand
 */
export declare const de_DeleteTrialComponentCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteTrialComponentCommandOutput>;
/**
 * deserializeAws_json1_1DeleteUserProfileCommand
 */
export declare const de_DeleteUserProfileCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteUserProfileCommandOutput>;
/**
 * deserializeAws_json1_1DeleteWorkforceCommand
 */
export declare const de_DeleteWorkforceCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteWorkforceCommandOutput>;
/**
 * deserializeAws_json1_1DeleteWorkteamCommand
 */
export declare const de_DeleteWorkteamCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteWorkteamCommandOutput>;
/**
 * deserializeAws_json1_1DeregisterDevicesCommand
 */
export declare const de_DeregisterDevicesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeregisterDevicesCommandOutput>;
/**
 * deserializeAws_json1_1DescribeActionCommand
 */
export declare const de_DescribeActionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeActionCommandOutput>;
/**
 * deserializeAws_json1_1DescribeAlgorithmCommand
 */
export declare const de_DescribeAlgorithmCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeAlgorithmCommandOutput>;
/**
 * deserializeAws_json1_1DescribeAppCommand
 */
export declare const de_DescribeAppCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeAppCommandOutput>;
/**
 * deserializeAws_json1_1DescribeAppImageConfigCommand
 */
export declare const de_DescribeAppImageConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeAppImageConfigCommandOutput>;
/**
 * deserializeAws_json1_1DescribeArtifactCommand
 */
export declare const de_DescribeArtifactCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeArtifactCommandOutput>;
/**
 * deserializeAws_json1_1DescribeAutoMLJobCommand
 */
export declare const de_DescribeAutoMLJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeAutoMLJobCommandOutput>;
/**
 * deserializeAws_json1_1DescribeAutoMLJobV2Command
 */
export declare const de_DescribeAutoMLJobV2Command: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeAutoMLJobV2CommandOutput>;
/**
 * deserializeAws_json1_1DescribeClusterCommand
 */
export declare const de_DescribeClusterCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeClusterCommandOutput>;
/**
 * deserializeAws_json1_1DescribeClusterNodeCommand
 */
export declare const de_DescribeClusterNodeCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeClusterNodeCommandOutput>;
/**
 * deserializeAws_json1_1DescribeClusterSchedulerConfigCommand
 */
export declare const de_DescribeClusterSchedulerConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeClusterSchedulerConfigCommandOutput>;
/**
 * deserializeAws_json1_1DescribeCodeRepositoryCommand
 */
export declare const de_DescribeCodeRepositoryCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeCodeRepositoryCommandOutput>;
/**
 * deserializeAws_json1_1DescribeCompilationJobCommand
 */
export declare const de_DescribeCompilationJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeCompilationJobCommandOutput>;
/**
 * deserializeAws_json1_1DescribeComputeQuotaCommand
 */
export declare const de_DescribeComputeQuotaCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeComputeQuotaCommandOutput>;
/**
 * deserializeAws_json1_1DescribeContextCommand
 */
export declare const de_DescribeContextCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeContextCommandOutput>;
/**
 * deserializeAws_json1_1DescribeDataQualityJobDefinitionCommand
 */
export declare const de_DescribeDataQualityJobDefinitionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeDataQualityJobDefinitionCommandOutput>;
/**
 * deserializeAws_json1_1DescribeDeviceCommand
 */
export declare const de_DescribeDeviceCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeDeviceCommandOutput>;
/**
 * deserializeAws_json1_1DescribeDeviceFleetCommand
 */
export declare const de_DescribeDeviceFleetCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeDeviceFleetCommandOutput>;
/**
 * deserializeAws_json1_1DescribeDomainCommand
 */
export declare const de_DescribeDomainCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeDomainCommandOutput>;
/**
 * deserializeAws_json1_1DescribeEdgeDeploymentPlanCommand
 */
export declare const de_DescribeEdgeDeploymentPlanCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeEdgeDeploymentPlanCommandOutput>;
/**
 * deserializeAws_json1_1DescribeEdgePackagingJobCommand
 */
export declare const de_DescribeEdgePackagingJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeEdgePackagingJobCommandOutput>;
/**
 * deserializeAws_json1_1DescribeEndpointCommand
 */
export declare const de_DescribeEndpointCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeEndpointCommandOutput>;
/**
 * deserializeAws_json1_1DescribeEndpointConfigCommand
 */
export declare const de_DescribeEndpointConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeEndpointConfigCommandOutput>;
/**
 * deserializeAws_json1_1DescribeExperimentCommand
 */
export declare const de_DescribeExperimentCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeExperimentCommandOutput>;
/**
 * deserializeAws_json1_1DescribeFeatureGroupCommand
 */
export declare const de_DescribeFeatureGroupCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeFeatureGroupCommandOutput>;
/**
 * deserializeAws_json1_1DescribeFeatureMetadataCommand
 */
export declare const de_DescribeFeatureMetadataCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeFeatureMetadataCommandOutput>;
/**
 * deserializeAws_json1_1DescribeFlowDefinitionCommand
 */
export declare const de_DescribeFlowDefinitionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeFlowDefinitionCommandOutput>;
/**
 * deserializeAws_json1_1DescribeHubCommand
 */
export declare const de_DescribeHubCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeHubCommandOutput>;
/**
 * deserializeAws_json1_1DescribeHubContentCommand
 */
export declare const de_DescribeHubContentCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeHubContentCommandOutput>;
/**
 * deserializeAws_json1_1DescribeHumanTaskUiCommand
 */
export declare const de_DescribeHumanTaskUiCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeHumanTaskUiCommandOutput>;
/**
 * deserializeAws_json1_1DescribeHyperParameterTuningJobCommand
 */
export declare const de_DescribeHyperParameterTuningJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeHyperParameterTuningJobCommandOutput>;
/**
 * deserializeAws_json1_1DescribeImageCommand
 */
export declare const de_DescribeImageCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeImageCommandOutput>;
/**
 * deserializeAws_json1_1DescribeImageVersionCommand
 */
export declare const de_DescribeImageVersionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeImageVersionCommandOutput>;
/**
 * deserializeAws_json1_1DescribeInferenceComponentCommand
 */
export declare const de_DescribeInferenceComponentCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeInferenceComponentCommandOutput>;
/**
 * deserializeAws_json1_1DescribeInferenceExperimentCommand
 */
export declare const de_DescribeInferenceExperimentCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeInferenceExperimentCommandOutput>;
/**
 * deserializeAws_json1_1DescribeInferenceRecommendationsJobCommand
 */
export declare const de_DescribeInferenceRecommendationsJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeInferenceRecommendationsJobCommandOutput>;
/**
 * deserializeAws_json1_1DescribeLabelingJobCommand
 */
export declare const de_DescribeLabelingJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeLabelingJobCommandOutput>;
/**
 * deserializeAws_json1_1DescribeLineageGroupCommand
 */
export declare const de_DescribeLineageGroupCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeLineageGroupCommandOutput>;
/**
 * deserializeAws_json1_1DescribeMlflowTrackingServerCommand
 */
export declare const de_DescribeMlflowTrackingServerCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeMlflowTrackingServerCommandOutput>;
/**
 * deserializeAws_json1_1DescribeModelCommand
 */
export declare const de_DescribeModelCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeModelCommandOutput>;
/**
 * deserializeAws_json1_1DescribeModelBiasJobDefinitionCommand
 */
export declare const de_DescribeModelBiasJobDefinitionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeModelBiasJobDefinitionCommandOutput>;
/**
 * deserializeAws_json1_1DescribeModelCardCommand
 */
export declare const de_DescribeModelCardCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeModelCardCommandOutput>;
/**
 * deserializeAws_json1_1DescribeModelCardExportJobCommand
 */
export declare const de_DescribeModelCardExportJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeModelCardExportJobCommandOutput>;
/**
 * deserializeAws_json1_1DescribeModelExplainabilityJobDefinitionCommand
 */
export declare const de_DescribeModelExplainabilityJobDefinitionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeModelExplainabilityJobDefinitionCommandOutput>;
/**
 * deserializeAws_json1_1DescribeModelPackageCommand
 */
export declare const de_DescribeModelPackageCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeModelPackageCommandOutput>;
/**
 * deserializeAws_json1_1DescribeModelPackageGroupCommand
 */
export declare const de_DescribeModelPackageGroupCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeModelPackageGroupCommandOutput>;
/**
 * deserializeAws_json1_1DescribeModelQualityJobDefinitionCommand
 */
export declare const de_DescribeModelQualityJobDefinitionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeModelQualityJobDefinitionCommandOutput>;
/**
 * deserializeAws_json1_1DescribeMonitoringScheduleCommand
 */
export declare const de_DescribeMonitoringScheduleCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeMonitoringScheduleCommandOutput>;
/**
 * deserializeAws_json1_1DescribeNotebookInstanceCommand
 */
export declare const de_DescribeNotebookInstanceCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeNotebookInstanceCommandOutput>;
/**
 * deserializeAws_json1_1DescribeNotebookInstanceLifecycleConfigCommand
 */
export declare const de_DescribeNotebookInstanceLifecycleConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeNotebookInstanceLifecycleConfigCommandOutput>;
/**
 * deserializeAws_json1_1DescribeOptimizationJobCommand
 */
export declare const de_DescribeOptimizationJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeOptimizationJobCommandOutput>;
/**
 * deserializeAws_json1_1DescribePartnerAppCommand
 */
export declare const de_DescribePartnerAppCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribePartnerAppCommandOutput>;
/**
 * deserializeAws_json1_1DescribePipelineCommand
 */
export declare const de_DescribePipelineCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribePipelineCommandOutput>;
/**
 * deserializeAws_json1_1DescribePipelineDefinitionForExecutionCommand
 */
export declare const de_DescribePipelineDefinitionForExecutionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribePipelineDefinitionForExecutionCommandOutput>;
/**
 * deserializeAws_json1_1DescribePipelineExecutionCommand
 */
export declare const de_DescribePipelineExecutionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribePipelineExecutionCommandOutput>;
/**
 * deserializeAws_json1_1DescribeProcessingJobCommand
 */
export declare const de_DescribeProcessingJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeProcessingJobCommandOutput>;
/**
 * deserializeAws_json1_1DescribeProjectCommand
 */
export declare const de_DescribeProjectCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeProjectCommandOutput>;
/**
 * deserializeAws_json1_1DescribeSpaceCommand
 */
export declare const de_DescribeSpaceCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeSpaceCommandOutput>;
/**
 * deserializeAws_json1_1DescribeStudioLifecycleConfigCommand
 */
export declare const de_DescribeStudioLifecycleConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeStudioLifecycleConfigCommandOutput>;
/**
 * deserializeAws_json1_1DescribeSubscribedWorkteamCommand
 */
export declare const de_DescribeSubscribedWorkteamCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeSubscribedWorkteamCommandOutput>;
/**
 * deserializeAws_json1_1DescribeTrainingJobCommand
 */
export declare const de_DescribeTrainingJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeTrainingJobCommandOutput>;
/**
 * deserializeAws_json1_1DescribeTrainingPlanCommand
 */
export declare const de_DescribeTrainingPlanCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeTrainingPlanCommandOutput>;
/**
 * deserializeAws_json1_1DescribeTransformJobCommand
 */
export declare const de_DescribeTransformJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeTransformJobCommandOutput>;
/**
 * deserializeAws_json1_1DescribeTrialCommand
 */
export declare const de_DescribeTrialCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeTrialCommandOutput>;
/**
 * deserializeAws_json1_1DescribeTrialComponentCommand
 */
export declare const de_DescribeTrialComponentCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeTrialComponentCommandOutput>;
/**
 * deserializeAws_json1_1DescribeUserProfileCommand
 */
export declare const de_DescribeUserProfileCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeUserProfileCommandOutput>;
/**
 * deserializeAws_json1_1DescribeWorkforceCommand
 */
export declare const de_DescribeWorkforceCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeWorkforceCommandOutput>;
/**
 * deserializeAws_json1_1DescribeWorkteamCommand
 */
export declare const de_DescribeWorkteamCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeWorkteamCommandOutput>;
/**
 * deserializeAws_json1_1DisableSagemakerServicecatalogPortfolioCommand
 */
export declare const de_DisableSagemakerServicecatalogPortfolioCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DisableSagemakerServicecatalogPortfolioCommandOutput>;
/**
 * deserializeAws_json1_1DisassociateTrialComponentCommand
 */
export declare const de_DisassociateTrialComponentCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DisassociateTrialComponentCommandOutput>;
/**
 * deserializeAws_json1_1EnableSagemakerServicecatalogPortfolioCommand
 */
export declare const de_EnableSagemakerServicecatalogPortfolioCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<EnableSagemakerServicecatalogPortfolioCommandOutput>;
/**
 * deserializeAws_json1_1GetDeviceFleetReportCommand
 */
export declare const de_GetDeviceFleetReportCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetDeviceFleetReportCommandOutput>;
/**
 * deserializeAws_json1_1GetLineageGroupPolicyCommand
 */
export declare const de_GetLineageGroupPolicyCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetLineageGroupPolicyCommandOutput>;
/**
 * deserializeAws_json1_1GetModelPackageGroupPolicyCommand
 */
export declare const de_GetModelPackageGroupPolicyCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetModelPackageGroupPolicyCommandOutput>;
/**
 * deserializeAws_json1_1GetSagemakerServicecatalogPortfolioStatusCommand
 */
export declare const de_GetSagemakerServicecatalogPortfolioStatusCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetSagemakerServicecatalogPortfolioStatusCommandOutput>;
/**
 * deserializeAws_json1_1GetScalingConfigurationRecommendationCommand
 */
export declare const de_GetScalingConfigurationRecommendationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetScalingConfigurationRecommendationCommandOutput>;
/**
 * deserializeAws_json1_1GetSearchSuggestionsCommand
 */
export declare const de_GetSearchSuggestionsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetSearchSuggestionsCommandOutput>;
/**
 * deserializeAws_json1_1ImportHubContentCommand
 */
export declare const de_ImportHubContentCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ImportHubContentCommandOutput>;
/**
 * deserializeAws_json1_1ListActionsCommand
 */
export declare const de_ListActionsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListActionsCommandOutput>;
/**
 * deserializeAws_json1_1ListAlgorithmsCommand
 */
export declare const de_ListAlgorithmsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListAlgorithmsCommandOutput>;
/**
 * deserializeAws_json1_1ListAliasesCommand
 */
export declare const de_ListAliasesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListAliasesCommandOutput>;
/**
 * deserializeAws_json1_1ListAppImageConfigsCommand
 */
export declare const de_ListAppImageConfigsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListAppImageConfigsCommandOutput>;
/**
 * deserializeAws_json1_1ListAppsCommand
 */
export declare const de_ListAppsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListAppsCommandOutput>;
/**
 * deserializeAws_json1_1ListArtifactsCommand
 */
export declare const de_ListArtifactsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListArtifactsCommandOutput>;
/**
 * deserializeAws_json1_1ListAssociationsCommand
 */
export declare const de_ListAssociationsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListAssociationsCommandOutput>;
/**
 * deserializeAws_json1_1ListAutoMLJobsCommand
 */
export declare const de_ListAutoMLJobsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListAutoMLJobsCommandOutput>;
/**
 * deserializeAws_json1_1ListCandidatesForAutoMLJobCommand
 */
export declare const de_ListCandidatesForAutoMLJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListCandidatesForAutoMLJobCommandOutput>;
/**
 * deserializeAws_json1_1ListClusterNodesCommand
 */
export declare const de_ListClusterNodesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListClusterNodesCommandOutput>;
/**
 * deserializeAws_json1_1ListClustersCommand
 */
export declare const de_ListClustersCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListClustersCommandOutput>;
/**
 * deserializeAws_json1_1ListClusterSchedulerConfigsCommand
 */
export declare const de_ListClusterSchedulerConfigsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListClusterSchedulerConfigsCommandOutput>;
/**
 * deserializeAws_json1_1ListCodeRepositoriesCommand
 */
export declare const de_ListCodeRepositoriesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListCodeRepositoriesCommandOutput>;
/**
 * deserializeAws_json1_1ListCompilationJobsCommand
 */
export declare const de_ListCompilationJobsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListCompilationJobsCommandOutput>;
/**
 * deserializeAws_json1_1ListComputeQuotasCommand
 */
export declare const de_ListComputeQuotasCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListComputeQuotasCommandOutput>;
/**
 * deserializeAws_json1_1ListContextsCommand
 */
export declare const de_ListContextsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListContextsCommandOutput>;
/**
 * deserializeAws_json1_1ListDataQualityJobDefinitionsCommand
 */
export declare const de_ListDataQualityJobDefinitionsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListDataQualityJobDefinitionsCommandOutput>;
/**
 * deserializeAws_json1_1ListDeviceFleetsCommand
 */
export declare const de_ListDeviceFleetsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListDeviceFleetsCommandOutput>;
/**
 * deserializeAws_json1_1ListDevicesCommand
 */
export declare const de_ListDevicesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListDevicesCommandOutput>;
/**
 * deserializeAws_json1_1ListDomainsCommand
 */
export declare const de_ListDomainsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListDomainsCommandOutput>;
/**
 * deserializeAws_json1_1ListEdgeDeploymentPlansCommand
 */
export declare const de_ListEdgeDeploymentPlansCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListEdgeDeploymentPlansCommandOutput>;
/**
 * deserializeAws_json1_1ListEdgePackagingJobsCommand
 */
export declare const de_ListEdgePackagingJobsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListEdgePackagingJobsCommandOutput>;
/**
 * deserializeAws_json1_1ListEndpointConfigsCommand
 */
export declare const de_ListEndpointConfigsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListEndpointConfigsCommandOutput>;
/**
 * deserializeAws_json1_1ListEndpointsCommand
 */
export declare const de_ListEndpointsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListEndpointsCommandOutput>;
/**
 * deserializeAws_json1_1ListExperimentsCommand
 */
export declare const de_ListExperimentsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListExperimentsCommandOutput>;
/**
 * deserializeAws_json1_1ListFeatureGroupsCommand
 */
export declare const de_ListFeatureGroupsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListFeatureGroupsCommandOutput>;
/**
 * deserializeAws_json1_1ListFlowDefinitionsCommand
 */
export declare const de_ListFlowDefinitionsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListFlowDefinitionsCommandOutput>;
/**
 * deserializeAws_json1_1ListHubContentsCommand
 */
export declare const de_ListHubContentsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListHubContentsCommandOutput>;
/**
 * deserializeAws_json1_1ListHubContentVersionsCommand
 */
export declare const de_ListHubContentVersionsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListHubContentVersionsCommandOutput>;
/**
 * deserializeAws_json1_1ListHubsCommand
 */
export declare const de_ListHubsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListHubsCommandOutput>;
/**
 * deserializeAws_json1_1ListHumanTaskUisCommand
 */
export declare const de_ListHumanTaskUisCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListHumanTaskUisCommandOutput>;
/**
 * deserializeAws_json1_1ListHyperParameterTuningJobsCommand
 */
export declare const de_ListHyperParameterTuningJobsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListHyperParameterTuningJobsCommandOutput>;
/**
 * deserializeAws_json1_1ListImagesCommand
 */
export declare const de_ListImagesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListImagesCommandOutput>;
/**
 * deserializeAws_json1_1ListImageVersionsCommand
 */
export declare const de_ListImageVersionsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListImageVersionsCommandOutput>;
/**
 * deserializeAws_json1_1ListInferenceComponentsCommand
 */
export declare const de_ListInferenceComponentsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListInferenceComponentsCommandOutput>;
/**
 * deserializeAws_json1_1ListInferenceExperimentsCommand
 */
export declare const de_ListInferenceExperimentsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListInferenceExperimentsCommandOutput>;
/**
 * deserializeAws_json1_1ListInferenceRecommendationsJobsCommand
 */
export declare const de_ListInferenceRecommendationsJobsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListInferenceRecommendationsJobsCommandOutput>;
/**
 * deserializeAws_json1_1ListInferenceRecommendationsJobStepsCommand
 */
export declare const de_ListInferenceRecommendationsJobStepsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListInferenceRecommendationsJobStepsCommandOutput>;
/**
 * deserializeAws_json1_1ListLabelingJobsCommand
 */
export declare const de_ListLabelingJobsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListLabelingJobsCommandOutput>;
/**
 * deserializeAws_json1_1ListLabelingJobsForWorkteamCommand
 */
export declare const de_ListLabelingJobsForWorkteamCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListLabelingJobsForWorkteamCommandOutput>;
/**
 * deserializeAws_json1_1ListLineageGroupsCommand
 */
export declare const de_ListLineageGroupsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListLineageGroupsCommandOutput>;
/**
 * deserializeAws_json1_1ListMlflowTrackingServersCommand
 */
export declare const de_ListMlflowTrackingServersCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListMlflowTrackingServersCommandOutput>;
/**
 * deserializeAws_json1_1ListModelBiasJobDefinitionsCommand
 */
export declare const de_ListModelBiasJobDefinitionsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListModelBiasJobDefinitionsCommandOutput>;
/**
 * deserializeAws_json1_1ListModelCardExportJobsCommand
 */
export declare const de_ListModelCardExportJobsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListModelCardExportJobsCommandOutput>;
/**
 * deserializeAws_json1_1ListModelCardsCommand
 */
export declare const de_ListModelCardsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListModelCardsCommandOutput>;
/**
 * deserializeAws_json1_1ListModelCardVersionsCommand
 */
export declare const de_ListModelCardVersionsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListModelCardVersionsCommandOutput>;
/**
 * deserializeAws_json1_1ListModelExplainabilityJobDefinitionsCommand
 */
export declare const de_ListModelExplainabilityJobDefinitionsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListModelExplainabilityJobDefinitionsCommandOutput>;
/**
 * deserializeAws_json1_1ListModelMetadataCommand
 */
export declare const de_ListModelMetadataCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListModelMetadataCommandOutput>;
/**
 * deserializeAws_json1_1ListModelPackageGroupsCommand
 */
export declare const de_ListModelPackageGroupsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListModelPackageGroupsCommandOutput>;
/**
 * deserializeAws_json1_1ListModelPackagesCommand
 */
export declare const de_ListModelPackagesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListModelPackagesCommandOutput>;
/**
 * deserializeAws_json1_1ListModelQualityJobDefinitionsCommand
 */
export declare const de_ListModelQualityJobDefinitionsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListModelQualityJobDefinitionsCommandOutput>;
/**
 * deserializeAws_json1_1ListModelsCommand
 */
export declare const de_ListModelsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListModelsCommandOutput>;
/**
 * deserializeAws_json1_1ListMonitoringAlertHistoryCommand
 */
export declare const de_ListMonitoringAlertHistoryCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListMonitoringAlertHistoryCommandOutput>;
/**
 * deserializeAws_json1_1ListMonitoringAlertsCommand
 */
export declare const de_ListMonitoringAlertsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListMonitoringAlertsCommandOutput>;
/**
 * deserializeAws_json1_1ListMonitoringExecutionsCommand
 */
export declare const de_ListMonitoringExecutionsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListMonitoringExecutionsCommandOutput>;
/**
 * deserializeAws_json1_1ListMonitoringSchedulesCommand
 */
export declare const de_ListMonitoringSchedulesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListMonitoringSchedulesCommandOutput>;
/**
 * deserializeAws_json1_1ListNotebookInstanceLifecycleConfigsCommand
 */
export declare const de_ListNotebookInstanceLifecycleConfigsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListNotebookInstanceLifecycleConfigsCommandOutput>;
/**
 * deserializeAws_json1_1ListNotebookInstancesCommand
 */
export declare const de_ListNotebookInstancesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListNotebookInstancesCommandOutput>;
/**
 * deserializeAws_json1_1ListOptimizationJobsCommand
 */
export declare const de_ListOptimizationJobsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListOptimizationJobsCommandOutput>;
/**
 * deserializeAws_json1_1ListPartnerAppsCommand
 */
export declare const de_ListPartnerAppsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListPartnerAppsCommandOutput>;
/**
 * deserializeAws_json1_1ListPipelineExecutionsCommand
 */
export declare const de_ListPipelineExecutionsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListPipelineExecutionsCommandOutput>;
/**
 * deserializeAws_json1_1ListPipelineExecutionStepsCommand
 */
export declare const de_ListPipelineExecutionStepsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListPipelineExecutionStepsCommandOutput>;
/**
 * deserializeAws_json1_1ListPipelineParametersForExecutionCommand
 */
export declare const de_ListPipelineParametersForExecutionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListPipelineParametersForExecutionCommandOutput>;
/**
 * deserializeAws_json1_1ListPipelinesCommand
 */
export declare const de_ListPipelinesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListPipelinesCommandOutput>;
/**
 * deserializeAws_json1_1ListProcessingJobsCommand
 */
export declare const de_ListProcessingJobsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListProcessingJobsCommandOutput>;
/**
 * deserializeAws_json1_1ListProjectsCommand
 */
export declare const de_ListProjectsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListProjectsCommandOutput>;
/**
 * deserializeAws_json1_1ListResourceCatalogsCommand
 */
export declare const de_ListResourceCatalogsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListResourceCatalogsCommandOutput>;
/**
 * deserializeAws_json1_1ListSpacesCommand
 */
export declare const de_ListSpacesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListSpacesCommandOutput>;
/**
 * deserializeAws_json1_1ListStageDevicesCommand
 */
export declare const de_ListStageDevicesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListStageDevicesCommandOutput>;
/**
 * deserializeAws_json1_1ListStudioLifecycleConfigsCommand
 */
export declare const de_ListStudioLifecycleConfigsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListStudioLifecycleConfigsCommandOutput>;
/**
 * deserializeAws_json1_1ListSubscribedWorkteamsCommand
 */
export declare const de_ListSubscribedWorkteamsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListSubscribedWorkteamsCommandOutput>;
/**
 * deserializeAws_json1_1ListTagsCommand
 */
export declare const de_ListTagsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListTagsCommandOutput>;
/**
 * deserializeAws_json1_1ListTrainingJobsCommand
 */
export declare const de_ListTrainingJobsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListTrainingJobsCommandOutput>;
/**
 * deserializeAws_json1_1ListTrainingJobsForHyperParameterTuningJobCommand
 */
export declare const de_ListTrainingJobsForHyperParameterTuningJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListTrainingJobsForHyperParameterTuningJobCommandOutput>;
/**
 * deserializeAws_json1_1ListTrainingPlansCommand
 */
export declare const de_ListTrainingPlansCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListTrainingPlansCommandOutput>;
/**
 * deserializeAws_json1_1ListTransformJobsCommand
 */
export declare const de_ListTransformJobsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListTransformJobsCommandOutput>;
/**
 * deserializeAws_json1_1ListTrialComponentsCommand
 */
export declare const de_ListTrialComponentsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListTrialComponentsCommandOutput>;
/**
 * deserializeAws_json1_1ListTrialsCommand
 */
export declare const de_ListTrialsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListTrialsCommandOutput>;
/**
 * deserializeAws_json1_1ListUserProfilesCommand
 */
export declare const de_ListUserProfilesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListUserProfilesCommandOutput>;
/**
 * deserializeAws_json1_1ListWorkforcesCommand
 */
export declare const de_ListWorkforcesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListWorkforcesCommandOutput>;
/**
 * deserializeAws_json1_1ListWorkteamsCommand
 */
export declare const de_ListWorkteamsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListWorkteamsCommandOutput>;
/**
 * deserializeAws_json1_1PutModelPackageGroupPolicyCommand
 */
export declare const de_PutModelPackageGroupPolicyCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutModelPackageGroupPolicyCommandOutput>;
/**
 * deserializeAws_json1_1QueryLineageCommand
 */
export declare const de_QueryLineageCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<QueryLineageCommandOutput>;
/**
 * deserializeAws_json1_1RegisterDevicesCommand
 */
export declare const de_RegisterDevicesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<RegisterDevicesCommandOutput>;
/**
 * deserializeAws_json1_1RenderUiTemplateCommand
 */
export declare const de_RenderUiTemplateCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<RenderUiTemplateCommandOutput>;
/**
 * deserializeAws_json1_1RetryPipelineExecutionCommand
 */
export declare const de_RetryPipelineExecutionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<RetryPipelineExecutionCommandOutput>;
/**
 * deserializeAws_json1_1SearchCommand
 */
export declare const de_SearchCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<SearchCommandOutput>;
/**
 * deserializeAws_json1_1SearchTrainingPlanOfferingsCommand
 */
export declare const de_SearchTrainingPlanOfferingsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<SearchTrainingPlanOfferingsCommandOutput>;
/**
 * deserializeAws_json1_1SendPipelineExecutionStepFailureCommand
 */
export declare const de_SendPipelineExecutionStepFailureCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<SendPipelineExecutionStepFailureCommandOutput>;
/**
 * deserializeAws_json1_1SendPipelineExecutionStepSuccessCommand
 */
export declare const de_SendPipelineExecutionStepSuccessCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<SendPipelineExecutionStepSuccessCommandOutput>;
/**
 * deserializeAws_json1_1StartEdgeDeploymentStageCommand
 */
export declare const de_StartEdgeDeploymentStageCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<StartEdgeDeploymentStageCommandOutput>;
/**
 * deserializeAws_json1_1StartInferenceExperimentCommand
 */
export declare const de_StartInferenceExperimentCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<StartInferenceExperimentCommandOutput>;
/**
 * deserializeAws_json1_1StartMlflowTrackingServerCommand
 */
export declare const de_StartMlflowTrackingServerCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<StartMlflowTrackingServerCommandOutput>;
/**
 * deserializeAws_json1_1StartMonitoringScheduleCommand
 */
export declare const de_StartMonitoringScheduleCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<StartMonitoringScheduleCommandOutput>;
/**
 * deserializeAws_json1_1StartNotebookInstanceCommand
 */
export declare const de_StartNotebookInstanceCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<StartNotebookInstanceCommandOutput>;
/**
 * deserializeAws_json1_1StartPipelineExecutionCommand
 */
export declare const de_StartPipelineExecutionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<StartPipelineExecutionCommandOutput>;
/**
 * deserializeAws_json1_1StopAutoMLJobCommand
 */
export declare const de_StopAutoMLJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<StopAutoMLJobCommandOutput>;
/**
 * deserializeAws_json1_1StopCompilationJobCommand
 */
export declare const de_StopCompilationJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<StopCompilationJobCommandOutput>;
/**
 * deserializeAws_json1_1StopEdgeDeploymentStageCommand
 */
export declare const de_StopEdgeDeploymentStageCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<StopEdgeDeploymentStageCommandOutput>;
/**
 * deserializeAws_json1_1StopEdgePackagingJobCommand
 */
export declare const de_StopEdgePackagingJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<StopEdgePackagingJobCommandOutput>;
/**
 * deserializeAws_json1_1StopHyperParameterTuningJobCommand
 */
export declare const de_StopHyperParameterTuningJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<StopHyperParameterTuningJobCommandOutput>;
/**
 * deserializeAws_json1_1StopInferenceExperimentCommand
 */
export declare const de_StopInferenceExperimentCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<StopInferenceExperimentCommandOutput>;
/**
 * deserializeAws_json1_1StopInferenceRecommendationsJobCommand
 */
export declare const de_StopInferenceRecommendationsJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<StopInferenceRecommendationsJobCommandOutput>;
/**
 * deserializeAws_json1_1StopLabelingJobCommand
 */
export declare const de_StopLabelingJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<StopLabelingJobCommandOutput>;
/**
 * deserializeAws_json1_1StopMlflowTrackingServerCommand
 */
export declare const de_StopMlflowTrackingServerCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<StopMlflowTrackingServerCommandOutput>;
/**
 * deserializeAws_json1_1StopMonitoringScheduleCommand
 */
export declare const de_StopMonitoringScheduleCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<StopMonitoringScheduleCommandOutput>;
/**
 * deserializeAws_json1_1StopNotebookInstanceCommand
 */
export declare const de_StopNotebookInstanceCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<StopNotebookInstanceCommandOutput>;
/**
 * deserializeAws_json1_1StopOptimizationJobCommand
 */
export declare const de_StopOptimizationJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<StopOptimizationJobCommandOutput>;
/**
 * deserializeAws_json1_1StopPipelineExecutionCommand
 */
export declare const de_StopPipelineExecutionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<StopPipelineExecutionCommandOutput>;
/**
 * deserializeAws_json1_1StopProcessingJobCommand
 */
export declare const de_StopProcessingJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<StopProcessingJobCommandOutput>;
/**
 * deserializeAws_json1_1StopTrainingJobCommand
 */
export declare const de_StopTrainingJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<StopTrainingJobCommandOutput>;
/**
 * deserializeAws_json1_1StopTransformJobCommand
 */
export declare const de_StopTransformJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<StopTransformJobCommandOutput>;
/**
 * deserializeAws_json1_1UpdateActionCommand
 */
export declare const de_UpdateActionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateActionCommandOutput>;
/**
 * deserializeAws_json1_1UpdateAppImageConfigCommand
 */
export declare const de_UpdateAppImageConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateAppImageConfigCommandOutput>;
/**
 * deserializeAws_json1_1UpdateArtifactCommand
 */
export declare const de_UpdateArtifactCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateArtifactCommandOutput>;
/**
 * deserializeAws_json1_1UpdateClusterCommand
 */
export declare const de_UpdateClusterCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateClusterCommandOutput>;
/**
 * deserializeAws_json1_1UpdateClusterSchedulerConfigCommand
 */
export declare const de_UpdateClusterSchedulerConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateClusterSchedulerConfigCommandOutput>;
/**
 * deserializeAws_json1_1UpdateClusterSoftwareCommand
 */
export declare const de_UpdateClusterSoftwareCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateClusterSoftwareCommandOutput>;
/**
 * deserializeAws_json1_1UpdateCodeRepositoryCommand
 */
export declare const de_UpdateCodeRepositoryCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateCodeRepositoryCommandOutput>;
/**
 * deserializeAws_json1_1UpdateComputeQuotaCommand
 */
export declare const de_UpdateComputeQuotaCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateComputeQuotaCommandOutput>;
/**
 * deserializeAws_json1_1UpdateContextCommand
 */
export declare const de_UpdateContextCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateContextCommandOutput>;
/**
 * deserializeAws_json1_1UpdateDeviceFleetCommand
 */
export declare const de_UpdateDeviceFleetCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateDeviceFleetCommandOutput>;
/**
 * deserializeAws_json1_1UpdateDevicesCommand
 */
export declare const de_UpdateDevicesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateDevicesCommandOutput>;
/**
 * deserializeAws_json1_1UpdateDomainCommand
 */
export declare const de_UpdateDomainCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateDomainCommandOutput>;
/**
 * deserializeAws_json1_1UpdateEndpointCommand
 */
export declare const de_UpdateEndpointCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateEndpointCommandOutput>;
/**
 * deserializeAws_json1_1UpdateEndpointWeightsAndCapacitiesCommand
 */
export declare const de_UpdateEndpointWeightsAndCapacitiesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateEndpointWeightsAndCapacitiesCommandOutput>;
/**
 * deserializeAws_json1_1UpdateExperimentCommand
 */
export declare const de_UpdateExperimentCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateExperimentCommandOutput>;
/**
 * deserializeAws_json1_1UpdateFeatureGroupCommand
 */
export declare const de_UpdateFeatureGroupCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateFeatureGroupCommandOutput>;
/**
 * deserializeAws_json1_1UpdateFeatureMetadataCommand
 */
export declare const de_UpdateFeatureMetadataCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateFeatureMetadataCommandOutput>;
/**
 * deserializeAws_json1_1UpdateHubCommand
 */
export declare const de_UpdateHubCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateHubCommandOutput>;
/**
 * deserializeAws_json1_1UpdateHubContentCommand
 */
export declare const de_UpdateHubContentCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateHubContentCommandOutput>;
/**
 * deserializeAws_json1_1UpdateHubContentReferenceCommand
 */
export declare const de_UpdateHubContentReferenceCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateHubContentReferenceCommandOutput>;
/**
 * deserializeAws_json1_1UpdateImageCommand
 */
export declare const de_UpdateImageCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateImageCommandOutput>;
/**
 * deserializeAws_json1_1UpdateImageVersionCommand
 */
export declare const de_UpdateImageVersionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateImageVersionCommandOutput>;
/**
 * deserializeAws_json1_1UpdateInferenceComponentCommand
 */
export declare const de_UpdateInferenceComponentCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateInferenceComponentCommandOutput>;
/**
 * deserializeAws_json1_1UpdateInferenceComponentRuntimeConfigCommand
 */
export declare const de_UpdateInferenceComponentRuntimeConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateInferenceComponentRuntimeConfigCommandOutput>;
/**
 * deserializeAws_json1_1UpdateInferenceExperimentCommand
 */
export declare const de_UpdateInferenceExperimentCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateInferenceExperimentCommandOutput>;
/**
 * deserializeAws_json1_1UpdateMlflowTrackingServerCommand
 */
export declare const de_UpdateMlflowTrackingServerCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateMlflowTrackingServerCommandOutput>;
/**
 * deserializeAws_json1_1UpdateModelCardCommand
 */
export declare const de_UpdateModelCardCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateModelCardCommandOutput>;
/**
 * deserializeAws_json1_1UpdateModelPackageCommand
 */
export declare const de_UpdateModelPackageCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateModelPackageCommandOutput>;
/**
 * deserializeAws_json1_1UpdateMonitoringAlertCommand
 */
export declare const de_UpdateMonitoringAlertCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateMonitoringAlertCommandOutput>;
/**
 * deserializeAws_json1_1UpdateMonitoringScheduleCommand
 */
export declare const de_UpdateMonitoringScheduleCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateMonitoringScheduleCommandOutput>;
/**
 * deserializeAws_json1_1UpdateNotebookInstanceCommand
 */
export declare const de_UpdateNotebookInstanceCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateNotebookInstanceCommandOutput>;
/**
 * deserializeAws_json1_1UpdateNotebookInstanceLifecycleConfigCommand
 */
export declare const de_UpdateNotebookInstanceLifecycleConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateNotebookInstanceLifecycleConfigCommandOutput>;
/**
 * deserializeAws_json1_1UpdatePartnerAppCommand
 */
export declare const de_UpdatePartnerAppCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdatePartnerAppCommandOutput>;
/**
 * deserializeAws_json1_1UpdatePipelineCommand
 */
export declare const de_UpdatePipelineCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdatePipelineCommandOutput>;
/**
 * deserializeAws_json1_1UpdatePipelineExecutionCommand
 */
export declare const de_UpdatePipelineExecutionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdatePipelineExecutionCommandOutput>;
/**
 * deserializeAws_json1_1UpdateProjectCommand
 */
export declare const de_UpdateProjectCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateProjectCommandOutput>;
/**
 * deserializeAws_json1_1UpdateSpaceCommand
 */
export declare const de_UpdateSpaceCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateSpaceCommandOutput>;
/**
 * deserializeAws_json1_1UpdateTrainingJobCommand
 */
export declare const de_UpdateTrainingJobCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateTrainingJobCommandOutput>;
/**
 * deserializeAws_json1_1UpdateTrialCommand
 */
export declare const de_UpdateTrialCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateTrialCommandOutput>;
/**
 * deserializeAws_json1_1UpdateTrialComponentCommand
 */
export declare const de_UpdateTrialComponentCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateTrialComponentCommandOutput>;
/**
 * deserializeAws_json1_1UpdateUserProfileCommand
 */
export declare const de_UpdateUserProfileCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateUserProfileCommandOutput>;
/**
 * deserializeAws_json1_1UpdateWorkforceCommand
 */
export declare const de_UpdateWorkforceCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateWorkforceCommandOutput>;
/**
 * deserializeAws_json1_1UpdateWorkteamCommand
 */
export declare const de_UpdateWorkteamCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateWorkteamCommandOutput>;
