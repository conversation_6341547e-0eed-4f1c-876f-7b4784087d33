import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateEndpointConfigInput,
  CreateEndpointConfigOutput,
} from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateEndpointConfigCommandInput
  extends CreateEndpointConfigInput {}
export interface CreateEndpointConfigCommandOutput
  extends CreateEndpointConfigOutput,
    __MetadataBearer {}
declare const CreateEndpointConfigCommand_base: {
  new (
    input: CreateEndpointConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateEndpointConfigCommandInput,
    CreateEndpointConfigCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateEndpointConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateEndpointConfigCommandInput,
    CreateEndpointConfigCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateEndpointConfigCommand extends CreateEndpointConfigCommand_base {
  protected static __types: {
    api: {
      input: CreateEndpointConfigInput;
      output: CreateEndpointConfigOutput;
    };
    sdk: {
      input: CreateEndpointConfigCommandInput;
      output: CreateEndpointConfigCommandOutput;
    };
  };
}
