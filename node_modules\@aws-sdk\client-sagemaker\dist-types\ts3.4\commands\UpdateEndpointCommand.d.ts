import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { UpdateEndpointInput, UpdateEndpointOutput } from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateEndpointCommandInput extends UpdateEndpointInput {}
export interface UpdateEndpointCommandOutput
  extends UpdateEndpointOutput,
    __MetadataBearer {}
declare const UpdateEndpointCommand_base: {
  new (
    input: UpdateEndpointCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateEndpointCommandInput,
    UpdateEndpointCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateEndpointCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateEndpointCommandInput,
    UpdateEndpointCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateEndpointCommand extends UpdateEndpointCommand_base {
  protected static __types: {
    api: {
      input: UpdateEndpointInput;
      output: UpdateEndpointOutput;
    };
    sdk: {
      input: UpdateEndpointCommandInput;
      output: UpdateEndpointCommandOutput;
    };
  };
}
