import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateHumanTaskUiRequest,
  CreateHumanTaskUiResponse,
} from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateHumanTaskUiCommandInput
  extends CreateHumanTaskUiRequest {}
export interface CreateHumanTaskUiCommandOutput
  extends CreateHumanTaskUiResponse,
    __MetadataBearer {}
declare const CreateHumanTaskUiCommand_base: {
  new (
    input: CreateHumanTaskUiCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateHumanTaskUiCommandInput,
    CreateHumanTaskUiCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateHumanTaskUiCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateHumanTaskUiCommandInput,
    CreateHumanTaskUiCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateHumanTaskUiCommand extends CreateHumanTaskUiCommand_base {
  protected static __types: {
    api: {
      input: CreateHumanTaskUiRequest;
      output: CreateHumanTaskUiResponse;
    };
    sdk: {
      input: CreateHumanTaskUiCommandInput;
      output: CreateHumanTaskUiCommandOutput;
    };
  };
}
