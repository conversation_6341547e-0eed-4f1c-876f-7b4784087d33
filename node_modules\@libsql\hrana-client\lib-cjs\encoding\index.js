"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.writeProtobufMessage = exports.readProtobufMessage = exports.writeJsonObject = exports.readJsonObject = void 0;
var decode_js_1 = require("./json/decode.js");
Object.defineProperty(exports, "readJsonObject", { enumerable: true, get: function () { return decode_js_1.readJsonObject; } });
var encode_js_1 = require("./json/encode.js");
Object.defineProperty(exports, "writeJsonObject", { enumerable: true, get: function () { return encode_js_1.writeJsonObject; } });
var decode_js_2 = require("./protobuf/decode.js");
Object.defineProperty(exports, "readProtobufMessage", { enumerable: true, get: function () { return decode_js_2.readProtobufMessage; } });
var encode_js_2 = require("./protobuf/encode.js");
Object.defineProperty(exports, "writeProtobufMessage", { enumerable: true, get: function () { return encode_js_2.writeProtobufMessage; } });
