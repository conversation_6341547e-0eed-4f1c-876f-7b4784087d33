import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  RenderUiTemplateRequest,
  RenderUiTemplateResponse,
} from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface RenderUiTemplateCommandInput extends RenderUiTemplateRequest {}
export interface RenderUiTemplateCommandOutput
  extends RenderUiTemplateResponse,
    __MetadataBearer {}
declare const RenderUiTemplateCommand_base: {
  new (
    input: RenderUiTemplateCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    RenderUiTemplateCommandInput,
    RenderUiTemplateCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: RenderUiTemplateCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    RenderUiTemplateCommandInput,
    RenderUiTemplateCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class RenderUiTemplateCommand extends RenderUiTemplateCommand_base {
  protected static __types: {
    api: {
      input: RenderUiTemplateRequest;
      output: RenderUiTemplateResponse;
    };
    sdk: {
      input: RenderUiTemplateCommandInput;
      output: RenderUiTemplateCommandOutput;
    };
  };
}
