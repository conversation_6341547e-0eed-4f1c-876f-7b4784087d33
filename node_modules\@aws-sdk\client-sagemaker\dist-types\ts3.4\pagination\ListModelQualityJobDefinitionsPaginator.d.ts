import { Paginator } from "@smithy/types";
import {
  ListModelQualityJobDefinitionsCommandInput,
  ListModelQualityJobDefinitionsCommandOutput,
} from "../commands/ListModelQualityJobDefinitionsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
export declare const paginateListModelQualityJobDefinitions: (
  config: SageMakerPaginationConfiguration,
  input: ListModelQualityJobDefinitionsCommandInput,
  ...rest: any[]
) => Paginator<ListModelQualityJobDefinitionsCommandOutput>;
