import { Paginator } from "@smithy/types";
import { ListClusterNodesCommandInput, ListClusterNodesCommandOutput } from "../commands/ListClusterNodesCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListClusterNodes: (config: SageMakerPaginationConfiguration, input: ListClusterNodesCommandInput, ...rest: any[]) => Paginator<ListClusterNodesCommandOutput>;
