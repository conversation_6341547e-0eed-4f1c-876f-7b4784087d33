import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { CreateDeviceFleetRequest } from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateDeviceFleetCommandInput
  extends CreateDeviceFleetRequest {}
export interface CreateDeviceFleetCommandOutput extends __MetadataBearer {}
declare const CreateDeviceFleetCommand_base: {
  new (
    input: CreateDeviceFleetCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateDeviceFleetCommandInput,
    CreateDeviceFleetCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateDeviceFleetCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateDeviceFleetCommandInput,
    CreateDeviceFleetCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateDeviceFleetCommand extends CreateDeviceFleetCommand_base {
  protected static __types: {
    api: {
      input: CreateDeviceFleetRequest;
      output: {};
    };
    sdk: {
      input: CreateDeviceFleetCommandInput;
      output: CreateDeviceFleetCommandOutput;
    };
  };
}
