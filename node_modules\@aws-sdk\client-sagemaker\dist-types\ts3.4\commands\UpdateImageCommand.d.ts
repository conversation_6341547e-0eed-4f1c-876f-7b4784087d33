import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { UpdateImageRequest, UpdateImageResponse } from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateImageCommandInput extends UpdateImageRequest {}
export interface UpdateImageCommandOutput
  extends UpdateImageResponse,
    __MetadataBearer {}
declare const UpdateImageCommand_base: {
  new (
    input: UpdateImageCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateImageCommandInput,
    UpdateImageCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateImageCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateImageCommandInput,
    UpdateImageCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateImageCommand extends UpdateImageCommand_base {
  protected static __types: {
    api: {
      input: UpdateImageRequest;
      output: UpdateImageResponse;
    };
    sdk: {
      input: UpdateImageCommandInput;
      output: UpdateImageCommandOutput;
    };
  };
}
