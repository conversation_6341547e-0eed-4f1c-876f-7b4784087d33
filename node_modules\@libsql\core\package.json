{"name": "@libsql/core", "version": "0.15.7", "keywords": ["libsql", "database", "sqlite", "serverless", "vercel", "netlify", "lambda"], "description": "libSQL driver for TypeScript and JavaScript", "repository": {"type": "git", "url": "git+https://github.com/libsql/libsql-client-ts", "directory": "packages/libsql-core"}, "authors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "license": "MIT", "type": "module", "exports": {"./api": {"types": "./lib-esm/api.d.ts", "import": "./lib-esm/api.js", "require": "./lib-cjs/api.js"}, "./config": {"types": "./lib-esm/config.d.ts", "import": "./lib-esm/config.js", "require": "./lib-cjs/config.js"}, "./uri": {"types": "./lib-esm/uri.d.ts", "import": "./lib-esm/uri.js", "require": "./lib-cjs/uri.js"}, "./util": {"types": "./lib-esm/util.d.ts", "import": "./lib-esm/util.js", "require": "./lib-cjs/util.js"}}, "typesVersions": {"*": {"api": ["./lib-esm/api.d.ts"], "config": ["./lib-esm/config.d.ts"], "uri": ["./lib-esm/uri.d.ts"], "util": ["./lib-esm/util.d.ts"]}}, "files": ["lib-cjs/**", "lib-esm/**"], "scripts": {"prepublishOnly": "npm run build", "prebuild": "rm -rf ./lib-cjs ./lib-esm", "build": "npm run build:cjs && npm run build:esm", "build:cjs": "tsc -p tsconfig.build-cjs.json", "build:esm": "tsc -p tsconfig.build-esm.json", "format:check": "prettier --check .", "postbuild": "cp package-cjs.json ./lib-cjs/package.json", "test": "jest --runInBand", "typecheck": "tsc --noEmit", "typedoc": "rm -rf ./docs && typedoc"}, "dependencies": {"js-base64": "^3.7.5"}, "devDependencies": {"@types/jest": "^29.2.5", "@types/node": "^18.15.5", "jest": "^29.3.1", "ts-jest": "^29.0.5", "typedoc": "^0.23.28", "typescript": "^4.9.4"}}