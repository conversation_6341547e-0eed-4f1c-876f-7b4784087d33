import { Platform } from 'react-native';

// Web için localhost, mobil için gerçek IP kullan
console.log('Platform.OS:', Platform.OS);
const MASTRA_API_URL = Platform.OS === 'web'
  ? 'http://localhost:3000/api'
  : 'http://***************:3000/api';
console.log('MASTRA_API_URL:', MASTRA_API_URL);

export interface Recipe {
  id: string;
  title: string;
  description: string;
  ingredients: string[];
  instructions: string[];
  cookingTime?: string;
  servings?: string;
  difficulty?: string;
  category?: string;
  image?: string;
}

export interface ChatMessage {
  id: string;
  text: string;
  isBot: boolean;
  timestamp: Date;
}

class RecipeService {
  // Chatbot mesaj gönderme
  async sendChatMessage(message: string): Promise<string> {
    try {
      console.log('Mastra API\'ye mesaj gönderiliyor:', message);
      console.log('API URL:', `${MASTRA_API_URL}/agents/0/generate`);

      const response = await fetch(`${MASTRA_API_URL}/agents/0/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messages: [
            {
              role: 'user',
              content: message
            }
          ]
        }),
      });

      console.log('Response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API Error:', errorText);
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('API Response:', data);
      return data.text || 'Üzgünüm, bir hata oluştu.';
    } catch (error) {
      console.error('Mastra API hatası:', error);
      throw new Error(`Bağlantı hatası: ${error.message}`);
    }
  }

  // Kategoriye göre tarif önerisi alma
  async getRecipesByCategory(category: string): Promise<string> {
    const message = `${category} kategorisinde tarif önerir misin?`;
    return this.sendChatMessage(message);
  }

  // Popüler tarifleri alma
  async getPopularRecipes(): Promise<string> {
    const message = 'Popüler ve kolay yapılabilir tarifleri önerir misin?';
    return this.sendChatMessage(message);
  }

  // Malzemeye göre tarif arama
  async searchRecipesByIngredients(ingredients: string[]): Promise<string> {
    const message = `Bu malzemelerle tarif önerir misin: ${ingredients.join(', ')}`;
    return this.sendChatMessage(message);
  }

  // Hızlı tarif önerileri
  async getQuickRecipes(): Promise<string> {
    const message = 'Hızlı ve kolay yapılabilir tarifleri önerir misin?';
    return this.sendChatMessage(message);
  }

  // Sağlıklı tarif önerileri
  async getHealthyRecipes(): Promise<string> {
    const message = 'Sağlıklı ve besleyici tarifleri önerir misin?';
    return this.sendChatMessage(message);
  }

  // Tatlı tarifleri
  async getDessertRecipes(): Promise<string> {
    const message = 'Tatlı tarifleri önerir misin?';
    return this.sendChatMessage(message);
  }

  // Ana yemek tarifleri
  async getMainDishRecipes(): Promise<string> {
    const message = 'Ana yemek tarifleri önerir misin?';
    return this.sendChatMessage(message);
  }

  // Çorba tarifleri
  async getSoupRecipes(): Promise<string> {
    const message = 'Çorba tarifleri önerir misin?';
    return this.sendChatMessage(message);
  }

  // Salata tarifleri
  async getSaladRecipes(): Promise<string> {
    const message = 'Salata tarifleri önerir misin?';
    return this.sendChatMessage(message);
  }
}

export const recipeService = new RecipeService();
