import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  CreateModelPackageGroupInput,
  CreateModelPackageGroupOutput,
} from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateModelPackageGroupCommandInput
  extends CreateModelPackageGroupInput {}
export interface CreateModelPackageGroupCommandOutput
  extends CreateModelPackageGroupOutput,
    __MetadataBearer {}
declare const CreateModelPackageGroupCommand_base: {
  new (
    input: CreateModelPackageGroupCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateModelPackageGroupCommandInput,
    CreateModelPackageGroupCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateModelPackageGroupCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateModelPackageGroupCommandInput,
    CreateModelPackageGroupCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateModelPackageGroupCommand extends CreateModelPackageGroupCommand_base {
  protected static __types: {
    api: {
      input: CreateModelPackageGroupInput;
      output: CreateModelPackageGroupOutput;
    };
    sdk: {
      input: CreateModelPackageGroupCommandInput;
      output: CreateModelPackageGroupCommandOutput;
    };
  };
}
