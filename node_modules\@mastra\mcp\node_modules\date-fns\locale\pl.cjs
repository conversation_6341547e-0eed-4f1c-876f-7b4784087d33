"use strict";
exports.pl = void 0;
var _index = require("./pl/_lib/formatDistance.cjs");
var _index2 = require("./pl/_lib/formatLong.cjs");
var _index3 = require("./pl/_lib/formatRelative.cjs");
var _index4 = require("./pl/_lib/localize.cjs");
var _index5 = require("./pl/_lib/match.cjs");

/**
 * @category Locales
 * @summary Polish locale.
 * @language Polish
 * @iso-639-2 pol
 * <AUTHOR> [@ertrzyiks](https://github.com/ertrzyiks)
 * <AUTHOR> RAG [@justrag](https://github.com/justrag)
 * <AUTHOR> [@mikolajgrzyb](https://github.com/mikolajgrzyb)
 * <AUTHOR> [@mutisz](https://github.com/mutisz)
 */
const pl = (exports.pl = {
  code: "pl",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
});
