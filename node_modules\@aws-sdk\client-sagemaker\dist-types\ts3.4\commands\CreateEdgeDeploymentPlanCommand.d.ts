import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateEdgeDeploymentPlanRequest,
  CreateEdgeDeploymentPlanResponse,
} from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateEdgeDeploymentPlanCommandInput
  extends CreateEdgeDeploymentPlanRequest {}
export interface CreateEdgeDeploymentPlanCommandOutput
  extends CreateEdgeDeploymentPlanResponse,
    __MetadataBearer {}
declare const CreateEdgeDeploymentPlanCommand_base: {
  new (
    input: CreateEdgeDeploymentPlanCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateEdgeDeploymentPlanCommandInput,
    CreateEdgeDeploymentPlanCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateEdgeDeploymentPlanCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateEdgeDeploymentPlanCommandInput,
    CreateEdgeDeploymentPlanCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateEdgeDeploymentPlanCommand extends CreateEdgeDeploymentPlanCommand_base {
  protected static __types: {
    api: {
      input: CreateEdgeDeploymentPlanRequest;
      output: CreateEdgeDeploymentPlanResponse;
    };
    sdk: {
      input: CreateEdgeDeploymentPlanCommandInput;
      output: CreateEdgeDeploymentPlanCommandOutput;
    };
  };
}
