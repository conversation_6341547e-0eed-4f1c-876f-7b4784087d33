import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ListLabelingJobsForWorkteamRequest,
  ListLabelingJobsForWorkteamResponse,
} from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface ListLabelingJobsForWorkteamCommandInput
  extends ListLabelingJobsForWorkteamRequest {}
export interface ListLabelingJobsForWorkteamCommandOutput
  extends ListLabelingJobsForWorkteamResponse,
    __MetadataBearer {}
declare const ListLabelingJobsForWorkteamCommand_base: {
  new (
    input: ListLabelingJobsForWorkteamCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListLabelingJobsForWorkteamCommandInput,
    ListLabelingJobsForWorkteamCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: ListLabelingJobsForWorkteamCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListLabelingJobsForWorkteamCommandInput,
    ListLabelingJobsForWorkteamCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListLabelingJobsForWorkteamCommand extends ListLabelingJobsForWorkteamCommand_base {
  protected static __types: {
    api: {
      input: ListLabelingJobsForWorkteamRequest;
      output: ListLabelingJobsForWorkteamResponse;
    };
    sdk: {
      input: ListLabelingJobsForWorkteamCommandInput;
      output: ListLabelingJobsForWorkteamCommandOutput;
    };
  };
}
