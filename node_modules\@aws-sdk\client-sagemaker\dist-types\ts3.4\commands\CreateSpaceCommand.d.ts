import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { CreateSpaceRequest, CreateSpaceResponse } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateSpaceCommandInput extends CreateSpaceRequest {}
export interface CreateSpaceCommandOutput
  extends CreateSpaceResponse,
    __MetadataBearer {}
declare const CreateSpaceCommand_base: {
  new (
    input: CreateSpaceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateSpaceCommandInput,
    CreateSpaceCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateSpaceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateSpaceCommandInput,
    CreateSpaceCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateSpaceCommand extends CreateSpaceCommand_base {
  protected static __types: {
    api: {
      input: CreateSpaceRequest;
      output: CreateSpaceResponse;
    };
    sdk: {
      input: CreateSpaceCommandInput;
      output: CreateSpaceCommandOutput;
    };
  };
}
