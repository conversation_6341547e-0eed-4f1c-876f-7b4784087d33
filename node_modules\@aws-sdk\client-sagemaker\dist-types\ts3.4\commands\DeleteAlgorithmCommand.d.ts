import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DeleteAlgorithmInput } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteAlgorithmCommandInput extends DeleteAlgorithmInput {}
export interface DeleteAlgorithmCommandOutput extends __MetadataBearer {}
declare const DeleteAlgorithmCommand_base: {
  new (
    input: DeleteAlgorithmCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteAlgorithmCommandInput,
    DeleteAlgorithmCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteAlgorithmCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteAlgorithmCommandInput,
    DeleteAlgorithmCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteAlgorithmCommand extends DeleteAlgorithmCommand_base {
  protected static __types: {
    api: {
      input: DeleteAlgorithmInput;
      output: {};
    };
    sdk: {
      input: DeleteAlgorithmCommandInput;
      output: DeleteAlgorithmCommandOutput;
    };
  };
}
