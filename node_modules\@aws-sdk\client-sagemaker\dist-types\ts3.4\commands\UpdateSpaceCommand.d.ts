import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { UpdateSpaceRequest, UpdateSpaceResponse } from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateSpaceCommandInput extends UpdateSpaceRequest {}
export interface UpdateSpaceCommandOutput
  extends UpdateSpaceResponse,
    __MetadataBearer {}
declare const UpdateSpaceCommand_base: {
  new (
    input: UpdateSpaceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateSpaceCommandInput,
    UpdateSpaceCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateSpaceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateSpaceCommandInput,
    UpdateSpaceCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateSpaceCommand extends UpdateSpaceCommand_base {
  protected static __types: {
    api: {
      input: UpdateSpaceRequest;
      output: UpdateSpaceResponse;
    };
    sdk: {
      input: UpdateSpaceCommandInput;
      output: UpdateSpaceCommandOutput;
    };
  };
}
