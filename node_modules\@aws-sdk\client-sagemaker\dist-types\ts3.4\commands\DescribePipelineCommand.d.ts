import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribePipelineRequest,
  DescribePipelineResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribePipelineCommandInput extends DescribePipelineRequest {}
export interface DescribePipelineCommandOutput
  extends DescribePipelineResponse,
    __MetadataBearer {}
declare const DescribePipelineCommand_base: {
  new (
    input: DescribePipelineCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribePipelineCommandInput,
    DescribePipelineCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribePipelineCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribePipelineCommandInput,
    DescribePipelineCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribePipelineCommand extends DescribePipelineCommand_base {
  protected static __types: {
    api: {
      input: DescribePipelineRequest;
      output: DescribePipelineResponse;
    };
    sdk: {
      input: DescribePipelineCommandInput;
      output: DescribePipelineCommandOutput;
    };
  };
}
