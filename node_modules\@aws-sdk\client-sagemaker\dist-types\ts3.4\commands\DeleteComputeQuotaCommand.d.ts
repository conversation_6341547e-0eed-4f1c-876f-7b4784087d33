import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DeleteComputeQuotaRequest } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteComputeQuotaCommandInput
  extends DeleteComputeQuotaRequest {}
export interface DeleteComputeQuotaCommandOutput extends __MetadataBearer {}
declare const DeleteComputeQuotaCommand_base: {
  new (
    input: DeleteComputeQuotaCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteComputeQuotaCommandInput,
    DeleteComputeQuotaCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteComputeQuotaCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteComputeQuotaCommandInput,
    DeleteComputeQuotaCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteComputeQuotaCommand extends DeleteComputeQuotaCommand_base {
  protected static __types: {
    api: {
      input: DeleteComputeQuotaRequest;
      output: {};
    };
    sdk: {
      input: DeleteComputeQuotaCommandInput;
      output: DeleteComputeQuotaCommandOutput;
    };
  };
}
