import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeTrainingJobRequest,
  DescribeTrainingJobResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeTrainingJobCommandInput
  extends DescribeTrainingJobRequest {}
export interface DescribeTrainingJobCommandOutput
  extends DescribeTrainingJobResponse,
    __MetadataBearer {}
declare const DescribeTrainingJobCommand_base: {
  new (
    input: DescribeTrainingJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeTrainingJobCommandInput,
    DescribeTrainingJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeTrainingJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeTrainingJobCommandInput,
    DescribeTrainingJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeTrainingJobCommand extends DescribeTrainingJobCommand_base {
  protected static __types: {
    api: {
      input: DescribeTrainingJobRequest;
      output: DescribeTrainingJobResponse;
    };
    sdk: {
      input: DescribeTrainingJobCommandInput;
      output: DescribeTrainingJobCommandOutput;
    };
  };
}
