import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DeleteCompilationJobRequest } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteCompilationJobCommandInput
  extends DeleteCompilationJobRequest {}
export interface DeleteCompilationJobCommandOutput extends __MetadataBearer {}
declare const DeleteCompilationJobCommand_base: {
  new (
    input: DeleteCompilationJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteCompilationJobCommandInput,
    DeleteCompilationJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteCompilationJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteCompilationJobCommandInput,
    DeleteCompilationJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteCompilationJobCommand extends DeleteCompilationJobCommand_base {
  protected static __types: {
    api: {
      input: DeleteCompilationJobRequest;
      output: {};
    };
    sdk: {
      input: DeleteCompilationJobCommandInput;
      output: DeleteCompilationJobCommandOutput;
    };
  };
}
