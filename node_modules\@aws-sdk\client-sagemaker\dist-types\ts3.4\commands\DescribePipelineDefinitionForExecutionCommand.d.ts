import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribePipelineDefinitionForExecutionRequest,
  DescribePipelineDefinitionForExecutionResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribePipelineDefinitionForExecutionCommandInput
  extends DescribePipelineDefinitionForExecutionRequest {}
export interface DescribePipelineDefinitionForExecutionCommandOutput
  extends DescribePipelineDefinitionForExecutionResponse,
    __MetadataBearer {}
declare const DescribePipelineDefinitionForExecutionCommand_base: {
  new (
    input: DescribePipelineDefinitionForExecutionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribePipelineDefinitionForExecutionCommandInput,
    DescribePipelineDefinitionForExecutionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribePipelineDefinitionForExecutionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribePipelineDefinitionForExecutionCommandInput,
    DescribePipelineDefinitionForExecutionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribePipelineDefinitionForExecutionCommand extends DescribePipelineDefinitionForExecutionCommand_base {
  protected static __types: {
    api: {
      input: DescribePipelineDefinitionForExecutionRequest;
      output: DescribePipelineDefinitionForExecutionResponse;
    };
    sdk: {
      input: DescribePipelineDefinitionForExecutionCommandInput;
      output: DescribePipelineDefinitionForExecutionCommandOutput;
    };
  };
}
