import { Ionicons } from '@expo/vector-icons';
import React, { useEffect, useState } from 'react';
import {
  Alert,
  Dimensions,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { recipeService } from '../services/RecipeService';

const { width } = Dimensions.get('window');

export default function HomeScreen({ navigation }: any) {
  const [popularRecipes, setPopularRecipes] = useState<string>('');
  const [quickRecipes, setQuickRecipes] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadHomeData();
  }, []);

  const loadHomeData = async () => {
    setIsLoading(true);
    try {
      const [popular, quick] = await Promise.all([
        recipeService.getPopularRecipes(),
        recipeService.getQuickRecipes(),
      ]);
      setPopularRecipes(popular);
      setQuickRecipes(quick);
    } catch (error) {
      console.error('Ana sayfa verileri yüklenirken hata:', error);
      Alert.alert('Hata', 'Veriler yüklenirken bir hata oluştu.');
    } finally {
      setIsLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadHomeData();
    setRefreshing(false);
  };

  const quickActions = [
    {
      title: 'Hızlı Tarifler',
      icon: 'flash-outline',
      color: '#FF6B35',
      onPress: () => navigation.navigate('Kategoriler', { category: 'Hızlı' }),
    },
    {
      title: 'Sağlıklı',
      icon: 'leaf-outline',
      color: '#4CAF50',
      onPress: () => navigation.navigate('Kategoriler', { category: 'Sağlıklı' }),
    },
    {
      title: 'Tatlılar',
      icon: 'ice-cream-outline',
      color: '#E91E63',
      onPress: () => navigation.navigate('Kategoriler', { category: 'Tatlı' }),
    },
    {
      title: 'Chatbot',
      icon: 'chatbubbles-outline',
      color: '#2196F3',
      onPress: () => navigation.navigate('Chatbot'),
    },
  ];

  const renderQuickAction = (action: any, index: number) => (
    <TouchableOpacity
      key={index}
      style={[styles.quickActionCard, { backgroundColor: action.color }]}
      onPress={action.onPress}
    >
      <Ionicons name={action.icon} size={30} color="white" />
      <Text style={styles.quickActionText}>{action.title}</Text>
    </TouchableOpacity>
  );

  const renderRecipeSection = (title: string, content: string, icon: keyof typeof Ionicons.glyphMap) => (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Ionicons name={icon} size={24} color="#FF6B35" />
        <Text style={styles.sectionTitle}>{title}</Text>
      </View>
      <View style={styles.recipeCard}>
        {isLoading ? (
          <Text style={styles.loadingText}>Yükleniyor... ⌨️</Text>
        ) : (
          <Text style={styles.recipeText}>{content}</Text>
        )}
      </View>
    </View>
  );

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* Welcome Section */}
      <View style={styles.welcomeSection}>
        <View style={styles.welcomeHeader}>
          <Ionicons name="hand-left-outline" size={24} color="#FF6B35" />
          <Text style={styles.welcomeTitle}>Hoş Geldiniz!</Text>
        </View>
        <Text style={styles.welcomeSubtitle}>
          Bugün ne pişirmek istiyorsunuz?
        </Text>
      </View>

      {/* Quick Actions */}
      <View style={styles.quickActionsContainer}>
        <Text style={styles.sectionTitle}>Hızlı Erişim</Text>
        <View style={styles.quickActionsGrid}>
          {quickActions.map(renderQuickAction)}
        </View>
      </View>

      {/* Popular Recipes */}
      {renderRecipeSection(
        'Popüler Tarifler',
        popularRecipes,
        'trending-up-outline'
      )}

      {/* Quick Recipes */}
      {renderRecipeSection(
        'Hızlı Tarifler',
        quickRecipes,
        'flash-outline'
      )}

      {/* Tips Section */}
      <View style={styles.tipsSection}>
        <View style={styles.sectionHeader}>
          <Ionicons name="bulb-outline" size={24} color="#FF6B35" />
          <Text style={styles.sectionTitle}>İpucu</Text>
        </View>
        <View style={styles.tipCard}>
          <Text style={styles.tipText}>
            Chatbot'a "Buzdolabımda domates, yumurta ve peynir var"
            diyerek elinizdeki malzemelerle yapabileceğiniz tarifleri öğrenebilirsiniz!
          </Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  welcomeSection: {
    backgroundColor: 'white',
    padding: 20,
    marginBottom: 10,
  },
  welcomeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
  },
  welcomeTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 10,
  },
  welcomeSubtitle: {
    fontSize: 16,
    color: '#666',
  },
  quickActionsContainer: {
    backgroundColor: 'white',
    padding: 20,
    marginBottom: 10,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 15,
  },
  quickActionCard: {
    width: (width - 60) / 2,
    height: 80,
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
  },
  quickActionText: {
    color: 'white',
    fontWeight: 'bold',
    marginTop: 5,
    fontSize: 14,
  },
  sectionContainer: {
    backgroundColor: 'white',
    padding: 20,
    marginBottom: 10,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 10,
  },
  recipeCard: {
    backgroundColor: '#f9f9f9',
    padding: 15,
    borderRadius: 10,
    borderLeftWidth: 4,
    borderLeftColor: '#FF6B35',
  },
  recipeText: {
    fontSize: 14,
    lineHeight: 20,
    color: '#333',
  },
  loadingText: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
    textAlign: 'center',
  },
  tipsSection: {
    backgroundColor: 'white',
    padding: 20,
    marginBottom: 20,
  },
  tipCard: {
    backgroundColor: '#FFF3E0',
    padding: 15,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#FFE0B2',
  },
  tipText: {
    fontSize: 14,
    lineHeight: 20,
    color: '#E65100',
  },
});
