import { Command as $Command } from "@smithy/smithy-client";
import { <PERSON>ada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { CreateProjectInput, CreateProjectOutput } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateProjectCommandInput extends CreateProjectInput {}
export interface CreateProjectCommandOutput
  extends CreateProjectOutput,
    __MetadataBearer {}
declare const CreateProjectCommand_base: {
  new (
    input: CreateProjectCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateProjectCommandInput,
    CreateProjectCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateProjectCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateProjectCommandInput,
    CreateProjectCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateProjectCommand extends CreateProjectCommand_base {
  protected static __types: {
    api: {
      input: CreateProjectInput;
      output: CreateProjectOutput;
    };
    sdk: {
      input: CreateProjectCommandInput;
      output: CreateProjectCommandOutput;
    };
  };
}
