import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  DeleteExperimentRequest,
  DeleteExperimentResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteExperimentCommandInput extends DeleteExperimentRequest {}
export interface DeleteExperimentCommandOutput
  extends DeleteExperimentResponse,
    __MetadataBearer {}
declare const DeleteExperimentCommand_base: {
  new (
    input: DeleteExperimentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteExperimentCommandInput,
    DeleteExperimentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteExperimentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteExperimentCommandInput,
    DeleteExperimentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteExperimentCommand extends DeleteExperimentCommand_base {
  protected static __types: {
    api: {
      input: DeleteExperimentRequest;
      output: DeleteExperimentResponse;
    };
    sdk: {
      input: DeleteExperimentCommandInput;
      output: DeleteExperimentCommandOutput;
    };
  };
}
