'use strict';

var chunkUVRGQEMD_cjs = require('../chunk-UVRGQEMD.cjs');



Object.defineProperty(exports, "InstrumentClass", {
  enumerable: true,
  get: function () { return chunkUVRGQEMD_cjs.InstrumentClass; }
});
Object.defineProperty(exports, "OTLPStorageExporter", {
  enumerable: true,
  get: function () { return chunkUVRGQEMD_cjs.OTLPTraceExporter; }
});
Object.defineProperty(exports, "Telemetry", {
  enumerable: true,
  get: function () { return chunkUVRGQEMD_cjs.Telemetry; }
});
Object.defineProperty(exports, "getBaggageValues", {
  enumerable: true,
  get: function () { return chunkUVRGQEMD_cjs.getBaggageValues; }
});
Object.defineProperty(exports, "hasActiveTelemetry", {
  enumerable: true,
  get: function () { return chunkUVRGQEMD_cjs.hasActiveTelemetry; }
});
Object.defineProperty(exports, "withSpan", {
  enumerable: true,
  get: function () { return chunkUVRGQEMD_cjs.withSpan; }
});
