import { Paginator } from "@smithy/types";
import { ListModelCardVersionsCommandInput, ListModelCardVersionsCommandOutput } from "../commands/ListModelCardVersionsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListModelCardVersions: (config: SageMakerPaginationConfiguration, input: ListModelCardVersionsCommandInput, ...rest: any[]) => Paginator<ListModelCardVersionsCommandOutput>;
