import { WaiterConfiguration, WaiterResult } from "@smithy/util-waiter";
import { DescribeImageCommandInput } from "../commands/DescribeImageCommand";
import { SageMakerClient } from "../SageMakerClient";
export declare const waitForImageCreated: (
  params: WaiterConfiguration<SageMakerClient>,
  input: DescribeImageCommandInput
) => Promise<WaiterResult>;
export declare const waitUntilImageCreated: (
  params: WaiterConfiguration<SageMakerClient>,
  input: DescribeImageCommandInput
) => Promise<WaiterResult>;
