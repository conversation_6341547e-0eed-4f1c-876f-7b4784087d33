import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateModelQualityJobDefinitionRequest,
  CreateModelQualityJobDefinitionResponse,
} from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateModelQualityJobDefinitionCommandInput
  extends CreateModelQualityJobDefinitionRequest {}
export interface CreateModelQualityJobDefinitionCommandOutput
  extends CreateModelQualityJobDefinitionResponse,
    __MetadataBearer {}
declare const CreateModelQualityJobDefinitionCommand_base: {
  new (
    input: CreateModelQualityJobDefinitionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateModelQualityJobDefinitionCommandInput,
    CreateModelQualityJobDefinitionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateModelQualityJobDefinitionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateModelQualityJobDefinitionCommandInput,
    CreateModelQualityJobDefinitionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateModelQualityJobDefinitionCommand extends CreateModelQualityJobDefinitionCommand_base {
  protected static __types: {
    api: {
      input: CreateModelQualityJobDefinitionRequest;
      output: CreateModelQualityJobDefinitionResponse;
    };
    sdk: {
      input: CreateModelQualityJobDefinitionCommandInput;
      output: CreateModelQualityJobDefinitionCommandOutput;
    };
  };
}
