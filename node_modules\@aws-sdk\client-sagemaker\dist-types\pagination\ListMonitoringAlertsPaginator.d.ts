import { Paginator } from "@smithy/types";
import { ListMonitoringAlertsCommandInput, ListMonitoringAlertsCommandOutput } from "../commands/ListMonitoringAlertsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListMonitoringAlerts: (config: SageMakerPaginationConfiguration, input: ListMonitoringAlertsCommandInput, ...rest: any[]) => Paginator<ListMonitoringAlertsCommandOutput>;
