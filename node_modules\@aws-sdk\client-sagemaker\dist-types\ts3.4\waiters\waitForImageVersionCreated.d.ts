import { WaiterConfiguration, WaiterResult } from "@smithy/util-waiter";
import { DescribeImageVersionCommandInput } from "../commands/DescribeImageVersionCommand";
import { SageMakerClient } from "../SageMakerClient";
export declare const waitForImageVersionCreated: (
  params: WaiterConfiguration<SageMakerClient>,
  input: DescribeImageVersionCommandInput
) => Promise<WaiterResult>;
export declare const waitUntilImageVersionCreated: (
  params: WaiterConfiguration<SageMakerClient>,
  input: DescribeImageVersionCommandInput
) => Promise<WaiterResult>;
