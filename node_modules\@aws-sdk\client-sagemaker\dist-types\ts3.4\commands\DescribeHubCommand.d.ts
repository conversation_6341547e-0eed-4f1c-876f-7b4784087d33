import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DescribeHubRequest } from "../models/models_2";
import { DescribeHubResponse } from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeHubCommandInput extends DescribeHubRequest {}
export interface DescribeHubCommandOutput
  extends DescribeHubResponse,
    __MetadataBearer {}
declare const DescribeHubCommand_base: {
  new (
    input: DescribeHubCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeHubCommandInput,
    DescribeHubCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeHubCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeHubCommandInput,
    DescribeHubCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeHubCommand extends DescribeHubCommand_base {
  protected static __types: {
    api: {
      input: DescribeHubRequest;
      output: DescribeHubResponse;
    };
    sdk: {
      input: DescribeHubCommandInput;
      output: DescribeHubCommandOutput;
    };
  };
}
