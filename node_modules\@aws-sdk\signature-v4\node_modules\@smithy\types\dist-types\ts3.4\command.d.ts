import { <PERSON><PERSON>, MiddlewareStack } from "./middleware";
import { <PERSON><PERSON><PERSON><PERSON>earer } from "./response";
/**
 * @public
 */
export interface Command<ClientInput extends object, InputType extends ClientInput, ClientOutput extends Metada<PERSON>Bearer, OutputType extends ClientOutput, ResolvedConfiguration> {
    readonly input: InputType;
    readonly middlewareStack: MiddlewareStack<InputType, OutputType>;
    resolveMiddleware(stack: MiddlewareStack<ClientInput, ClientOutput>, configuration: ResolvedConfiguration, options: any): <PERSON><PERSON><InputType, OutputType>;
}
