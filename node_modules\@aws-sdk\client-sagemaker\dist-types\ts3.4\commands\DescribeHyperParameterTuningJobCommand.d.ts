import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeHyperParameterTuningJobRequest,
  DescribeHyperParameterTuningJobResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeHyperParameterTuningJobCommandInput
  extends DescribeHyperParameterTuningJobRequest {}
export interface DescribeHyperParameterTuningJobCommandOutput
  extends DescribeHyperParameterTuningJobResponse,
    __MetadataBearer {}
declare const DescribeHyperParameterTuningJobCommand_base: {
  new (
    input: DescribeHyperParameterTuningJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeHyperParameterTuningJobCommandInput,
    DescribeHyperParameterTuningJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeHyperParameterTuningJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeHyperParameterTuningJobCommandInput,
    DescribeHyperParameterTuningJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeHyperParameterTuningJobCommand extends DescribeHyperParameterTuningJobCommand_base {
  protected static __types: {
    api: {
      input: DescribeHyperParameterTuningJobRequest;
      output: DescribeHyperParameterTuningJobResponse;
    };
    sdk: {
      input: DescribeHyperParameterTuningJobCommandInput;
      output: DescribeHyperParameterTuningJobCommandOutput;
    };
  };
}
