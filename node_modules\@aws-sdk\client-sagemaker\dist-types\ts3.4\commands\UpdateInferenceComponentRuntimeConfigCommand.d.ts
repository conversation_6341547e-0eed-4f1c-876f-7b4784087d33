import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  UpdateInferenceComponentRuntimeConfigInput,
  UpdateInferenceComponentRuntimeConfigOutput,
} from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateInferenceComponentRuntimeConfigCommandInput
  extends UpdateInferenceComponentRuntimeConfigInput {}
export interface UpdateInferenceComponentRuntimeConfigCommandOutput
  extends UpdateInferenceComponentRuntimeConfigOutput,
    __MetadataBearer {}
declare const UpdateInferenceComponentRuntimeConfigCommand_base: {
  new (
    input: UpdateInferenceComponentRuntimeConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateInferenceComponentRuntimeConfigCommandInput,
    UpdateInferenceComponentRuntimeConfigCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateInferenceComponentRuntimeConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateInferenceComponentRuntimeConfigCommandInput,
    UpdateInferenceComponentRuntimeConfigCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateInferenceComponentRuntimeConfigCommand extends UpdateInferenceComponentRuntimeConfigCommand_base {
  protected static __types: {
    api: {
      input: UpdateInferenceComponentRuntimeConfigInput;
      output: UpdateInferenceComponentRuntimeConfigOutput;
    };
    sdk: {
      input: UpdateInferenceComponentRuntimeConfigCommandInput;
      output: UpdateInferenceComponentRuntimeConfigCommandOutput;
    };
  };
}
