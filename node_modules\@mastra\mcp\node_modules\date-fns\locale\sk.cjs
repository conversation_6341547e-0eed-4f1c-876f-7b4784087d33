"use strict";
exports.sk = void 0;
var _index = require("./sk/_lib/formatDistance.cjs");
var _index2 = require("./sk/_lib/formatLong.cjs");
var _index3 = require("./sk/_lib/formatRelative.cjs");
var _index4 = require("./sk/_lib/localize.cjs");
var _index5 = require("./sk/_lib/match.cjs");

/**
 * @category Locales
 * @summary Slovak locale.
 * @language Slovak
 * @iso-639-2 slk
 * <AUTHOR> [@mareksuscak](https://github.com/mareksuscak)
 */
const sk = (exports.sk = {
  code: "sk",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
});
