import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DeregisterDevicesRequest } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeregisterDevicesCommandInput
  extends DeregisterDevicesRequest {}
export interface DeregisterDevicesCommandOutput extends __MetadataBearer {}
declare const DeregisterDevicesCommand_base: {
  new (
    input: DeregisterDevicesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeregisterDevicesCommandInput,
    DeregisterDevicesCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeregisterDevicesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeregisterDevicesCommandInput,
    DeregisterDevicesCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeregisterDevicesCommand extends DeregisterDevicesCommand_base {
  protected static __types: {
    api: {
      input: DeregisterDevicesRequest;
      output: {};
    };
    sdk: {
      input: DeregisterDevicesCommandInput;
      output: DeregisterDevicesCommandOutput;
    };
  };
}
