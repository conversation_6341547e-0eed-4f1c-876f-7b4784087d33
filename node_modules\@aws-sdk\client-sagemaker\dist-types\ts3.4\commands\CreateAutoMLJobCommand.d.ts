import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateAutoMLJobRequest,
  CreateAutoMLJobResponse,
} from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateAutoMLJobCommandInput extends CreateAutoMLJobRequest {}
export interface CreateAutoMLJobCommandOutput
  extends CreateAutoMLJobResponse,
    __MetadataBearer {}
declare const CreateAutoMLJobCommand_base: {
  new (
    input: CreateAutoMLJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateAutoMLJobCommandInput,
    CreateAutoMLJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateAutoMLJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateAutoMLJobCommandInput,
    CreateAutoMLJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateAutoMLJobCommand extends CreateAutoMLJobCommand_base {
  protected static __types: {
    api: {
      input: CreateAutoMLJobRequest;
      output: CreateAutoMLJobResponse;
    };
    sdk: {
      input: CreateAutoMLJobCommandInput;
      output: CreateAutoMLJobCommandOutput;
    };
  };
}
