import { <PERSON><PERSON><PERSON><PERSON><PERSON>, IBundler } from '../bundler/index.js';
import '../base-tc5kgDTD.js';
import '@opentelemetry/api';
import '../logger-EhZkzZOr.js';
import 'stream';
import '@opentelemetry/sdk-trace-base';

interface IDeployer extends IBundler {
    deploy(outputDirectory: string): Promise<void>;
}
declare abstract class MastraDeployer extends MastraBundler implements IDeployer {
    constructor({ name }: {
        name: string;
    });
    abstract deploy(outputDirectory: string): Promise<void>;
}

export { type IDeployer, MastraDeployer };
