import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateDataQualityJobDefinitionRequest,
  CreateDataQualityJobDefinitionResponse,
} from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateDataQualityJobDefinitionCommandInput
  extends CreateDataQualityJobDefinitionRequest {}
export interface CreateDataQualityJobDefinitionCommandOutput
  extends CreateDataQualityJobDefinitionResponse,
    __MetadataBearer {}
declare const CreateDataQualityJobDefinitionCommand_base: {
  new (
    input: CreateDataQualityJobDefinitionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateDataQualityJobDefinitionCommandInput,
    CreateDataQualityJobDefinitionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateDataQualityJobDefinitionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateDataQualityJobDefinitionCommandInput,
    CreateDataQualityJobDefinitionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateDataQualityJobDefinitionCommand extends CreateDataQualityJobDefinitionCommand_base {
  protected static __types: {
    api: {
      input: CreateDataQualityJobDefinitionRequest;
      output: CreateDataQualityJobDefinitionResponse;
    };
    sdk: {
      input: CreateDataQualityJobDefinitionCommandInput;
      output: CreateDataQualityJobDefinitionCommandOutput;
    };
  };
}
