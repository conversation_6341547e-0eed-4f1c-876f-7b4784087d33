import { Paginator } from "@smithy/types";
import { ListAssociationsCommandInput, ListAssociationsCommandOutput } from "../commands/ListAssociationsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListAssociations: (config: SageMakerPaginationConfiguration, input: ListAssociationsCommandInput, ...rest: any[]) => Paginator<ListAssociationsCommandOutput>;
