import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { UpdateDeviceFleetRequest } from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateDeviceFleetCommandInput
  extends UpdateDeviceFleetRequest {}
export interface UpdateDeviceFleetCommandOutput extends __MetadataBearer {}
declare const UpdateDeviceFleetCommand_base: {
  new (
    input: UpdateDeviceFleetCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateDeviceFleetCommandInput,
    UpdateDeviceFleetCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateDeviceFleetCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateDeviceFleetCommandInput,
    UpdateDeviceFleetCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateDeviceFleetCommand extends UpdateDeviceFleetCommand_base {
  protected static __types: {
    api: {
      input: UpdateDeviceFleetRequest;
      output: {};
    };
    sdk: {
      input: UpdateDeviceFleetCommandInput;
      output: UpdateDeviceFleetCommandOutput;
    };
  };
}
