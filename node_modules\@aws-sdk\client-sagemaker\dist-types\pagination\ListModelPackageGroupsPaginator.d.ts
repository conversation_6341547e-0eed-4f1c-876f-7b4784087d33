import { Paginator } from "@smithy/types";
import { ListModelPackageGroupsCommandInput, ListModelPackageGroupsCommandOutput } from "../commands/ListModelPackageGroupsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListModelPackageGroups: (config: SageMakerPaginationConfiguration, input: ListModelPackageGroupsCommandInput, ...rest: any[]) => Paginator<ListModelPackageGroupsCommandOutput>;
