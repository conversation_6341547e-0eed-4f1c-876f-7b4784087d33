import { Paginator } from "@smithy/types";
import { ListMonitoringSchedulesCommandInput, ListMonitoringSchedulesCommandOutput } from "../commands/ListMonitoringSchedulesCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListMonitoringSchedules: (config: SageMakerPaginationConfiguration, input: ListMonitoringSchedulesCommandInput, ...rest: any[]) => Paginator<ListMonitoringSchedulesCommandOutput>;
