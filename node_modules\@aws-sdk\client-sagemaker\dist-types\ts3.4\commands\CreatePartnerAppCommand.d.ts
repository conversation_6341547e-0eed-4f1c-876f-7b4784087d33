import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreatePartnerAppRequest,
  CreatePartnerAppResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreatePartnerAppCommandInput extends CreatePartnerAppRequest {}
export interface CreatePartnerAppCommandOutput
  extends CreatePartnerAppResponse,
    __MetadataBearer {}
declare const CreatePartnerAppCommand_base: {
  new (
    input: CreatePartnerAppCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreatePartnerAppCommandInput,
    CreatePartnerAppCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreatePartnerAppCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreatePartnerAppCommandInput,
    CreatePartnerAppCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreatePartnerAppCommand extends CreatePartnerAppCommand_base {
  protected static __types: {
    api: {
      input: CreatePartnerAppRequest;
      output: CreatePartnerAppResponse;
    };
    sdk: {
      input: CreatePartnerAppCommandInput;
      output: CreatePartnerAppCommandOutput;
    };
  };
}
