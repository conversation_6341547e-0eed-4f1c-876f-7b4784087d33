import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateTransformJobRequest,
  CreateTransformJobResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateTransformJobCommandInput
  extends CreateTransformJobRequest {}
export interface CreateTransformJobCommandOutput
  extends CreateTransformJobResponse,
    __MetadataBearer {}
declare const CreateTransformJobCommand_base: {
  new (
    input: CreateTransformJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateTransformJobCommandInput,
    CreateTransformJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateTransformJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateTransformJobCommandInput,
    CreateTransformJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateTransformJobCommand extends CreateTransformJobCommand_base {
  protected static __types: {
    api: {
      input: CreateTransformJobRequest;
      output: CreateTransformJobResponse;
    };
    sdk: {
      input: CreateTransformJobCommandInput;
      output: CreateTransformJobCommandOutput;
    };
  };
}
