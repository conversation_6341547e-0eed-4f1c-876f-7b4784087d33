import { Paginator } from "@smithy/types";
import { ListCandidatesForAutoMLJobCommandInput, ListCandidatesForAutoMLJobCommandOutput } from "../commands/ListCandidatesForAutoMLJobCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListCandidatesForAutoMLJob: (config: SageMakerPaginationConfiguration, input: ListCandidatesForAutoMLJobCommandInput, ...rest: any[]) => Paginator<ListCandidatesForAutoMLJobCommandOutput>;
