import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeImageVersionRequest,
  DescribeImageVersionResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeImageVersionCommandInput
  extends DescribeImageVersionRequest {}
export interface DescribeImageVersionCommandOutput
  extends DescribeImageVersionResponse,
    __MetadataBearer {}
declare const DescribeImageVersionCommand_base: {
  new (
    input: DescribeImageVersionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeImageVersionCommandInput,
    DescribeImageVersionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeImageVersionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeImageVersionCommandInput,
    DescribeImageVersionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeImageVersionCommand extends DescribeImageVersionCommand_base {
  protected static __types: {
    api: {
      input: DescribeImageVersionRequest;
      output: DescribeImageVersionResponse;
    };
    sdk: {
      input: DescribeImageVersionCommandInput;
      output: DescribeImageVersionCommandOutput;
    };
  };
}
