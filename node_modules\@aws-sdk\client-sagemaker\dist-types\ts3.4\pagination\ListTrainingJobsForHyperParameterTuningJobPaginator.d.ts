import { Paginator } from "@smithy/types";
import {
  ListTrainingJobsForHyperParameterTuningJobCommandInput,
  ListTrainingJobsForHyperParameterTuningJobCommandOutput,
} from "../commands/ListTrainingJobsForHyperParameterTuningJobCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
export declare const paginateListTrainingJobsForHyperParameterTuningJob: (
  config: SageMakerPaginationConfiguration,
  input: ListTrainingJobsForHyperParameterTuningJobCommandInput,
  ...rest: any[]
) => Paginator<ListTrainingJobsForHyperParameterTuningJobCommandOutput>;
