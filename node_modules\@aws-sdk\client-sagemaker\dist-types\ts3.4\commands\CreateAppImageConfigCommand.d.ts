import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateAppImageConfigRequest,
  CreateAppImageConfigResponse,
} from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateAppImageConfigCommandInput
  extends CreateAppImageConfigRequest {}
export interface CreateAppImageConfigCommandOutput
  extends CreateAppImageConfigResponse,
    __MetadataBearer {}
declare const CreateAppImageConfigCommand_base: {
  new (
    input: CreateAppImageConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateAppImageConfigCommandInput,
    CreateAppImageConfigCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateAppImageConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateAppImageConfigCommandInput,
    CreateAppImageConfigCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateAppImageConfigCommand extends CreateAppImageConfigCommand_base {
  protected static __types: {
    api: {
      input: CreateAppImageConfigRequest;
      output: CreateAppImageConfigResponse;
    };
    sdk: {
      input: CreateAppImageConfigCommandInput;
      output: CreateAppImageConfigCommandOutput;
    };
  };
}
