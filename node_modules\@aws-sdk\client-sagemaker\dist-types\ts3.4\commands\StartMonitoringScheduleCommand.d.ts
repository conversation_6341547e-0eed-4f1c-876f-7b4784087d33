import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { StartMonitoringScheduleRequest } from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface StartMonitoringScheduleCommandInput
  extends StartMonitoringScheduleRequest {}
export interface StartMonitoringScheduleCommandOutput
  extends __MetadataBearer {}
declare const StartMonitoringScheduleCommand_base: {
  new (
    input: StartMonitoringScheduleCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StartMonitoringScheduleCommandInput,
    StartMonitoringScheduleCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: StartMonitoringScheduleCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StartMonitoringScheduleCommandInput,
    StartMonitoringScheduleCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class StartMonitoringScheduleCommand extends StartMonitoringScheduleCommand_base {
  protected static __types: {
    api: {
      input: StartMonitoringScheduleRequest;
      output: {};
    };
    sdk: {
      input: StartMonitoringScheduleCommandInput;
      output: StartMonitoringScheduleCommandOutput;
    };
  };
}
