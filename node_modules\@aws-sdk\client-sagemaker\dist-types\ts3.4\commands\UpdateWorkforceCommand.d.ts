import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  UpdateWorkforceRequest,
  UpdateWorkforceResponse,
} from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateWorkforceCommandInput extends UpdateWorkforceRequest {}
export interface UpdateWorkforceCommandOutput
  extends UpdateWorkforceResponse,
    __MetadataBearer {}
declare const UpdateWorkforceCommand_base: {
  new (
    input: UpdateWorkforceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateWorkforceCommandInput,
    UpdateWorkforceCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateWorkforceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateWorkforceCommandInput,
    UpdateWorkforceCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateWorkforceCommand extends UpdateWorkforceCommand_base {
  protected static __types: {
    api: {
      input: UpdateWorkforceRequest;
      output: UpdateWorkforceResponse;
    };
    sdk: {
      input: UpdateWorkforceCommandInput;
      output: UpdateWorkforceCommandOutput;
    };
  };
}
