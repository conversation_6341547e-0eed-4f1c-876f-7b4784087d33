import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { DeleteHubContentReferenceRequest } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteHubContentReferenceCommandInput
  extends DeleteHubContentReferenceRequest {}
export interface DeleteHubContentReferenceCommandOutput
  extends __MetadataBearer {}
declare const DeleteHubContentReferenceCommand_base: {
  new (
    input: DeleteHubContentReferenceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteHubContentReferenceCommandInput,
    DeleteHubContentReferenceCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteHubContentReferenceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteHubContentReferenceCommandInput,
    DeleteHubContentReferenceCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteHubContentReferenceCommand extends DeleteHubContentReferenceCommand_base {
  protected static __types: {
    api: {
      input: DeleteHubContentReferenceRequest;
      output: {};
    };
    sdk: {
      input: DeleteHubContentReferenceCommandInput;
      output: DeleteHubContentReferenceCommandOutput;
    };
  };
}
