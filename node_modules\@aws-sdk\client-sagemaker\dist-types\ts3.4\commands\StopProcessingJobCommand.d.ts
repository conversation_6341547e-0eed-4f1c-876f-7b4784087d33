import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { StopProcessingJobRequest } from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface StopProcessingJobCommandInput
  extends StopProcessingJobRequest {}
export interface StopProcessingJobCommandOutput extends __MetadataBearer {}
declare const StopProcessingJobCommand_base: {
  new (
    input: StopProcessingJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StopProcessingJobCommandInput,
    StopProcessingJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: StopProcessingJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StopProcessingJobCommandInput,
    StopProcessingJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class StopProcessingJobCommand extends StopProcessingJobCommand_base {
  protected static __types: {
    api: {
      input: StopProcessingJobRequest;
      output: {};
    };
    sdk: {
      input: StopProcessingJobCommandInput;
      output: StopProcessingJobCommandOutput;
    };
  };
}
