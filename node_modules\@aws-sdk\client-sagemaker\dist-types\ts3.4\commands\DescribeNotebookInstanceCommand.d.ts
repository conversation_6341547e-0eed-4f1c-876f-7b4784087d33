import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeNotebookInstanceInput,
  DescribeNotebookInstanceOutput,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeNotebookInstanceCommandInput
  extends DescribeNotebookInstanceInput {}
export interface DescribeNotebookInstanceCommandOutput
  extends DescribeNotebookInstanceOutput,
    __MetadataBearer {}
declare const DescribeNotebookInstanceCommand_base: {
  new (
    input: DescribeNotebookInstanceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeNotebookInstanceCommandInput,
    DescribeNotebookInstanceCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeNotebookInstanceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeNotebookInstanceCommandInput,
    DescribeNotebookInstanceCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeNotebookInstanceCommand extends DescribeNotebookInstanceCommand_base {
  protected static __types: {
    api: {
      input: DescribeNotebookInstanceInput;
      output: DescribeNotebookInstanceOutput;
    };
    sdk: {
      input: DescribeNotebookInstanceCommandInput;
      output: DescribeNotebookInstanceCommandOutput;
    };
  };
}
