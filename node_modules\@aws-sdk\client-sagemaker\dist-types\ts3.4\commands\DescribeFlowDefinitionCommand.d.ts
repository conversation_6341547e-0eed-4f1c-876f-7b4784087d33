import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeFlowDefinitionRequest,
  DescribeFlowDefinitionResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeFlowDefinitionCommandInput
  extends DescribeFlowDefinitionRequest {}
export interface DescribeFlowDefinitionCommandOutput
  extends DescribeFlowDefinitionResponse,
    __MetadataBearer {}
declare const DescribeFlowDefinitionCommand_base: {
  new (
    input: DescribeFlowDefinitionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeFlowDefinitionCommandInput,
    DescribeFlowDefinitionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeFlowDefinitionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeFlowDefinitionCommandInput,
    DescribeFlowDefinitionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeFlowDefinitionCommand extends DescribeFlowDefinitionCommand_base {
  protected static __types: {
    api: {
      input: DescribeFlowDefinitionRequest;
      output: DescribeFlowDefinitionResponse;
    };
    sdk: {
      input: DescribeFlowDefinitionCommandInput;
      output: DescribeFlowDefinitionCommandOutput;
    };
  };
}
