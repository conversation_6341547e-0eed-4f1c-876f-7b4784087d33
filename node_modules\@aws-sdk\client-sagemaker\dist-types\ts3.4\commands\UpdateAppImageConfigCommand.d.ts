import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  UpdateAppImageConfigRequest,
  UpdateAppImageConfigResponse,
} from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateAppImageConfigCommandInput
  extends UpdateAppImageConfigRequest {}
export interface UpdateAppImageConfigCommandOutput
  extends UpdateAppImageConfigResponse,
    __MetadataBearer {}
declare const UpdateAppImageConfigCommand_base: {
  new (
    input: UpdateAppImageConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateAppImageConfigCommandInput,
    UpdateAppImageConfigCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateAppImageConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateAppImageConfigCommandInput,
    UpdateAppImageConfigCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateAppImageConfigCommand extends UpdateAppImageConfigCommand_base {
  protected static __types: {
    api: {
      input: UpdateAppImageConfigRequest;
      output: UpdateAppImageConfigResponse;
    };
    sdk: {
      input: UpdateAppImageConfigCommandInput;
      output: UpdateAppImageConfigCommandOutput;
    };
  };
}
