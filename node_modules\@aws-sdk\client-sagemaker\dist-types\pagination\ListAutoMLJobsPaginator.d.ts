import { Paginator } from "@smithy/types";
import { ListAutoMLJobsCommandInput, ListAutoMLJobsCommandOutput } from "../commands/ListAutoMLJobsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListAutoMLJobs: (config: SageMakerPaginationConfiguration, input: ListAutoMLJobsCommandInput, ...rest: any[]) => Paginator<ListAutoMLJobsCommandOutput>;
