import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  BatchDescribeModelPackageInput,
  BatchDescribeModelPackageOutput,
} from "../models/models_0";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface BatchDescribeModelPackageCommandInput
  extends BatchDescribeModelPackageInput {}
export interface BatchDescribeModelPackageCommandOutput
  extends BatchDescribeModelPackageOutput,
    __MetadataBearer {}
declare const BatchDescribeModelPackageCommand_base: {
  new (
    input: BatchDescribeModelPackageCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    BatchDescribeModelPackageCommandInput,
    BatchDescribeModelPackageCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: BatchDescribeModelPackageCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    BatchDescribeModelPackageCommandInput,
    BatchDescribeModelPackageCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class BatchDescribeModelPackageCommand extends BatchDescribeModelPackageCommand_base {
  protected static __types: {
    api: {
      input: BatchDescribeModelPackageInput;
      output: BatchDescribeModelPackageOutput;
    };
    sdk: {
      input: BatchDescribeModelPackageCommandInput;
      output: BatchDescribeModelPackageCommandOutput;
    };
  };
}
