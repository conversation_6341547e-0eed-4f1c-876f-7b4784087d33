"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.prepareRequest = exports.moveHeadersToQuery = exports.getPayloadHash = exports.getCanonicalQuery = exports.getCanonicalHeaders = void 0;
const tslib_1 = require("tslib");
tslib_1.__exportStar(require("./SignatureV4"), exports);
var getCanonicalHeaders_1 = require("./getCanonicalHeaders");
Object.defineProperty(exports, "getCanonicalHeaders", { enumerable: true, get: function () { return getCanonicalHeaders_1.getCanonicalHeaders; } });
var getCanonicalQuery_1 = require("./getCanonicalQuery");
Object.defineProperty(exports, "getCanonicalQuery", { enumerable: true, get: function () { return getCanonicalQuery_1.getCanonicalQuery; } });
var getPayloadHash_1 = require("./getPayloadHash");
Object.defineProperty(exports, "getPayloadHash", { enumerable: true, get: function () { return getPayloadHash_1.getPayloadHash; } });
var moveHeadersToQuery_1 = require("./moveHeadersToQuery");
Object.defineProperty(exports, "moveHeadersToQuery", { enumerable: true, get: function () { return moveHeadersToQuery_1.moveHeadersToQuery; } });
var prepareRequest_1 = require("./prepareRequest");
Object.defineProperty(exports, "prepareRequest", { enumerable: true, get: function () { return prepareRequest_1.prepareRequest; } });
tslib_1.__exportStar(require("./credentialDerivation"), exports);
