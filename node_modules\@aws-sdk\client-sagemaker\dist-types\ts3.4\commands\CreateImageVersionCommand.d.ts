import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateImageVersionRequest,
  CreateImageVersionResponse,
} from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateImageVersionCommandInput
  extends CreateImageVersionRequest {}
export interface CreateImageVersionCommandOutput
  extends CreateImageVersionResponse,
    __MetadataBearer {}
declare const CreateImageVersionCommand_base: {
  new (
    input: CreateImageVersionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateImageVersionCommandInput,
    CreateImageVersionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateImageVersionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateImageVersionCommandInput,
    CreateImageVersionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateImageVersionCommand extends CreateImageVersionCommand_base {
  protected static __types: {
    api: {
      input: CreateImageVersionRequest;
      output: CreateImageVersionResponse;
    };
    sdk: {
      input: CreateImageVersionCommandInput;
      output: CreateImageVersionCommandOutput;
    };
  };
}
