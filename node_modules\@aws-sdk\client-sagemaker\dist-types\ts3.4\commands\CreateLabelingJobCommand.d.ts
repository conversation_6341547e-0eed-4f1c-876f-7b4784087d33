import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateLabelingJobRequest,
  CreateLabelingJobResponse,
} from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateLabelingJobCommandInput
  extends CreateLabelingJobRequest {}
export interface CreateLabelingJobCommandOutput
  extends CreateLabelingJobResponse,
    __MetadataBearer {}
declare const CreateLabelingJobCommand_base: {
  new (
    input: CreateLabelingJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateLabelingJobCommandInput,
    CreateLabelingJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateLabelingJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateLabelingJobCommandInput,
    CreateLabelingJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateLabelingJobCommand extends CreateLabelingJobCommand_base {
  protected static __types: {
    api: {
      input: CreateLabelingJobRequest;
      output: CreateLabelingJobResponse;
    };
    sdk: {
      input: CreateLabelingJobCommandInput;
      output: CreateLabelingJobCommandOutput;
    };
  };
}
