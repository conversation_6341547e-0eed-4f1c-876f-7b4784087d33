import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { UpdateDomainRequest, UpdateDomainResponse } from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateDomainCommandInput extends UpdateDomainRequest {}
export interface UpdateDomainCommandOutput
  extends UpdateDomainResponse,
    __MetadataBearer {}
declare const UpdateDomainCommand_base: {
  new (
    input: UpdateDomainCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateDomainCommandInput,
    UpdateDomainCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateDomainCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateDomainCommandInput,
    UpdateDomainCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateDomainCommand extends UpdateDomainCommand_base {
  protected static __types: {
    api: {
      input: UpdateDomainRequest;
      output: UpdateDomainResponse;
    };
    sdk: {
      input: UpdateDomainCommandInput;
      output: UpdateDomainCommandOutput;
    };
  };
}
