import { WaiterConfiguration, WaiterR<PERSON>ult } from "@smithy/util-waiter";
import { DescribeImageCommandInput } from "../commands/DescribeImageCommand";
import { SageMakerClient } from "../SageMakerClient";
/**
 *
 *  @deprecated Use waitUntilImageUpdated instead. waitForImageUpdated does not throw error in non-success cases.
 */
export declare const waitForImageUpdated: (params: WaiterConfiguration<SageMakerClient>, input: DescribeImageCommandInput) => Promise<WaiterResult>;
/**
 *
 *  @param params - Waiter configuration options.
 *  @param input - The input to DescribeImageCommand for polling.
 */
export declare const waitUntilImageUpdated: (params: WaiterConfiguration<SageMakerClient>, input: DescribeImageCommandInput) => Promise<WaiterResult>;
