'use strict';

var chunkST5RMVLG_cjs = require('../chunk-ST5RMVLG.cjs');



Object.defineProperty(exports, "AvailableHooks", {
  enumerable: true,
  get: function () { return chunkST5RMVLG_cjs.AvailableHooks; }
});
Object.defineProperty(exports, "executeHook", {
  enumerable: true,
  get: function () { return chunkST5RMVLG_cjs.executeHook; }
});
Object.defineProperty(exports, "registerHook", {
  enumerable: true,
  get: function () { return chunkST5RMVLG_cjs.registerHook; }
});
