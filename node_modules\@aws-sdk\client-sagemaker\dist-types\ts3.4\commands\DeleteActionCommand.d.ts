import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DeleteActionRequest, DeleteActionResponse } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteActionCommandInput extends DeleteActionRequest {}
export interface DeleteActionCommandOutput
  extends DeleteActionResponse,
    __MetadataBearer {}
declare const DeleteActionCommand_base: {
  new (
    input: DeleteActionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteActionCommandInput,
    DeleteActionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteActionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteActionCommandInput,
    DeleteActionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteActionCommand extends DeleteActionCommand_base {
  protected static __types: {
    api: {
      input: DeleteActionRequest;
      output: DeleteActionResponse;
    };
    sdk: {
      input: DeleteActionCommandInput;
      output: DeleteActionCommandOutput;
    };
  };
}
