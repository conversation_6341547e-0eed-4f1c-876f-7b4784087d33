import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { StopTransformJobRequest } from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface StopTransformJobCommandInput extends StopTransformJobRequest {}
export interface StopTransformJobCommandOutput extends __MetadataBearer {}
declare const StopTransformJobCommand_base: {
  new (
    input: StopTransformJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StopTransformJobCommandInput,
    StopTransformJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: StopTransformJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StopTransformJobCommandInput,
    StopTransformJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class StopTransformJobCommand extends StopTransformJobCommand_base {
  protected static __types: {
    api: {
      input: StopTransformJobRequest;
      output: {};
    };
    sdk: {
      input: StopTransformJobCommandInput;
      output: StopTransformJobCommandOutput;
    };
  };
}
