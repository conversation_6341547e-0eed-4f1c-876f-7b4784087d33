import { Paginator } from "@smithy/types";
import {
  ListFeatureGroupsCommandInput,
  ListFeatureGroupsCommandOutput,
} from "../commands/ListFeatureGroupsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
export declare const paginateListFeatureGroups: (
  config: SageMakerPaginationConfiguration,
  input: ListFeatureGroupsCommandInput,
  ...rest: any[]
) => Paginator<ListFeatureGroupsCommandOutput>;
