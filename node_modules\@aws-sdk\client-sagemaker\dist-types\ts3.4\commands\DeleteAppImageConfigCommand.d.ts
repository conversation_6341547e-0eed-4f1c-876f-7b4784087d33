import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { DeleteAppImageConfigRequest } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteAppImageConfigCommandInput
  extends DeleteAppImageConfigRequest {}
export interface DeleteAppImageConfigCommandOutput extends __MetadataBearer {}
declare const DeleteAppImageConfigCommand_base: {
  new (
    input: DeleteAppImageConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteAppImageConfigCommandInput,
    DeleteAppImageConfigCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteAppImageConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteAppImageConfigCommandInput,
    DeleteAppImageConfigCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteAppImageConfigCommand extends DeleteAppImageConfigCommand_base {
  protected static __types: {
    api: {
      input: DeleteAppImageConfigRequest;
      output: {};
    };
    sdk: {
      input: DeleteAppImageConfigCommandInput;
      output: DeleteAppImageConfigCommandOutput;
    };
  };
}
