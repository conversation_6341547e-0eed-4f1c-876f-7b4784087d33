import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DeleteEndpointInput } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteEndpointCommandInput extends DeleteEndpointInput {}
export interface DeleteEndpointCommandOutput extends __MetadataBearer {}
declare const DeleteEndpointCommand_base: {
  new (
    input: DeleteEndpointCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteEndpointCommandInput,
    DeleteEndpointCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteEndpointCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteEndpointCommandInput,
    DeleteEndpointCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteEndpointCommand extends DeleteEndpointCommand_base {
  protected static __types: {
    api: {
      input: DeleteEndpointInput;
      output: {};
    };
    sdk: {
      input: DeleteEndpointCommandInput;
      output: DeleteEndpointCommandOutput;
    };
  };
}
