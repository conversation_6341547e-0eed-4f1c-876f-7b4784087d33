import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ListWorkforcesRequest,
  ListWorkforcesResponse,
} from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface ListWorkforcesCommandInput extends ListWorkforcesRequest {}
export interface ListWorkforcesCommandOutput
  extends ListWorkforcesResponse,
    __MetadataBearer {}
declare const ListWorkforcesCommand_base: {
  new (
    input: ListWorkforcesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListWorkforcesCommandInput,
    ListWorkforcesCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    ...[input]: [] | [ListWorkforcesCommandInput]
  ): import("@smithy/smithy-client").CommandImpl<
    ListWorkforcesCommandInput,
    ListWorkforcesCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListWorkforcesCommand extends ListWorkforcesCommand_base {
  protected static __types: {
    api: {
      input: ListWorkforcesRequest;
      output: ListWorkforcesResponse;
    };
    sdk: {
      input: ListWorkforcesCommandInput;
      output: ListWorkforcesCommandOutput;
    };
  };
}
