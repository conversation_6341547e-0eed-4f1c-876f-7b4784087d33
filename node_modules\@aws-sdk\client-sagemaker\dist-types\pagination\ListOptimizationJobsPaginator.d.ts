import { Paginator } from "@smithy/types";
import { ListOptimizationJobsCommandInput, ListOptimizationJobsCommandOutput } from "../commands/ListOptimizationJobsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListOptimizationJobs: (config: SageMakerPaginationConfiguration, input: ListOptimizationJobsCommandInput, ...rest: any[]) => Paginator<ListOptimizationJobsCommandOutput>;
