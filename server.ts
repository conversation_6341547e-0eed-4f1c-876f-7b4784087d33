import cors from 'cors';
import 'dotenv/config';
import express from 'express';
import { recipeAgent } from './src/mastra/index.js';

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Ana sayfa
app.get('/', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html lang="tr">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Recipe Agent</title>
        <style>
            * {
                box-sizing: border-box;
            }
            html, body {
                height: 100%;
                margin: 0;
                padding: 0;
                overflow: hidden;
            }
            body {
                font-family: Arial, sans-serif;
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f5f5f5;
                display: flex;
                flex-direction: column;
            }
            .container {
                background: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                height: calc(100vh - 40px);
                display: flex;
                flex-direction: column;
                position: relative;
            }
            h1 {
                color: #333;
                text-align: center;
                margin-bottom: 30px;
            }
            .chat-container {
                border: 1px solid #ddd;
                border-radius: 8px;
                flex: 1;
                overflow-y: auto;
                overflow-x: hidden;
                padding: 15px 15px 100px 15px;
                background-color: #fafafa;
                scroll-behavior: smooth;
                margin-bottom: 0;
                min-height: 0;
            }
            .message {
                margin-bottom: 15px;
                padding: 10px;
                border-radius: 8px;
            }
            .user-message {
                background-color: #007bff;
                color: white;
                text-align: right;
            }
            .bot-message {
                background-color: #e9ecef;
                color: #333;
            }
            .message-input-area {
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                background: #ffffff;
                border-top: 1px solid #e0e0e0;
                padding: 20px;
                box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
                z-index: 1000;
            }
            .message-input-container {
                max-width: 800px;
                margin: 0 auto;
                display: grid;
                grid-template-columns: 1fr auto;
                gap: 15px;
                align-items: end;
            }
            .text-input {
                width: 100%;
                padding: 15px 20px;
                border: 2px solid #e0e0e0;
                border-radius: 12px;
                font-size: 16px;
                font-family: inherit;
                resize: none;
                outline: none;
                background: #fafafa;
                min-height: 24px;
                max-height: 120px;
                line-height: 1.4;
                box-sizing: border-box;
            }
            .text-input:focus {
                border-color: #007bff;
                background: #ffffff;
            }
            .text-input:disabled {
                background: #f5f5f5;
                color: #999;
                cursor: not-allowed;
            }
            .submit-btn {
                background: #007bff;
                color: white;
                border: none;
                border-radius: 12px;
                padding: 15px 25px;
                font-size: 16px;
                font-weight: 600;
                cursor: pointer;
                white-space: nowrap;
                min-width: 90px;
                height: 54px;
                box-sizing: border-box;
            }
            .submit-btn:hover:not(:disabled) {
                background: #0056b3;
            }
            .submit-btn:disabled {
                background: #cccccc;
                cursor: not-allowed;
            }
            .loading {
                color: #666;
                font-style: italic;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🍳 Recipe Agent</h1>
            <div id="chat" class="chat-container">
                <div class="message bot-message">
                    Merhaba! Ben Recipe Agent'ım. Elinizde bulunan malzemeleri söyleyin, size uygun tarifler önereyim! 
                    <br><br>
                    Örnek: "Yumurta, domates ve peynir var. Ne yapabilirim?"
                </div>
            </div>
        </div>

        <div class="message-input-area">
            <div class="message-input-container">
                <textarea
                    id="userInput"
                    class="text-input"
                    placeholder="Malzemelerinizi yazın... (örn: yumurta, domates, peynir)"
                    rows="1"
                    onkeydown="handleKeyDown(event)"
                ></textarea>
                <button id="sendBtn" class="submit-btn" onclick="submitMessage()">
                    Gönder
                </button>
            </div>
        </div>

        <script>
            let isProcessing = false;

            async function submitMessage() {
                if (isProcessing) return;

                const textarea = document.getElementById('userInput');
                const button = document.getElementById('sendBtn');
                const chatArea = document.getElementById('chat');
                const messageText = textarea.value.trim();

                if (!messageText) return;

                // Lock UI
                isProcessing = true;
                textarea.disabled = true;
                button.disabled = true;
                button.textContent = 'Gönderiliyor...';

                // Add user message
                createMessage(messageText, 'user');
                textarea.value = '';

                // Add loading message
                const loadingMsg = createMessage('Tarif arıyorum...', 'bot', 'loading');

                try {
                    const response = await fetch('/api/chat', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ message: messageText })
                    });

                    const result = await response.json();

                    // Remove loading message
                    loadingMsg.remove();

                    // Add bot response
                    createMessage(result.response, 'bot');

                } catch (error) {
                    loadingMsg.remove();
                    createMessage('Üzgünüm, bir hata oluştu. Lütfen tekrar deneyin.', 'bot');
                } finally {
                    // Unlock UI
                    isProcessing = false;
                    textarea.disabled = false;
                    button.disabled = false;
                    button.textContent = 'Gönder';
                    textarea.focus();
                }
            }

            function createMessage(text, type, extraClass = '') {
                const chatArea = document.getElementById('chat');
                const msgDiv = document.createElement('div');
                msgDiv.className = \`message \${type}-message \${extraClass}\`;
                msgDiv.innerHTML = text.replace(/\\n/g, '<br>');
                chatArea.appendChild(msgDiv);

                // Auto scroll
                setTimeout(() => {
                    chatArea.scrollTop = chatArea.scrollHeight;
                }, 50);

                return msgDiv;
            }

            function handleKeyDown(event) {
                if (event.key === 'Enter' && !event.shiftKey) {
                    event.preventDefault();
                    submitMessage();
                }
            }

            // Auto-resize textarea
            document.getElementById('userInput').addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            });
        </script>
    </body>
    </html>
  `);
});

// Chat API endpoint
app.post('/api/chat', async (req, res) => {
  try {
    const { message } = req.body;

    console.log('📝 Kullanıcı mesajı:', message);
    console.log('🔄 Recipe Agent çağrılıyor...');

    const response = await recipeAgent.generate([
      {
        role: 'user',
        content: message
      }
    ]);

    console.log('🤖 Agent yanıtı:', response.text);

    res.json({ response: response.text });

  } catch (error) {
    console.error('❌ Chat hatası:', error);
    console.error('❌ Hata detayı:', error.message);

    // MCP server hatası durumunda fallback yanıt
    let fallbackResponse = 'Üzgünüm, şu anda tarif servisimizde teknik bir sorun var. ';

    if (error.message && error.message.includes('Server not initialized')) {
      fallbackResponse += 'Tarif veritabanı bağlantısı kurulamadı. Lütfen birkaç dakika sonra tekrar deneyin.';
    } else {
      fallbackResponse += 'Lütfen daha sonra tekrar deneyin.';
    }

    res.status(500).json({
      error: 'Bir hata oluştu',
      response: fallbackResponse
    });
  }
});

// Sunucuyu başlat
app.listen(PORT, () => {
  console.log(`🚀 Recipe Agent sunucusu http://localhost:${PORT} adresinde çalışıyor`);
  console.log(`🍳 Web arayüzü için tarayıcınızda http://localhost:${PORT} adresini açın`);
  console.log(`📡 API endpoint: http://localhost:${PORT}/api/chat`);
});
