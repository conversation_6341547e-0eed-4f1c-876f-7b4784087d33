import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  EnableSagemakerServicecatalogPortfolioInput,
  EnableSagemakerServicecatalogPortfolioOutput,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface EnableSagemakerServicecatalogPortfolioCommandInput
  extends EnableSagemakerServicecatalogPortfolioInput {}
export interface EnableSagemakerServicecatalogPortfolioCommandOutput
  extends EnableSagemakerServicecatalogPortfolioOutput,
    __MetadataBearer {}
declare const EnableSagemakerServicecatalogPortfolioCommand_base: {
  new (
    input: EnableSagemakerServicecatalogPortfolioCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    EnableSagemakerServicecatalogPortfolioCommandInput,
    EnableSagemakerServicecatalogPortfolioCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    ...[input]: [] | [EnableSagemakerServicecatalogPortfolioCommandInput]
  ): import("@smithy/smithy-client").CommandImpl<
    EnableSagemakerServicecatalogPortfolioCommandInput,
    EnableSagemakerServicecatalogPortfolioCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class EnableSagemakerServicecatalogPortfolioCommand extends EnableSagemakerServicecatalogPortfolioCommand_base {
  protected static __types: {
    api: {
      input: {};
      output: {};
    };
    sdk: {
      input: EnableSagemakerServicecatalogPortfolioCommandInput;
      output: EnableSagemakerServicecatalogPortfolioCommandOutput;
    };
  };
}
