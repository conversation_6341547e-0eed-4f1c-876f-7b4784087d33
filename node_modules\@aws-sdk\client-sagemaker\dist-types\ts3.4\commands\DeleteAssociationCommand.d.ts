import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DeleteAssociationRequest,
  DeleteAssociationResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteAssociationCommandInput
  extends DeleteAssociationRequest {}
export interface DeleteAssociationCommandOutput
  extends DeleteAssociationResponse,
    __MetadataBearer {}
declare const DeleteAssociationCommand_base: {
  new (
    input: DeleteAssociationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteAssociationCommandInput,
    DeleteAssociationCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteAssociationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteAssociationCommandInput,
    DeleteAssociationCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteAssociationCommand extends DeleteAssociationCommand_base {
  protected static __types: {
    api: {
      input: DeleteAssociationRequest;
      output: DeleteAssociationResponse;
    };
    sdk: {
      input: DeleteAssociationCommandInput;
      output: DeleteAssociationCommandOutput;
    };
  };
}
