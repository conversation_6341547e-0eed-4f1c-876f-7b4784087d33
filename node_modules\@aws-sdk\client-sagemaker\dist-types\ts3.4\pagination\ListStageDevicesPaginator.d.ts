import { Paginator } from "@smithy/types";
import {
  ListStageDevicesCommandInput,
  ListStageDevicesCommandOutput,
} from "../commands/ListStageDevicesCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
export declare const paginateListStageDevices: (
  config: SageMakerPaginationConfiguration,
  input: ListStageDevicesCommandInput,
  ...rest: any[]
) => Paginator<ListStageDevicesCommandOutput>;
