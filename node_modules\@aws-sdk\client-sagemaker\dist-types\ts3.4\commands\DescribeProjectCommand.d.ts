import { Command as $Command } from "@smithy/smithy-client";
import { <PERSON>ada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeProjectInput,
  DescribeProjectOutput,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeProjectCommandInput extends DescribeProjectInput {}
export interface DescribeProjectCommandOutput
  extends DescribeProjectOutput,
    __MetadataBearer {}
declare const DescribeProjectCommand_base: {
  new (
    input: DescribeProjectCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeProjectCommandInput,
    DescribeProjectCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeProjectCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeProjectCommandInput,
    DescribeProjectCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeProjectCommand extends DescribeProjectCommand_base {
  protected static __types: {
    api: {
      input: DescribeProjectInput;
      output: DescribeProjectOutput;
    };
    sdk: {
      input: DescribeProjectCommandInput;
      output: DescribeProjectCommandOutput;
    };
  };
}
