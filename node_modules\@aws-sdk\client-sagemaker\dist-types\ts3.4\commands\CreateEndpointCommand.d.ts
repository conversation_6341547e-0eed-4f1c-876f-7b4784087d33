import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { CreateEndpointInput, CreateEndpointOutput } from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateEndpointCommandInput extends CreateEndpointInput {}
export interface CreateEndpointCommandOutput
  extends CreateEndpointOutput,
    __MetadataBearer {}
declare const CreateEndpointCommand_base: {
  new (
    input: CreateEndpointCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateEndpointCommandInput,
    CreateEndpointCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateEndpointCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateEndpointCommandInput,
    CreateEndpointCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateEndpointCommand extends CreateEndpointCommand_base {
  protected static __types: {
    api: {
      input: CreateEndpointInput;
      output: CreateEndpointOutput;
    };
    sdk: {
      input: CreateEndpointCommandInput;
      output: CreateEndpointCommandOutput;
    };
  };
}
