import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  UpdateImageVersionRequest,
  UpdateImageVersionResponse,
} from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateImageVersionCommandInput
  extends UpdateImageVersionRequest {}
export interface UpdateImageVersionCommandOutput
  extends UpdateImageVersionResponse,
    __MetadataBearer {}
declare const UpdateImageVersionCommand_base: {
  new (
    input: UpdateImageVersionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateImageVersionCommandInput,
    UpdateImageVersionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateImageVersionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateImageVersionCommandInput,
    UpdateImageVersionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateImageVersionCommand extends UpdateImageVersionCommand_base {
  protected static __types: {
    api: {
      input: UpdateImageVersionRequest;
      output: UpdateImageVersionResponse;
    };
    sdk: {
      input: UpdateImageVersionCommandInput;
      output: UpdateImageVersionCommandOutput;
    };
  };
}
