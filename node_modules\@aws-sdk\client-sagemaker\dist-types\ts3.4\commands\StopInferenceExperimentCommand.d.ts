import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { StopInferenceExperimentRequest } from "../models/models_4";
import { StopInferenceExperimentResponse } from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface StopInferenceExperimentCommandInput
  extends StopInferenceExperimentRequest {}
export interface StopInferenceExperimentCommandOutput
  extends StopInferenceExperimentResponse,
    __MetadataBearer {}
declare const StopInferenceExperimentCommand_base: {
  new (
    input: StopInferenceExperimentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StopInferenceExperimentCommandInput,
    StopInferenceExperimentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: StopInferenceExperimentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StopInferenceExperimentCommandInput,
    StopInferenceExperimentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class StopInferenceExperimentCommand extends StopInferenceExperimentCommand_base {
  protected static __types: {
    api: {
      input: StopInferenceExperimentRequest;
      output: StopInferenceExperimentResponse;
    };
    sdk: {
      input: StopInferenceExperimentCommandInput;
      output: StopInferenceExperimentCommandOutput;
    };
  };
}
