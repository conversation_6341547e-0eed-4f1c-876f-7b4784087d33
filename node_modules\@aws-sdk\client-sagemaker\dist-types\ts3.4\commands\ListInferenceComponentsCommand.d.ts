import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ListInferenceComponentsInput,
  ListInferenceComponentsOutput,
} from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface ListInferenceComponentsCommandInput
  extends ListInferenceComponentsInput {}
export interface ListInferenceComponentsCommandOutput
  extends ListInferenceComponentsOutput,
    __MetadataBearer {}
declare const ListInferenceComponentsCommand_base: {
  new (
    input: ListInferenceComponentsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListInferenceComponentsCommandInput,
    ListInferenceComponentsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    ...[input]: [] | [ListInferenceComponentsCommandInput]
  ): import("@smithy/smithy-client").CommandImpl<
    ListInferenceComponentsCommandInput,
    ListInferenceComponentsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListInferenceComponentsCommand extends ListInferenceComponentsCommand_base {
  protected static __types: {
    api: {
      input: ListInferenceComponentsInput;
      output: ListInferenceComponentsOutput;
    };
    sdk: {
      input: ListInferenceComponentsCommandInput;
      output: ListInferenceComponentsCommandOutput;
    };
  };
}
