{"name": "@smithy/types", "version": "1.2.0", "scripts": {"build": "concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types && yarn build:types:downlevel'", "build:cjs": "tsc -p tsconfig.cjs.json", "build:es": "tsc -p tsconfig.es.json", "build:types": "tsc -p tsconfig.types.json", "build:types:downlevel": "downlevel-dts dist-types dist-types/ts3.4", "stage-release": "rimraf ./.release && yarn pack && mkdir ./.release && tar zxvf ./package.tgz --directory ./.release && rm ./package.tgz", "clean": "rimraf ./dist-* && rimraf *.tsbuildinfo", "lint": "eslint -c ../../.eslintrc.js \"src/**/*.ts\"", "format": "prettier --config ../../prettier.config.js --ignore-path ../.prettierignore --write \"**/*.{ts,md,json}\"", "test": "tsc -p tsconfig.test.json", "extract:docs": "api-extractor run --local"}, "main": "./dist-cjs/index.js", "module": "./dist-es/index.js", "types": "./dist-types/index.d.ts", "author": {"name": "AWS Smithy Team", "email": "", "url": "https://smithy.io"}, "license": "Apache-2.0", "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}, "typesVersions": {"<4.0": {"dist-types/*": ["dist-types/ts3.4/*"]}}, "files": ["dist-*/**"], "homepage": "https://github.com/awslabs/smithy-typescript/tree/main/packages/types", "repository": {"type": "git", "url": "https://github.com/awslabs/smithy-typescript.git", "directory": "packages/types"}, "devDependencies": {"@tsconfig/recommended": "1.0.1", "concurrently": "7.0.0", "downlevel-dts": "0.10.1", "jest": "28.1.1", "rimraf": "3.0.2", "typedoc": "0.23.23", "typescript": "~4.9.5"}, "typedoc": {"entryPoint": "src/index.ts"}, "publishConfig": {"directory": ".release/package"}}