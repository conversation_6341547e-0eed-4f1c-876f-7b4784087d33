import { Paginator } from "@smithy/types";
import { ListAlgorithmsCommandInput, ListAlgorithmsCommandOutput } from "../commands/ListAlgorithmsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListAlgorithms: (config: SageMakerPaginationConfiguration, input: ListAlgorithmsCommandInput, ...rest: any[]) => Paginator<ListAlgorithmsCommandOutput>;
