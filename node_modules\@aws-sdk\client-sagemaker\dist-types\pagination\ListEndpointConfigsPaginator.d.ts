import { Paginator } from "@smithy/types";
import { ListEndpointConfigsCommandInput, ListEndpointConfigsCommandOutput } from "../commands/ListEndpointConfigsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListEndpointConfigs: (config: SageMakerPaginationConfiguration, input: ListEndpointConfigsCommandInput, ...rest: any[]) => Paginator<ListEndpointConfigsCommandOutput>;
