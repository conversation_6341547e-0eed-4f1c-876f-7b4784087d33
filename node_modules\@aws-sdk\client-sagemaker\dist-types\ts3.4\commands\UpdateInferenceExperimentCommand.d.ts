import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  UpdateInferenceExperimentRequest,
  UpdateInferenceExperimentResponse,
} from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateInferenceExperimentCommandInput
  extends UpdateInferenceExperimentRequest {}
export interface UpdateInferenceExperimentCommandOutput
  extends UpdateInferenceExperimentResponse,
    __MetadataBearer {}
declare const UpdateInferenceExperimentCommand_base: {
  new (
    input: UpdateInferenceExperimentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateInferenceExperimentCommandInput,
    UpdateInferenceExperimentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateInferenceExperimentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateInferenceExperimentCommandInput,
    UpdateInferenceExperimentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateInferenceExperimentCommand extends UpdateInferenceExperimentCommand_base {
  protected static __types: {
    api: {
      input: UpdateInferenceExperimentRequest;
      output: UpdateInferenceExperimentResponse;
    };
    sdk: {
      input: UpdateInferenceExperimentCommandInput;
      output: UpdateInferenceExperimentCommandOutput;
    };
  };
}
