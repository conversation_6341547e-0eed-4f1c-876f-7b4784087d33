import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  UpdateNotebookInstanceLifecycleConfigInput,
  UpdateNotebookInstanceLifecycleConfigOutput,
} from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateNotebookInstanceLifecycleConfigCommandInput
  extends UpdateNotebookInstanceLifecycleConfigInput {}
export interface UpdateNotebookInstanceLifecycleConfigCommandOutput
  extends UpdateNotebookInstanceLifecycleConfigOutput,
    __MetadataBearer {}
declare const UpdateNotebookInstanceLifecycleConfigCommand_base: {
  new (
    input: UpdateNotebookInstanceLifecycleConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateNotebookInstanceLifecycleConfigCommandInput,
    UpdateNotebookInstanceLifecycleConfigCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateNotebookInstanceLifecycleConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateNotebookInstanceLifecycleConfigCommandInput,
    UpdateNotebookInstanceLifecycleConfigCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateNotebookInstanceLifecycleConfigCommand extends UpdateNotebookInstanceLifecycleConfigCommand_base {
  protected static __types: {
    api: {
      input: UpdateNotebookInstanceLifecycleConfigInput;
      output: {};
    };
    sdk: {
      input: UpdateNotebookInstanceLifecycleConfigCommandInput;
      output: UpdateNotebookInstanceLifecycleConfigCommandOutput;
    };
  };
}
