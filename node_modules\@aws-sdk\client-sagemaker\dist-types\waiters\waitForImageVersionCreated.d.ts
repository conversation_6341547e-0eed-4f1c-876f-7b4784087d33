import { WaiterConfiguration, WaiterResult } from "@smithy/util-waiter";
import { DescribeImageVersionCommandInput } from "../commands/DescribeImageVersionCommand";
import { SageMakerClient } from "../SageMakerClient";
/**
 *
 *  @deprecated Use waitUntilImageVersionCreated instead. waitForImageVersionCreated does not throw error in non-success cases.
 */
export declare const waitForImageVersionCreated: (params: WaiterConfiguration<SageMakerClient>, input: DescribeImageVersionCommandInput) => Promise<WaiterResult>;
/**
 *
 *  @param params - Waiter configuration options.
 *  @param input - The input to DescribeImageVersionCommand for polling.
 */
export declare const waitUntilImageVersionCreated: (params: WaiterConfiguration<SageMakerClient>, input: DescribeImageVersionCommandInput) => Promise<WaiterResult>;
