import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { DeleteEdgeDeploymentStageRequest } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteEdgeDeploymentStageCommandInput
  extends DeleteEdgeDeploymentStageRequest {}
export interface DeleteEdgeDeploymentStageCommandOutput
  extends __MetadataBearer {}
declare const DeleteEdgeDeploymentStageCommand_base: {
  new (
    input: DeleteEdgeDeploymentStageCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteEdgeDeploymentStageCommandInput,
    DeleteEdgeDeploymentStageCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteEdgeDeploymentStageCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteEdgeDeploymentStageCommandInput,
    DeleteEdgeDeploymentStageCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteEdgeDeploymentStageCommand extends DeleteEdgeDeploymentStageCommand_base {
  protected static __types: {
    api: {
      input: DeleteEdgeDeploymentStageRequest;
      output: {};
    };
    sdk: {
      input: DeleteEdgeDeploymentStageCommandInput;
      output: DeleteEdgeDeploymentStageCommandOutput;
    };
  };
}
