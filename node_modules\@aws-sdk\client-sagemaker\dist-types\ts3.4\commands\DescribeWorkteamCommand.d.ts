import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeWorkteamRequest,
  DescribeWorkteamResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeWorkteamCommandInput extends DescribeWorkteamRequest {}
export interface DescribeWorkteamCommandOutput
  extends DescribeWorkteamResponse,
    __MetadataBearer {}
declare const DescribeWorkteamCommand_base: {
  new (
    input: DescribeWorkteamCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeWorkteamCommandInput,
    DescribeWorkteamCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeWorkteamCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeWorkteamCommandInput,
    DescribeWorkteamCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeWorkteamCommand extends DescribeWorkteamCommand_base {
  protected static __types: {
    api: {
      input: DescribeWorkteamRequest;
      output: DescribeWorkteamResponse;
    };
    sdk: {
      input: DescribeWorkteamCommandInput;
      output: DescribeWorkteamCommandOutput;
    };
  };
}
