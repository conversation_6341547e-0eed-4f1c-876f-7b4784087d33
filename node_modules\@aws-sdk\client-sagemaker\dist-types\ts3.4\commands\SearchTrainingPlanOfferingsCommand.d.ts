import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  SearchTrainingPlanOfferingsRequest,
  SearchTrainingPlanOfferingsResponse,
} from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface SearchTrainingPlanOfferingsCommandInput
  extends SearchTrainingPlanOfferingsRequest {}
export interface SearchTrainingPlanOfferingsCommandOutput
  extends SearchTrainingPlanOfferingsResponse,
    __MetadataBearer {}
declare const SearchTrainingPlanOfferingsCommand_base: {
  new (
    input: SearchTrainingPlanOfferingsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    SearchTrainingPlanOfferingsCommandInput,
    SearchTrainingPlanOfferingsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: SearchTrainingPlanOfferingsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    SearchTrainingPlanOfferingsCommandInput,
    SearchTrainingPlanOfferingsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class SearchTrainingPlanOfferingsCommand extends SearchTrainingPlanOfferingsCommand_base {
  protected static __types: {
    api: {
      input: SearchTrainingPlanOfferingsRequest;
      output: SearchTrainingPlanOfferingsResponse;
    };
    sdk: {
      input: SearchTrainingPlanOfferingsCommandInput;
      output: SearchTrainingPlanOfferingsCommandOutput;
    };
  };
}
