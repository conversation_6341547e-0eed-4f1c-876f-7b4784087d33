import { Paginator } from "@smithy/types";
import {
  ListModelPackagesCommandInput,
  ListModelPackagesCommandOutput,
} from "../commands/ListModelPackagesCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
export declare const paginateListModelPackages: (
  config: SageMakerPaginationConfiguration,
  input: ListModelPackagesCommandInput,
  ...rest: any[]
) => Paginator<ListModelPackagesCommandOutput>;
