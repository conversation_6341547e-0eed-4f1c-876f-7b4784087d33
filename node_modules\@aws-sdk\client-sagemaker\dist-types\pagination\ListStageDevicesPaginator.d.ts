import { Paginator } from "@smithy/types";
import { ListStageDevicesCommandInput, ListStageDevicesCommandOutput } from "../commands/ListStageDevicesCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListStageDevices: (config: SageMakerPaginationConfiguration, input: ListStageDevicesCommandInput, ...rest: any[]) => Paginator<ListStageDevicesCommandOutput>;
