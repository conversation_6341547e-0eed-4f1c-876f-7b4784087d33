import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ListInferenceRecommendationsJobStepsRequest,
  ListInferenceRecommendationsJobStepsResponse,
} from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface ListInferenceRecommendationsJobStepsCommandInput
  extends ListInferenceRecommendationsJobStepsRequest {}
export interface ListInferenceRecommendationsJobStepsCommandOutput
  extends ListInferenceRecommendationsJobStepsResponse,
    __MetadataBearer {}
declare const ListInferenceRecommendationsJobStepsCommand_base: {
  new (
    input: ListInferenceRecommendationsJobStepsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListInferenceRecommendationsJobStepsCommandInput,
    ListInferenceRecommendationsJobStepsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: ListInferenceRecommendationsJobStepsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListInferenceRecommendationsJobStepsCommandInput,
    ListInferenceRecommendationsJobStepsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListInferenceRecommendationsJobStepsCommand extends ListInferenceRecommendationsJobStepsCommand_base {
  protected static __types: {
    api: {
      input: ListInferenceRecommendationsJobStepsRequest;
      output: ListInferenceRecommendationsJobStepsResponse;
    };
    sdk: {
      input: ListInferenceRecommendationsJobStepsCommandInput;
      output: ListInferenceRecommendationsJobStepsCommandOutput;
    };
  };
}
