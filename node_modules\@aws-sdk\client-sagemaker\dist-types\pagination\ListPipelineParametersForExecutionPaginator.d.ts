import { Paginator } from "@smithy/types";
import { ListPipelineParametersForExecutionCommandInput, ListPipelineParametersForExecutionCommandOutput } from "../commands/ListPipelineParametersForExecutionCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListPipelineParametersForExecution: (config: SageMakerPaginationConfiguration, input: ListPipelineParametersForExecutionCommandInput, ...rest: any[]) => Paginator<ListPipelineParametersForExecutionCommandOutput>;
