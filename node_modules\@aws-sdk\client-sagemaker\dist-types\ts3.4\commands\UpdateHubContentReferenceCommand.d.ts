import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  UpdateHubContentReferenceRequest,
  UpdateHubContentReferenceResponse,
} from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateHubContentReferenceCommandInput
  extends UpdateHubContentReferenceRequest {}
export interface UpdateHubContentReferenceCommandOutput
  extends UpdateHubContentReferenceResponse,
    __MetadataBearer {}
declare const UpdateHubContentReferenceCommand_base: {
  new (
    input: UpdateHubContentReferenceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateHubContentReferenceCommandInput,
    UpdateHubContentReferenceCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateHubContentReferenceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateHubContentReferenceCommandInput,
    UpdateHubContentReferenceCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateHubContentReferenceCommand extends UpdateHubContentReferenceCommand_base {
  protected static __types: {
    api: {
      input: UpdateHubContentReferenceRequest;
      output: UpdateHubContentReferenceResponse;
    };
    sdk: {
      input: UpdateHubContentReferenceCommandInput;
      output: UpdateHubContentReferenceCommandOutput;
    };
  };
}
