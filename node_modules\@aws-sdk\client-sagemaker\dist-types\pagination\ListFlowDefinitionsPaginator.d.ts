import { Paginator } from "@smithy/types";
import { ListFlowDefinitionsCommandInput, ListFlowDefinitionsCommandOutput } from "../commands/ListFlowDefinitionsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListFlowDefinitions: (config: SageMakerPaginationConfiguration, input: ListFlowDefinitionsCommandInput, ...rest: any[]) => Paginator<ListFlowDefinitionsCommandOutput>;
