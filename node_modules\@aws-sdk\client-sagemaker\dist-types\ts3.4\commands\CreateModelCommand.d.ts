import { Command as $Command } from "@smithy/smithy-client";
import { <PERSON>ada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { CreateModelInput, CreateModelOutput } from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateModelCommandInput extends CreateModelInput {}
export interface CreateModelCommandOutput
  extends CreateModelOutput,
    __MetadataBearer {}
declare const CreateModelCommand_base: {
  new (
    input: CreateModelCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateModelCommandInput,
    CreateModelCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateModelCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateModelCommandInput,
    CreateModelCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateModelCommand extends CreateModelCommand_base {
  protected static __types: {
    api: {
      input: CreateModelInput;
      output: CreateModelOutput;
    };
    sdk: {
      input: CreateModelCommandInput;
      output: CreateModelCommandOutput;
    };
  };
}
