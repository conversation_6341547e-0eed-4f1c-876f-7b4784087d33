import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  SendPipelineExecutionStepFailureRequest,
  SendPipelineExecutionStepFailureResponse,
} from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface SendPipelineExecutionStepFailureCommandInput
  extends SendPipelineExecutionStepFailureRequest {}
export interface SendPipelineExecutionStepFailureCommandOutput
  extends SendPipelineExecutionStepFailureResponse,
    __MetadataBearer {}
declare const SendPipelineExecutionStepFailureCommand_base: {
  new (
    input: SendPipelineExecutionStepFailureCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    SendPipelineExecutionStepFailureCommandInput,
    SendPipelineExecutionStepFailureCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: SendPipelineExecutionStepFailureCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    SendPipelineExecutionStepFailureCommandInput,
    SendPipelineExecutionStepFailureCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class SendPipelineExecutionStepFailureCommand extends SendPipelineExecutionStepFailureCommand_base {
  protected static __types: {
    api: {
      input: SendPipelineExecutionStepFailureRequest;
      output: SendPipelineExecutionStepFailureResponse;
    };
    sdk: {
      input: SendPipelineExecutionStepFailureCommandInput;
      output: SendPipelineExecutionStepFailureCommandOutput;
    };
  };
}
