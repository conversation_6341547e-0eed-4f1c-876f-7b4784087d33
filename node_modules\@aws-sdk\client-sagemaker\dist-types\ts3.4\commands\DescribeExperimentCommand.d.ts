import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeExperimentRequest,
  DescribeExperimentResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeExperimentCommandInput
  extends DescribeExperimentRequest {}
export interface DescribeExperimentCommandOutput
  extends DescribeExperimentResponse,
    __MetadataBearer {}
declare const DescribeExperimentCommand_base: {
  new (
    input: DescribeExperimentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeExperimentCommandInput,
    DescribeExperimentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeExperimentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeExperimentCommandInput,
    DescribeExperimentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeExperimentCommand extends DescribeExperimentCommand_base {
  protected static __types: {
    api: {
      input: DescribeExperimentRequest;
      output: DescribeExperimentResponse;
    };
    sdk: {
      input: DescribeExperimentCommandInput;
      output: DescribeExperimentCommandOutput;
    };
  };
}
