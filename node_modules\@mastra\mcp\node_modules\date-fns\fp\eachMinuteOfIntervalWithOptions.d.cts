export declare const eachMinuteOfIntervalWithOptions: import("./types.js").FPFn2<
  import("../eachMinuteOfInterval.js").EachMinuteOfIntervalResult<
    import("../fp.js").Interval<
      import("../fp.js").DateArg<Date>,
      import("../fp.js").DateArg<Date>
    >,
    | import("../eachMinuteOfInterval.js").EachMinuteOfIntervalOptions<Date>
    | undefined
  >,
  | import("../eachMinuteOfInterval.js").EachMinuteOfIntervalOptions<Date>
  | undefined,
  import("../fp.js").Interval<
    import("../fp.js").DateArg<Date>,
    import("../fp.js").DateArg<Date>
  >
>;
