import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeAlgorithmInput,
  DescribeAlgorithmOutput,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeAlgorithmCommandInput extends DescribeAlgorithmInput {}
export interface DescribeAlgorithmCommandOutput
  extends DescribeAlgorithmOutput,
    __MetadataBearer {}
declare const DescribeAlgorithmCommand_base: {
  new (
    input: DescribeAlgorithmCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeAlgorithmCommandInput,
    DescribeAlgorithmCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeAlgorithmCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeAlgorithmCommandInput,
    DescribeAlgorithmCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeAlgorithmCommand extends DescribeAlgorithmCommand_base {
  protected static __types: {
    api: {
      input: DescribeAlgorithmInput;
      output: DescribeAlgorithmOutput;
    };
    sdk: {
      input: DescribeAlgorithmCommandInput;
      output: DescribeAlgorithmCommandOutput;
    };
  };
}
