{"name": "@libsql/client", "version": "0.15.7", "keywords": ["libsql", "database", "sqlite", "serverless", "vercel", "netlify", "lambda"], "description": "libSQL driver for TypeScript and JavaScript", "repository": {"type": "git", "url": "git+https://github.com/libsql/libsql-client-ts", "directory": "packages/libsql-client"}, "authors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "license": "MIT", "type": "module", "main": "lib-cjs/node.js", "types": "lib-esm/node.d.ts", "exports": {".": {"types": "./lib-esm/node.d.ts", "import": {"workerd": "./lib-esm/web.js", "deno": "./lib-esm/web.js", "edge-light": "./lib-esm/web.js", "netlify": "./lib-esm/web.js", "node": "./lib-esm/node.js", "browser": "./lib-esm/web.js", "default": "./lib-esm/node.js"}, "require": "./lib-cjs/node.js"}, "./node": {"types": "./lib-esm/node.d.ts", "import": "./lib-esm/node.js", "require": "./lib-cjs/node.js"}, "./http": {"types": "./lib-esm/http.d.ts", "import": "./lib-esm/http.js", "require": "./lib-cjs/http.js"}, "./ws": {"types": "./lib-esm/ws.d.ts", "import": "./lib-esm/ws.js", "require": "./lib-cjs/ws.js"}, "./sqlite3": {"types": "./lib-esm/sqlite3.d.ts", "import": "./lib-esm/sqlite3.js", "require": "./lib-cjs/sqlite3.js"}, "./web": {"types": "./lib-esm/web.d.ts", "import": "./lib-esm/web.js", "require": "./lib-cjs/web.js"}}, "typesVersions": {"*": {".": ["./lib-esm/node.d.ts"], "http": ["./lib-esm/http.d.ts"], "hrana": ["./lib-esm/hrana.d.ts"], "sqlite3": ["./lib-esm/sqlite3.d.ts"], "web": ["./lib-esm/web.d.ts"]}}, "files": ["lib-cjs/**", "lib-esm/**", "README.md"], "scripts": {"prepublishOnly": "npm run build", "prebuild": "rm -rf ./lib-cjs ./lib-esm", "build": "npm run build:cjs && npm run build:esm", "build:cjs": "tsc -p tsconfig.build-cjs.json", "build:esm": "tsc -p tsconfig.build-esm.json", "format:check": "prettier --check .", "postbuild": "cp package-cjs.json ./lib-cjs/package.json", "test": "jest --runInBand", "typecheck": "tsc --noEmit", "typedoc": "rm -rf ./docs && typedoc", "lint-staged": "lint-staged"}, "dependencies": {"@libsql/core": "^0.15.7", "@libsql/hrana-client": "^0.7.0", "js-base64": "^3.7.5", "libsql": "^0.5.11", "promise-limit": "^2.7.0"}, "devDependencies": {"@types/jest": "^29.2.5", "@types/node": "^18.15.5", "jest": "^29.3.1", "lint-staged": "^15.2.2", "msw": "^2.3.0", "prettier": "3.2.5", "ts-jest": "^29.0.5", "typedoc": "^0.23.28", "typescript": "^4.9.4"}}