import { bf as MastraLanguageModel } from '../base-B96VvaWm.cjs';
import '../base-aPYtPBT2.cjs';
import 'ai';
import '../types-Bo1uigWx.cjs';
import 'sift';
import 'zod';
import 'json-schema';
import '../deployer/index.cjs';
import '../bundler/index.cjs';
import '@opentelemetry/api';
import '../logger-EhZkzZOr.cjs';
import 'stream';
import '@opentelemetry/sdk-trace-base';
import 'node:http';
import 'hono';
import '../runtime-context/index.cjs';
import '../tts/index.cjs';
import '../vector/index.cjs';
import '../vector/filter/index.cjs';
import 'xstate';
import 'node:events';
import 'events';
import '../workflows/constants.cjs';
import 'hono/cors';
import 'hono-openapi';
import 'ai/test';

interface RelevanceScoreProvider {
    getRelevanceScore(text1: string, text2: string): Promise<number>;
}
declare function createSimilarityPrompt(query: string, text: string): string;

declare class CohereRelevanceScorer implements RelevanceScoreProvider {
    private client;
    private model;
    constructor(model: string, apiKey?: string);
    getRelevanceScore(query: string, text: string): Promise<number>;
}

declare class MastraAgentRelevanceScorer implements RelevanceScoreProvider {
    private agent;
    constructor(name: string, model: MastraLanguageModel);
    getRelevanceScore(query: string, text: string): Promise<number>;
}

export { CohereRelevanceScorer, MastraAgentRelevanceScorer, type RelevanceScoreProvider, createSimilarityPrompt };
