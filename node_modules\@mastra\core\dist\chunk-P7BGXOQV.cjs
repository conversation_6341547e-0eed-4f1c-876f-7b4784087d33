'use strict';

var chunk6KB5CPBV_cjs = require('./chunk-6KB5CPBV.cjs');
var chunkYEULQPUY_cjs = require('./chunk-YEULQPUY.cjs');

// src/storage/base.ts
var MastraStorage = class extends chunkYEULQPUY_cjs.MastraBase {
  /** @deprecated import from { TABLE_WORKFLOW_SNAPSHOT } '@mastra/core/storage' instead */
  static TABLE_WORKFLOW_SNAPSHOT = chunk6KB5CPBV_cjs.TABLE_WORKFLOW_SNAPSHOT;
  /** @deprecated import from { TABLE_EVALS } '@mastra/core/storage' instead */
  static TABLE_EVALS = chunk6KB5CPBV_cjs.TABLE_EVALS;
  /** @deprecated import from { TABLE_MESSAGES } '@mastra/core/storage' instead */
  static TABLE_MESSAGES = chunk6KB5CPBV_cjs.TABLE_MESSAGES;
  /** @deprecated import from { TABLE_THREADS } '@mastra/core/storage' instead */
  static TABLE_THREADS = chunk6KB5CPBV_cjs.TABLE_THREADS;
  /** @deprecated import { TABLE_TRACES } from '@mastra/core/storage' instead */
  static TABLE_TRACES = chunk6KB5CPBV_cjs.TABLE_TRACES;
  hasInitialized = null;
  shouldCacheInit = true;
  constructor({ name }) {
    super({
      component: "STORAGE",
      name
    });
  }
  batchTraceInsert({ records }) {
    return this.batchInsert({ tableName: chunk6KB5CPBV_cjs.TABLE_TRACES, records });
  }
  async init() {
    if (this.shouldCacheInit && await this.hasInitialized) {
      return;
    }
    this.hasInitialized = Promise.all([
      this.createTable({
        tableName: chunk6KB5CPBV_cjs.TABLE_WORKFLOW_SNAPSHOT,
        schema: chunk6KB5CPBV_cjs.TABLE_SCHEMAS[chunk6KB5CPBV_cjs.TABLE_WORKFLOW_SNAPSHOT]
      }),
      this.createTable({
        tableName: chunk6KB5CPBV_cjs.TABLE_EVALS,
        schema: chunk6KB5CPBV_cjs.TABLE_SCHEMAS[chunk6KB5CPBV_cjs.TABLE_EVALS]
      }),
      this.createTable({
        tableName: chunk6KB5CPBV_cjs.TABLE_THREADS,
        schema: chunk6KB5CPBV_cjs.TABLE_SCHEMAS[chunk6KB5CPBV_cjs.TABLE_THREADS]
      }),
      this.createTable({
        tableName: chunk6KB5CPBV_cjs.TABLE_MESSAGES,
        schema: chunk6KB5CPBV_cjs.TABLE_SCHEMAS[chunk6KB5CPBV_cjs.TABLE_MESSAGES]
      }),
      this.createTable({
        tableName: chunk6KB5CPBV_cjs.TABLE_TRACES,
        schema: chunk6KB5CPBV_cjs.TABLE_SCHEMAS[chunk6KB5CPBV_cjs.TABLE_TRACES]
      })
    ]).then(() => true);
    await this.hasInitialized;
  }
  async persistWorkflowSnapshot({
    workflowName,
    runId,
    snapshot
  }) {
    await this.init();
    const data = {
      workflow_name: workflowName,
      run_id: runId,
      snapshot,
      createdAt: /* @__PURE__ */ new Date(),
      updatedAt: /* @__PURE__ */ new Date()
    };
    this.logger.debug("Persisting workflow snapshot", { workflowName, runId, data });
    await this.insert({
      tableName: chunk6KB5CPBV_cjs.TABLE_WORKFLOW_SNAPSHOT,
      record: data
    });
  }
  async loadWorkflowSnapshot({
    workflowName,
    runId
  }) {
    if (!this.hasInitialized) {
      await this.init();
    }
    this.logger.debug("Loading workflow snapshot", { workflowName, runId });
    const d = await this.load({
      tableName: chunk6KB5CPBV_cjs.TABLE_WORKFLOW_SNAPSHOT,
      keys: { workflow_name: workflowName, run_id: runId }
    });
    return d ? d.snapshot : null;
  }
};

exports.MastraStorage = MastraStorage;
