import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeInferenceRecommendationsJobRequest,
  DescribeInferenceRecommendationsJobResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeInferenceRecommendationsJobCommandInput
  extends DescribeInferenceRecommendationsJobRequest {}
export interface DescribeInferenceRecommendationsJobCommandOutput
  extends DescribeInferenceRecommendationsJobResponse,
    __MetadataBearer {}
declare const DescribeInferenceRecommendationsJobCommand_base: {
  new (
    input: DescribeInferenceRecommendationsJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeInferenceRecommendationsJobCommandInput,
    DescribeInferenceRecommendationsJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeInferenceRecommendationsJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeInferenceRecommendationsJobCommandInput,
    DescribeInferenceRecommendationsJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeInferenceRecommendationsJobCommand extends DescribeInferenceRecommendationsJobCommand_base {
  protected static __types: {
    api: {
      input: DescribeInferenceRecommendationsJobRequest;
      output: DescribeInferenceRecommendationsJobResponse;
    };
    sdk: {
      input: DescribeInferenceRecommendationsJobCommandInput;
      output: DescribeInferenceRecommendationsJobCommandOutput;
    };
  };
}
