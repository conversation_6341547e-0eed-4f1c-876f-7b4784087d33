import { Paginator } from "@smithy/types";
import { ListProjectsCommandInput, ListProjectsCommandOutput } from "../commands/ListProjectsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListProjects: (config: SageMakerPaginationConfiguration, input: ListProjectsCommandInput, ...rest: any[]) => Paginator<ListProjectsCommandOutput>;
