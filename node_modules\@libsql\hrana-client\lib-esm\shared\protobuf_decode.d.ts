import * as d from "../encoding/protobuf/decode.js";
import * as proto from "./proto.js";
export declare const Error: d.MessageDef<proto.Error>;
export declare const StmtResult: d.MessageDef<proto.StmtResult>;
export declare const BatchResult: d.MessageDef<proto.BatchResult>;
export declare const CursorEntry: d.MessageDef<proto.CursorEntry>;
export declare const DescribeResult: d.MessageDef<proto.DescribeResult>;
