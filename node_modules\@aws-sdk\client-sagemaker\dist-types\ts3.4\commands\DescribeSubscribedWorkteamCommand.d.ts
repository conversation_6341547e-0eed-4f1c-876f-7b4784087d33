import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeSubscribedWorkteamRequest,
  DescribeSubscribedWorkteamResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeSubscribedWorkteamCommandInput
  extends DescribeSubscribedWorkteamRequest {}
export interface DescribeSubscribedWorkteamCommandOutput
  extends DescribeSubscribedWorkteamResponse,
    __MetadataBearer {}
declare const DescribeSubscribedWorkteamCommand_base: {
  new (
    input: DescribeSubscribedWorkteamCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeSubscribedWorkteamCommandInput,
    DescribeSubscribedWorkteamCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeSubscribedWorkteamCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeSubscribedWorkteamCommandInput,
    DescribeSubscribedWorkteamCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeSubscribedWorkteamCommand extends DescribeSubscribedWorkteamCommand_base {
  protected static __types: {
    api: {
      input: DescribeSubscribedWorkteamRequest;
      output: DescribeSubscribedWorkteamResponse;
    };
    sdk: {
      input: DescribeSubscribedWorkteamCommandInput;
      output: DescribeSubscribedWorkteamCommandOutput;
    };
  };
}
