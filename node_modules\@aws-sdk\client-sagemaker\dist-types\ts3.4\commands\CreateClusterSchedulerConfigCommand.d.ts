import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateClusterSchedulerConfigRequest,
  CreateClusterSchedulerConfigResponse,
} from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateClusterSchedulerConfigCommandInput
  extends CreateClusterSchedulerConfigRequest {}
export interface CreateClusterSchedulerConfigCommandOutput
  extends CreateClusterSchedulerConfigResponse,
    __MetadataBearer {}
declare const CreateClusterSchedulerConfigCommand_base: {
  new (
    input: CreateClusterSchedulerConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateClusterSchedulerConfigCommandInput,
    CreateClusterSchedulerConfigCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateClusterSchedulerConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateClusterSchedulerConfigCommandInput,
    CreateClusterSchedulerConfigCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateClusterSchedulerConfigCommand extends CreateClusterSchedulerConfigCommand_base {
  protected static __types: {
    api: {
      input: CreateClusterSchedulerConfigRequest;
      output: CreateClusterSchedulerConfigResponse;
    };
    sdk: {
      input: CreateClusterSchedulerConfigCommandInput;
      output: CreateClusterSchedulerConfigCommandOutput;
    };
  };
}
