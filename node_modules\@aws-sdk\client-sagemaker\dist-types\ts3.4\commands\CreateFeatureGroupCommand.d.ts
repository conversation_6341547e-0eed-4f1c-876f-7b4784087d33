import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateFeatureGroupRequest,
  CreateFeatureGroupResponse,
} from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateFeatureGroupCommandInput
  extends CreateFeatureGroupRequest {}
export interface CreateFeatureGroupCommandOutput
  extends CreateFeatureGroupResponse,
    __MetadataBearer {}
declare const CreateFeatureGroupCommand_base: {
  new (
    input: CreateFeatureGroupCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateFeatureGroupCommandInput,
    CreateFeatureGroupCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateFeatureGroupCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateFeatureGroupCommandInput,
    CreateFeatureGroupCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateFeatureGroupCommand extends CreateFeatureGroupCommand_base {
  protected static __types: {
    api: {
      input: CreateFeatureGroupRequest;
      output: CreateFeatureGroupResponse;
    };
    sdk: {
      input: CreateFeatureGroupCommandInput;
      output: CreateFeatureGroupCommandOutput;
    };
  };
}
