import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { DeleteModelPackageGroupInput } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteModelPackageGroupCommandInput
  extends DeleteModelPackageGroupInput {}
export interface DeleteModelPackageGroupCommandOutput
  extends __MetadataBearer {}
declare const DeleteModelPackageGroupCommand_base: {
  new (
    input: DeleteModelPackageGroupCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteModelPackageGroupCommandInput,
    DeleteModelPackageGroupCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteModelPackageGroupCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteModelPackageGroupCommandInput,
    DeleteModelPackageGroupCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteModelPackageGroupCommand extends DeleteModelPackageGroupCommand_base {
  protected static __types: {
    api: {
      input: DeleteModelPackageGroupInput;
      output: {};
    };
    sdk: {
      input: DeleteModelPackageGroupCommandInput;
      output: DeleteModelPackageGroupCommandOutput;
    };
  };
}
