import { Paginator } from "@smithy/types";
import { ListUserProfilesCommandInput, ListUserProfilesCommandOutput } from "../commands/ListUserProfilesCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListUserProfiles: (config: SageMakerPaginationConfiguration, input: ListUserProfilesCommandInput, ...rest: any[]) => Paginator<ListUserProfilesCommandOutput>;
