import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DeleteTrialComponentRequest,
  DeleteTrialComponentResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteTrialComponentCommandInput
  extends DeleteTrialComponentRequest {}
export interface DeleteTrialComponentCommandOutput
  extends DeleteTrialComponentResponse,
    __MetadataBearer {}
declare const DeleteTrialComponentCommand_base: {
  new (
    input: DeleteTrialComponentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteTrialComponentCommandInput,
    DeleteTrialComponentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteTrialComponentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteTrialComponentCommandInput,
    DeleteTrialComponentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteTrialComponentCommand extends DeleteTrialComponentCommand_base {
  protected static __types: {
    api: {
      input: DeleteTrialComponentRequest;
      output: DeleteTrialComponentResponse;
    };
    sdk: {
      input: DeleteTrialComponentCommandInput;
      output: DeleteTrialComponentCommandOutput;
    };
  };
}
