import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeTrialComponentRequest,
  DescribeTrialComponentResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeTrialComponentCommandInput
  extends DescribeTrialComponentRequest {}
export interface DescribeTrialComponentCommandOutput
  extends DescribeTrialComponentResponse,
    __MetadataBearer {}
declare const DescribeTrialComponentCommand_base: {
  new (
    input: DescribeTrialComponentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeTrialComponentCommandInput,
    DescribeTrialComponentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeTrialComponentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeTrialComponentCommandInput,
    DescribeTrialComponentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeTrialComponentCommand extends DescribeTrialComponentCommand_base {
  protected static __types: {
    api: {
      input: DescribeTrialComponentRequest;
      output: DescribeTrialComponentResponse;
    };
    sdk: {
      input: DescribeTrialComponentCommandInput;
      output: DescribeTrialComponentCommandOutput;
    };
  };
}
