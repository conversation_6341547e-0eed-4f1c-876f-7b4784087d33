import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DeleteMonitoringScheduleRequest } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteMonitoringScheduleCommandInput
  extends DeleteMonitoringScheduleRequest {}
export interface DeleteMonitoringScheduleCommandOutput
  extends __MetadataBearer {}
declare const DeleteMonitoringScheduleCommand_base: {
  new (
    input: DeleteMonitoringScheduleCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteMonitoringScheduleCommandInput,
    DeleteMonitoringScheduleCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteMonitoringScheduleCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteMonitoringScheduleCommandInput,
    DeleteMonitoringScheduleCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteMonitoringScheduleCommand extends DeleteMonitoringScheduleCommand_base {
  protected static __types: {
    api: {
      input: DeleteMonitoringScheduleRequest;
      output: {};
    };
    sdk: {
      input: DeleteMonitoringScheduleCommandInput;
      output: DeleteMonitoringScheduleCommandOutput;
    };
  };
}
