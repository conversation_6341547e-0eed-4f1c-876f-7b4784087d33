import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { StartNotebookInstanceInput } from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface StartNotebookInstanceCommandInput
  extends StartNotebookInstanceInput {}
export interface StartNotebookInstanceCommandOutput extends __MetadataBearer {}
declare const StartNotebookInstanceCommand_base: {
  new (
    input: StartNotebookInstanceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StartNotebookInstanceCommandInput,
    StartNotebookInstanceCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: StartNotebookInstanceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StartNotebookInstanceCommandInput,
    StartNotebookInstanceCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class StartNotebookInstanceCommand extends StartNotebookInstanceCommand_base {
  protected static __types: {
    api: {
      input: StartNotebookInstanceInput;
      output: {};
    };
    sdk: {
      input: StartNotebookInstanceCommandInput;
      output: StartNotebookInstanceCommandOutput;
    };
  };
}
