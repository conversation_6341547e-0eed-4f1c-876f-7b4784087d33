import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { StopCompilationJobRequest } from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface StopCompilationJobCommandInput
  extends StopCompilationJobRequest {}
export interface StopCompilationJobCommandOutput extends __MetadataBearer {}
declare const StopCompilationJobCommand_base: {
  new (
    input: StopCompilationJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StopCompilationJobCommandInput,
    StopCompilationJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: StopCompilationJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StopCompilationJobCommandInput,
    StopCompilationJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class StopCompilationJobCommand extends StopCompilationJobCommand_base {
  protected static __types: {
    api: {
      input: StopCompilationJobRequest;
      output: {};
    };
    sdk: {
      input: StopCompilationJobCommandInput;
      output: StopCompilationJobCommandOutput;
    };
  };
}
