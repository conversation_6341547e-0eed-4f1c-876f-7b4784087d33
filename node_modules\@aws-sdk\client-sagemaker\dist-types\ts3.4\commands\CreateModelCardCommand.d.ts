import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  CreateModelCardRequest,
  CreateModelCardResponse,
} from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateModelCardCommandInput extends CreateModelCardRequest {}
export interface CreateModelCardCommandOutput
  extends CreateModelCardResponse,
    __MetadataBearer {}
declare const CreateModelCardCommand_base: {
  new (
    input: CreateModelCardCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateModelCardCommandInput,
    CreateModelCardCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateModelCardCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateModelCardCommandInput,
    CreateModelCardCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateModelCardCommand extends CreateModelCardCommand_base {
  protected static __types: {
    api: {
      input: CreateModelCardRequest;
      output: CreateModelCardResponse;
    };
    sdk: {
      input: CreateModelCardCommandInput;
      output: CreateModelCardCommandOutput;
    };
  };
}
