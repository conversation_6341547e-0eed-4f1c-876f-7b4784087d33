import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { StopInferenceRecommendationsJobRequest } from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface StopInferenceRecommendationsJobCommandInput
  extends StopInferenceRecommendationsJobRequest {}
export interface StopInferenceRecommendationsJobCommandOutput
  extends __MetadataBearer {}
declare const StopInferenceRecommendationsJobCommand_base: {
  new (
    input: StopInferenceRecommendationsJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StopInferenceRecommendationsJobCommandInput,
    StopInferenceRecommendationsJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: StopInferenceRecommendationsJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StopInferenceRecommendationsJobCommandInput,
    StopInferenceRecommendationsJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class StopInferenceRecommendationsJobCommand extends StopInferenceRecommendationsJobCommand_base {
  protected static __types: {
    api: {
      input: StopInferenceRecommendationsJobRequest;
      output: {};
    };
    sdk: {
      input: StopInferenceRecommendationsJobCommandInput;
      output: StopInferenceRecommendationsJobCommandOutput;
    };
  };
}
