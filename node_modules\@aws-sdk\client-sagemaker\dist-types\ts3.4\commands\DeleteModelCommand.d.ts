import { Command as $Command } from "@smithy/smithy-client";
import { <PERSON>ada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DeleteModelInput } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteModelCommandInput extends DeleteModelInput {}
export interface DeleteModelCommandOutput extends __MetadataBearer {}
declare const DeleteModelCommand_base: {
  new (
    input: DeleteModelCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteModelCommandInput,
    DeleteModelCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteModelCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteModelCommandInput,
    DeleteModelCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteModelCommand extends DeleteModelCommand_base {
  protected static __types: {
    api: {
      input: DeleteModelInput;
      output: {};
    };
    sdk: {
      input: DeleteModelCommandInput;
      output: DeleteModelCommandOutput;
    };
  };
}
