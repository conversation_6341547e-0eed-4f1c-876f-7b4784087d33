import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeAutoMLJobRequest,
  DescribeAutoMLJobResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeAutoMLJobCommandInput
  extends DescribeAutoMLJobRequest {}
export interface DescribeAutoMLJobCommandOutput
  extends DescribeAutoMLJobResponse,
    __MetadataBearer {}
declare const DescribeAutoMLJobCommand_base: {
  new (
    input: DescribeAutoMLJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeAutoMLJobCommandInput,
    DescribeAutoMLJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeAutoMLJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeAutoMLJobCommandInput,
    DescribeAutoMLJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeAutoMLJobCommand extends DescribeAutoMLJobCommand_base {
  protected static __types: {
    api: {
      input: DescribeAutoMLJobRequest;
      output: DescribeAutoMLJobResponse;
    };
    sdk: {
      input: DescribeAutoMLJobCommandInput;
      output: DescribeAutoMLJobCommandOutput;
    };
  };
}
