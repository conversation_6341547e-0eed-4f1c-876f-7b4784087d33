import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeDomainRequest,
  DescribeDomainResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeDomainCommandInput extends DescribeDomainRequest {}
export interface DescribeDomainCommandOutput
  extends DescribeDomainResponse,
    __MetadataBearer {}
declare const DescribeDomainCommand_base: {
  new (
    input: DescribeDomainCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeDomainCommandInput,
    DescribeDomainCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeDomainCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeDomainCommandInput,
    DescribeDomainCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeDomainCommand extends DescribeDomainCommand_base {
  protected static __types: {
    api: {
      input: DescribeDomainRequest;
      output: DescribeDomainResponse;
    };
    sdk: {
      input: DescribeDomainCommandInput;
      output: DescribeDomainCommandOutput;
    };
  };
}
