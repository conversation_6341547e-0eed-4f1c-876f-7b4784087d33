import { Paginator } from "@smithy/types";
import { ListEndpointsCommandInput, ListEndpointsCommandOutput } from "../commands/ListEndpointsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListEndpoints: (config: SageMakerPaginationConfiguration, input: ListEndpointsCommandInput, ...rest: any[]) => Paginator<ListEndpointsCommandOutput>;
