import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateHyperParameterTuningJobRequest,
  CreateHyperParameterTuningJobResponse,
} from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateHyperParameterTuningJobCommandInput
  extends CreateHyperParameterTuningJobRequest {}
export interface CreateHyperParameterTuningJobCommandOutput
  extends CreateHyperParameterTuningJobResponse,
    __MetadataBearer {}
declare const CreateHyperParameterTuningJobCommand_base: {
  new (
    input: CreateHyperParameterTuningJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateHyperParameterTuningJobCommandInput,
    CreateHyperParameterTuningJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateHyperParameterTuningJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateHyperParameterTuningJobCommandInput,
    CreateHyperParameterTuningJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateHyperParameterTuningJobCommand extends CreateHyperParameterTuningJobCommand_base {
  protected static __types: {
    api: {
      input: CreateHyperParameterTuningJobRequest;
      output: CreateHyperParameterTuningJobResponse;
    };
    sdk: {
      input: CreateHyperParameterTuningJobCommandInput;
      output: CreateHyperParameterTuningJobCommandOutput;
    };
  };
}
