import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeTrialRequest,
  DescribeTrialResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeTrialCommandInput extends DescribeTrialRequest {}
export interface DescribeTrialCommandOutput
  extends DescribeTrialResponse,
    __MetadataBearer {}
declare const DescribeTrialCommand_base: {
  new (
    input: DescribeTrialCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeTrialCommandInput,
    DescribeTrialCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeTrialCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeTrialCommandInput,
    DescribeTrialCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeTrialCommand extends DescribeTrialCommand_base {
  protected static __types: {
    api: {
      input: DescribeTrialRequest;
      output: DescribeTrialResponse;
    };
    sdk: {
      input: DescribeTrialCommandInput;
      output: DescribeTrialCommandOutput;
    };
  };
}
