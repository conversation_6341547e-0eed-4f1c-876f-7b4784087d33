import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeLineageGroupRequest,
  DescribeLineageGroupResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeLineageGroupCommandInput
  extends DescribeLineageGroupRequest {}
export interface DescribeLineageGroupCommandOutput
  extends DescribeLineageGroupResponse,
    __MetadataBearer {}
declare const DescribeLineageGroupCommand_base: {
  new (
    input: DescribeLineageGroupCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeLineageGroupCommandInput,
    DescribeLineageGroupCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeLineageGroupCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeLineageGroupCommandInput,
    DescribeLineageGroupCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeLineageGroupCommand extends DescribeLineageGroupCommand_base {
  protected static __types: {
    api: {
      input: DescribeLineageGroupRequest;
      output: DescribeLineageGroupResponse;
    };
    sdk: {
      input: DescribeLineageGroupCommandInput;
      output: DescribeLineageGroupCommandOutput;
    };
  };
}
