import { Command as $Command } from "@smithy/smithy-client";
import { <PERSON>ada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DeleteCodeRepositoryInput } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteCodeRepositoryCommandInput
  extends DeleteCodeRepositoryInput {}
export interface DeleteCodeRepositoryCommandOutput extends __MetadataBearer {}
declare const DeleteCodeRepositoryCommand_base: {
  new (
    input: DeleteCodeRepositoryCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteCodeRepositoryCommandInput,
    DeleteCodeRepositoryCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteCodeRepositoryCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteCodeRepositoryCommandInput,
    DeleteCodeRepositoryCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteCodeRepositoryCommand extends DeleteCodeRepositoryCommand_base {
  protected static __types: {
    api: {
      input: DeleteCodeRepositoryInput;
      output: {};
    };
    sdk: {
      input: DeleteCodeRepositoryCommandInput;
      output: DeleteCodeRepositoryCommandOutput;
    };
  };
}
