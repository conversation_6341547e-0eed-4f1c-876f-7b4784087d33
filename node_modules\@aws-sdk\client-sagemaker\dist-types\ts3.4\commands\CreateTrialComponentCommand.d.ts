import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateTrialComponentRequest,
  CreateTrialComponentResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateTrialComponentCommandInput
  extends CreateTrialComponentRequest {}
export interface CreateTrialComponentCommandOutput
  extends CreateTrialComponentResponse,
    __MetadataBearer {}
declare const CreateTrialComponentCommand_base: {
  new (
    input: CreateTrialComponentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateTrialComponentCommandInput,
    CreateTrialComponentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateTrialComponentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateTrialComponentCommandInput,
    CreateTrialComponentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateTrialComponentCommand extends CreateTrialComponentCommand_base {
  protected static __types: {
    api: {
      input: CreateTrialComponentRequest;
      output: CreateTrialComponentResponse;
    };
    sdk: {
      input: CreateTrialComponentCommandInput;
      output: CreateTrialComponentCommandOutput;
    };
  };
}
