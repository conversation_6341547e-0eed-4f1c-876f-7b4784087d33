import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { DeleteEdgeDeploymentPlanRequest } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteEdgeDeploymentPlanCommandInput
  extends DeleteEdgeDeploymentPlanRequest {}
export interface DeleteEdgeDeploymentPlanCommandOutput
  extends __MetadataBearer {}
declare const DeleteEdgeDeploymentPlanCommand_base: {
  new (
    input: DeleteEdgeDeploymentPlanCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteEdgeDeploymentPlanCommandInput,
    DeleteEdgeDeploymentPlanCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteEdgeDeploymentPlanCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteEdgeDeploymentPlanCommandInput,
    DeleteEdgeDeploymentPlanCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteEdgeDeploymentPlanCommand extends DeleteEdgeDeploymentPlanCommand_base {
  protected static __types: {
    api: {
      input: DeleteEdgeDeploymentPlanRequest;
      output: {};
    };
    sdk: {
      input: DeleteEdgeDeploymentPlanCommandInput;
      output: DeleteEdgeDeploymentPlanCommandOutput;
    };
  };
}
