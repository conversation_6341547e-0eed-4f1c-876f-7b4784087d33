import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ListCodeRepositoriesInput,
  ListCodeRepositoriesOutput,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface ListCodeRepositoriesCommandInput
  extends ListCodeRepositoriesInput {}
export interface ListCodeRepositoriesCommandOutput
  extends ListCodeRepositoriesOutput,
    __MetadataBearer {}
declare const ListCodeRepositoriesCommand_base: {
  new (
    input: ListCodeRepositoriesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListCodeRepositoriesCommandInput,
    ListCodeRepositoriesCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    ...[input]: [] | [ListCodeRepositoriesCommandInput]
  ): import("@smithy/smithy-client").CommandImpl<
    ListCodeRepositoriesCommandInput,
    ListCodeRepositoriesCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListCodeRepositoriesCommand extends ListCodeRepositoriesCommand_base {
  protected static __types: {
    api: {
      input: ListCodeRepositoriesInput;
      output: ListCodeRepositoriesOutput;
    };
    sdk: {
      input: ListCodeRepositoriesCommandInput;
      output: ListCodeRepositoriesCommandOutput;
    };
  };
}
