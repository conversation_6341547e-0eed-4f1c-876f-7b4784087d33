import * as d from "../encoding/json/decode.js";
import * as proto from "./proto.js";
export declare function Error(obj: d.Obj): proto.Error;
export declare function StmtResult(obj: d.Obj): proto.StmtResult;
export declare function BatchResult(obj: d.Obj): proto.BatchResult;
export declare function CursorEntry(obj: d.Obj): proto.CursorEntry;
export declare function DescribeResult(obj: d.Obj): proto.DescribeResult;
export declare function Value(obj: d.Obj): proto.Value;
