import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ListResourceCatalogsRequest,
  ListResourceCatalogsResponse,
} from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface ListResourceCatalogsCommandInput
  extends ListResourceCatalogsRequest {}
export interface ListResourceCatalogsCommandOutput
  extends ListResourceCatalogsResponse,
    __MetadataBearer {}
declare const ListResourceCatalogsCommand_base: {
  new (
    input: ListResourceCatalogsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListResourceCatalogsCommandInput,
    ListResourceCatalogsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    ...[input]: [] | [ListResourceCatalogsCommandInput]
  ): import("@smithy/smithy-client").CommandImpl<
    ListResourceCatalogsCommandInput,
    ListResourceCatalogsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListResourceCatalogsCommand extends ListResourceCatalogsCommand_base {
  protected static __types: {
    api: {
      input: ListResourceCatalogsRequest;
      output: ListResourceCatalogsResponse;
    };
    sdk: {
      input: ListResourceCatalogsCommandInput;
      output: ListResourceCatalogsCommandOutput;
    };
  };
}
