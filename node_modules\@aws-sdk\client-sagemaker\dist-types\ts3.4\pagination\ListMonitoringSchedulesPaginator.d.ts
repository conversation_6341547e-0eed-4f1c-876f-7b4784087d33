import { Paginator } from "@smithy/types";
import {
  ListMonitoringSchedulesCommandInput,
  ListMonitoringSchedulesCommandOutput,
} from "../commands/ListMonitoringSchedulesCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
export declare const paginateListMonitoringSchedules: (
  config: SageMakerPaginationConfiguration,
  input: ListMonitoringSchedulesCommandInput,
  ...rest: any[]
) => Paginator<ListMonitoringSchedulesCommandOutput>;
