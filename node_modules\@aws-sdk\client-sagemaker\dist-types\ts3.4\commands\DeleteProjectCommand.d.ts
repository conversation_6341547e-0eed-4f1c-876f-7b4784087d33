import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DeleteProjectInput } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteProjectCommandInput extends DeleteProjectInput {}
export interface DeleteProjectCommandOutput extends __MetadataBearer {}
declare const DeleteProjectCommand_base: {
  new (
    input: DeleteProjectCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteProjectCommandInput,
    DeleteProjectCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteProjectCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteProjectCommandInput,
    DeleteProjectCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteProjectCommand extends DeleteProjectCommand_base {
  protected static __types: {
    api: {
      input: DeleteProjectInput;
      output: {};
    };
    sdk: {
      input: DeleteProjectCommandInput;
      output: DeleteProjectCommandOutput;
    };
  };
}
