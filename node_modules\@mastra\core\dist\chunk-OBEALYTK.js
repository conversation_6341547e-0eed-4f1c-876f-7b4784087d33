import { Transform } from 'stream';

// src/logger/multi-logger.ts
var MultiLogger = class {
  loggers;
  constructor(loggers) {
    this.loggers = loggers;
  }
  debug(message, ...args) {
    this.loggers.forEach((logger) => logger.debug(message, ...args));
  }
  info(message, ...args) {
    this.loggers.forEach((logger) => logger.info(message, ...args));
  }
  warn(message, ...args) {
    this.loggers.forEach((logger) => logger.warn(message, ...args));
  }
  error(message, ...args) {
    this.loggers.forEach((logger) => logger.error(message, ...args));
  }
  getTransports() {
    const transports = [];
    this.loggers.forEach((logger) => transports.push(...logger.getTransports().entries()));
    return new Map(transports);
  }
  async getLogs(transportId) {
    for (const logger of this.loggers) {
      const logs = await logger.getLogs(transportId);
      if (logs.length > 0) {
        return logs;
      }
    }
    return [];
  }
  async getLogsByRunId(args) {
    for (const logger of this.loggers) {
      const logs = await logger.getLogsByRunId(args);
      if (logs.length > 0) {
        return logs;
      }
    }
    return [];
  }
};

// src/logger/noop-logger.ts
var noopLogger = {
  debug: () => {
  },
  info: () => {
  },
  warn: () => {
  },
  error: () => {
  },
  cleanup: async () => {
  },
  getTransports: () => /* @__PURE__ */ new Map(),
  getLogs: async () => [],
  getLogsByRunId: async () => []
};
var LoggerTransport = class extends Transform {
  constructor(opts = {}) {
    super({ ...opts, objectMode: true });
  }
  async getLogsByRunId(_args) {
    return [];
  }
  async getLogs() {
    return [];
  }
};

export { LoggerTransport, MultiLogger, noopLogger };
