import { Paginator } from "@smithy/types";
import { ListInferenceRecommendationsJobStepsCommandInput, ListInferenceRecommendationsJobStepsCommandOutput } from "../commands/ListInferenceRecommendationsJobStepsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListInferenceRecommendationsJobSteps: (config: SageMakerPaginationConfiguration, input: ListInferenceRecommendationsJobStepsCommandInput, ...rest: any[]) => Paginator<ListInferenceRecommendationsJobStepsCommandOutput>;
