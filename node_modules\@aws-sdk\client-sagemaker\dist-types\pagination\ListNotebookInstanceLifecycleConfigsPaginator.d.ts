import { Paginator } from "@smithy/types";
import { ListNotebookInstanceLifecycleConfigsCommandInput, ListNotebookInstanceLifecycleConfigsCommandOutput } from "../commands/ListNotebookInstanceLifecycleConfigsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListNotebookInstanceLifecycleConfigs: (config: SageMakerPaginationConfiguration, input: ListNotebookInstanceLifecycleConfigsCommandInput, ...rest: any[]) => Paginator<ListNotebookInstanceLifecycleConfigsCommandOutput>;
