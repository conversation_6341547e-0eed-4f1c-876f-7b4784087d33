import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { AddTagsInput, AddTagsOutput } from "../models/models_0";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface AddTagsCommandInput extends AddTagsInput {}
export interface AddTagsCommandOutput extends AddTagsOutput, __MetadataBearer {}
declare const AddTagsCommand_base: {
  new (input: AddTagsCommandInput): import("@smithy/smithy-client").CommandImpl<
    AddTagsCommandInput,
    AddTagsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (input: AddTagsCommandInput): import("@smithy/smithy-client").CommandImpl<
    AddTagsCommandInput,
    AddTagsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class AddTagsCommand extends AddTagsCommand_base {
  protected static __types: {
    api: {
      input: AddTagsInput;
      output: AddTagsOutput;
    };
    sdk: {
      input: AddTagsCommandInput;
      output: AddTagsCommandOutput;
    };
  };
}
