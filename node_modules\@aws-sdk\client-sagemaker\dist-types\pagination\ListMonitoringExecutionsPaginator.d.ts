import { Paginator } from "@smithy/types";
import { ListMonitoringExecutionsCommandInput, ListMonitoringExecutionsCommandOutput } from "../commands/ListMonitoringExecutionsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListMonitoringExecutions: (config: SageMakerPaginationConfiguration, input: ListMonitoringExecutionsCommandInput, ...rest: any[]) => Paginator<ListMonitoringExecutionsCommandOutput>;
