import { M as Metric, T as TestInfo, E as EvaluationResult } from '../types-Bo1uigWx.cjs';
export { a as MetricResult } from '../types-Bo1uigWx.cjs';
import { r as Agent } from '../base-B96VvaWm.cjs';
import 'ai';
import '../base-aPYtPBT2.cjs';
import '@opentelemetry/api';
import '../logger-EhZkzZOr.cjs';
import 'stream';
import '@opentelemetry/sdk-trace-base';
import 'sift';
import 'zod';
import 'json-schema';
import '../deployer/index.cjs';
import '../bundler/index.cjs';
import 'node:http';
import 'hono';
import '../runtime-context/index.cjs';
import '../tts/index.cjs';
import '../vector/index.cjs';
import '../vector/filter/index.cjs';
import 'xstate';
import 'node:events';
import 'events';
import '../workflows/constants.cjs';
import 'hono/cors';
import 'hono-openapi';
import 'ai/test';

declare function evaluate<T extends Agent>({ agentName, input, metric, output, runId, globalRunId, testInfo, instructions, }: {
    agentName: string;
    input: Parameters<T['generate']>[0];
    metric: Metric;
    output: string;
    globalRunId: string;
    runId?: string;
    testInfo?: TestInfo;
    instructions: string;
}): Promise<EvaluationResult>;

export { EvaluationResult, Metric, TestInfo, evaluate };
