import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DeleteMlflowTrackingServerRequest,
  DeleteMlflowTrackingServerResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteMlflowTrackingServerCommandInput
  extends DeleteMlflowTrackingServerRequest {}
export interface DeleteMlflowTrackingServerCommandOutput
  extends DeleteMlflowTrackingServerResponse,
    __MetadataBearer {}
declare const DeleteMlflowTrackingServerCommand_base: {
  new (
    input: DeleteMlflowTrackingServerCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteMlflowTrackingServerCommandInput,
    DeleteMlflowTrackingServerCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteMlflowTrackingServerCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteMlflowTrackingServerCommandInput,
    DeleteMlflowTrackingServerCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteMlflowTrackingServerCommand extends DeleteMlflowTrackingServerCommand_base {
  protected static __types: {
    api: {
      input: DeleteMlflowTrackingServerRequest;
      output: DeleteMlflowTrackingServerResponse;
    };
    sdk: {
      input: DeleteMlflowTrackingServerCommandInput;
      output: DeleteMlflowTrackingServerCommandOutput;
    };
  };
}
