import { Paginator } from "@smithy/types";
import {
  ListNotebookInstancesCommandInput,
  ListNotebookInstancesCommandOutput,
} from "../commands/ListNotebookInstancesCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
export declare const paginateListNotebookInstances: (
  config: SageMakerPaginationConfiguration,
  input: ListNotebookInstancesCommandInput,
  ...rest: any[]
) => Paginator<ListNotebookInstancesCommandOutput>;
