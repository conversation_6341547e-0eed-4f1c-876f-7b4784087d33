import { Ionicons } from '@expo/vector-icons';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { NavigationContainer } from '@react-navigation/native';
import { StatusBar } from 'expo-status-bar';
import React from 'react';

// Screens
import CategoriesScreen from './src/screens/CategoriesScreen';
import ChatScreen from './src/screens/ChatScreen';
import FavoritesScreen from './src/screens/FavoritesScreen';
import HomeScreen from './src/screens/HomeScreen';
import ShoppingListScreen from './src/screens/ShoppingListScreen';

const Tab = createBottomTabNavigator();

export default function App() {
  return (
    <NavigationContainer>
      <StatusBar style="light" />
      <Tab.Navigator
        screenOptions={({ route }) => ({
          tabBarIcon: ({ focused, color, size }) => {
            let iconName: keyof typeof Ionicons.glyphMap;

            if (route.name === 'Ana Sayfa') {
              iconName = focused ? 'home' : 'home-outline';
            } else if (route.name === 'Kategoriler') {
              iconName = focused ? 'grid' : 'grid-outline';
            } else if (route.name === 'Chatbot') {
              iconName = focused ? 'chatbubbles' : 'chatbubbles-outline';
            } else if (route.name === 'Favoriler') {
              iconName = focused ? 'heart' : 'heart-outline';
            } else if (route.name === 'Alışveriş') {
              iconName = focused ? 'list' : 'list-outline';
            } else {
              iconName = 'home-outline';
            }

            return <Ionicons name={iconName} size={size} color={color} />;
          },
          tabBarActiveTintColor: '#FF6B35',
          tabBarInactiveTintColor: 'gray',
          tabBarStyle: {
            backgroundColor: 'white',
            borderTopWidth: 1,
            borderTopColor: '#e0e0e0',
            height: 60,
            paddingBottom: 5,
            paddingTop: 5,
          },
          headerStyle: {
            backgroundColor: '#FF6B35',
          },
          headerTintColor: 'white',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
        })}
      >
        <Tab.Screen
          name="Ana Sayfa"
          component={HomeScreen}
          options={{ title: 'Tarif Uygulaması' }}
        />
        <Tab.Screen
          name="Kategoriler"
          component={CategoriesScreen}
          options={{ title: 'Kategoriler' }}
        />
        <Tab.Screen
          name="Chatbot"
          component={ChatScreen}
          options={{ title: 'Tarif Asistanı' }}
        />
        <Tab.Screen
          name="Favoriler"
          component={FavoritesScreen}
          options={{ title: 'Favorilerim' }}
        />
        <Tab.Screen
          name="Alışveriş"
          component={ShoppingListScreen}
          options={{ title: 'Alışveriş Listesi' }}
        />
      </Tab.Navigator>
    </NavigationContainer>
  );
}
