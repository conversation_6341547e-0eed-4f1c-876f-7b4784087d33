import { Paginator } from "@smithy/types";
import {
  ListMonitoringAlertsCommandInput,
  ListMonitoringAlertsCommandOutput,
} from "../commands/ListMonitoringAlertsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
export declare const paginateListMonitoringAlerts: (
  config: SageMakerPaginationConfiguration,
  input: ListMonitoringAlertsCommandInput,
  ...rest: any[]
) => Paginator<ListMonitoringAlertsCommandOutput>;
