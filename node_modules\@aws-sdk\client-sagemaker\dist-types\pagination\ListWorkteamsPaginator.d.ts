import { Paginator } from "@smithy/types";
import { ListWorkteamsCommandInput, ListWorkteamsCommandOutput } from "../commands/ListWorkteamsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListWorkteams: (config: SageMakerPaginationConfiguration, input: ListWorkteamsCommandInput, ...rest: any[]) => Paginator<ListWorkteamsCommandOutput>;
