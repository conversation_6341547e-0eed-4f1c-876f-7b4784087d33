{"name": "recipe-chatbot", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-navigation/native": "^7.0.15", "@react-navigation/bottom-tabs": "^7.0.15", "@react-navigation/stack": "^7.0.15", "@react-native-async-storage/async-storage": "^2.1.0", "expo": "~53.0.9", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.2", "react-native-gifted-chat": "^2.8.1", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-safe-area-context": "^5.0.7", "react-native-screens": "^4.2.0", "react-native-gesture-handler": "^2.22.1", "react-dom": "19.0.0", "react-native-web": "^0.20.0", "@expo/metro-runtime": "~5.0.4"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}