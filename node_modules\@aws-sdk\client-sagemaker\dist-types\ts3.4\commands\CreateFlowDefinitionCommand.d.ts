import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateFlowDefinitionRequest,
  CreateFlowDefinitionResponse,
} from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateFlowDefinitionCommandInput
  extends CreateFlowDefinitionRequest {}
export interface CreateFlowDefinitionCommandOutput
  extends CreateFlowDefinitionResponse,
    __MetadataBearer {}
declare const CreateFlowDefinitionCommand_base: {
  new (
    input: CreateFlowDefinitionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateFlowDefinitionCommandInput,
    CreateFlowDefinitionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateFlowDefinitionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateFlowDefinitionCommandInput,
    CreateFlowDefinitionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateFlowDefinitionCommand extends CreateFlowDefinitionCommand_base {
  protected static __types: {
    api: {
      input: CreateFlowDefinitionRequest;
      output: CreateFlowDefinitionResponse;
    };
    sdk: {
      input: CreateFlowDefinitionCommandInput;
      output: CreateFlowDefinitionCommandOutput;
    };
  };
}
