import '../base-aPYtPBT2.cjs';
import { af as Workflow, a9 as ToolAction } from '../base-B96VvaWm.cjs';
import '@opentelemetry/api';
import '../logger-EhZkzZOr.cjs';
import 'stream';
import '@opentelemetry/sdk-trace-base';
import 'ai';
import '../types-Bo1uigWx.cjs';
import 'sift';
import 'zod';
import 'json-schema';
import '../deployer/index.cjs';
import '../bundler/index.cjs';
import 'node:http';
import 'hono';
import '../runtime-context/index.cjs';
import '../tts/index.cjs';
import '../vector/index.cjs';
import '../vector/filter/index.cjs';
import 'xstate';
import 'node:events';
import 'events';
import '../workflows/constants.cjs';
import 'hono/cors';
import 'hono-openapi';
import 'ai/test';

declare class Integration<ToolsParams = void, ApiClient = void> {
    name: string;
    private workflows;
    constructor();
    /**
     * Workflows
     */
    registerWorkflow(name: string, fn: Workflow): void;
    getWorkflows({ serialized }: {
        serialized?: boolean;
    }): Record<string, Workflow>;
    /**
     * TOOLS
     */
    getStaticTools(_params?: ToolsParams): Record<string, ToolAction<any, any, any>>;
    getTools(_params?: ToolsParams): Promise<Record<string, ToolAction<any, any, any>>>;
    getApiClient(): Promise<ApiClient>;
}

declare abstract class OpenAPIToolset {
    abstract readonly name: string;
    abstract readonly tools: Record<string, ToolAction<any, any, any>>;
    authType: string;
    constructor();
    protected get toolSchemas(): any;
    protected get toolDocumentations(): Record<string, {
        comment: string;
        doc?: string;
    }>;
    protected get baseClient(): any;
    getApiClient(): Promise<any>;
    protected _generateIntegrationTools<T>(): T;
}

export { Integration, OpenAPIToolset };
