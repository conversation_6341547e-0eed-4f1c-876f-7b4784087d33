import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateNotebookInstanceInput,
  CreateNotebookInstanceOutput,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateNotebookInstanceCommandInput
  extends CreateNotebookInstanceInput {}
export interface CreateNotebookInstanceCommandOutput
  extends CreateNotebookInstanceOutput,
    __MetadataBearer {}
declare const CreateNotebookInstanceCommand_base: {
  new (
    input: CreateNotebookInstanceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateNotebookInstanceCommandInput,
    CreateNotebookInstanceCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateNotebookInstanceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateNotebookInstanceCommandInput,
    CreateNotebookInstanceCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateNotebookInstanceCommand extends CreateNotebookInstanceCommand_base {
  protected static __types: {
    api: {
      input: CreateNotebookInstanceInput;
      output: CreateNotebookInstanceOutput;
    };
    sdk: {
      input: CreateNotebookInstanceCommandInput;
      output: CreateNotebookInstanceCommandOutput;
    };
  };
}
