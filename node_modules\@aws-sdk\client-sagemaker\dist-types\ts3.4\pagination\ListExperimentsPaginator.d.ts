import { Paginator } from "@smithy/types";
import {
  ListExperimentsCommandInput,
  ListExperimentsCommandOutput,
} from "../commands/ListExperimentsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
export declare const paginateListExperiments: (
  config: SageMakerPaginationConfiguration,
  input: ListExperimentsCommandInput,
  ...rest: any[]
) => Paginator<ListExperimentsCommandOutput>;
