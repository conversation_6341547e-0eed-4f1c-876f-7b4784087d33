import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  GetScalingConfigurationRecommendationRequest,
  GetScalingConfigurationRecommendationResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface GetScalingConfigurationRecommendationCommandInput
  extends GetScalingConfigurationRecommendationRequest {}
export interface GetScalingConfigurationRecommendationCommandOutput
  extends GetScalingConfigurationRecommendationResponse,
    __MetadataBearer {}
declare const GetScalingConfigurationRecommendationCommand_base: {
  new (
    input: GetScalingConfigurationRecommendationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetScalingConfigurationRecommendationCommandInput,
    GetScalingConfigurationRecommendationCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetScalingConfigurationRecommendationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetScalingConfigurationRecommendationCommandInput,
    GetScalingConfigurationRecommendationCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetScalingConfigurationRecommendationCommand extends GetScalingConfigurationRecommendationCommand_base {
  protected static __types: {
    api: {
      input: GetScalingConfigurationRecommendationRequest;
      output: GetScalingConfigurationRecommendationResponse;
    };
    sdk: {
      input: GetScalingConfigurationRecommendationCommandInput;
      output: GetScalingConfigurationRecommendationCommandOutput;
    };
  };
}
