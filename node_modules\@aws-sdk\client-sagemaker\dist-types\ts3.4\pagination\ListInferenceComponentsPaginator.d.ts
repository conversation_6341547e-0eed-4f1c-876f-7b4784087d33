import { Paginator } from "@smithy/types";
import {
  ListInferenceComponentsCommandInput,
  ListInferenceComponentsCommandOutput,
} from "../commands/ListInferenceComponentsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
export declare const paginateListInferenceComponents: (
  config: SageMakerPaginationConfiguration,
  input: ListInferenceComponentsCommandInput,
  ...rest: any[]
) => Paginator<ListInferenceComponentsCommandOutput>;
