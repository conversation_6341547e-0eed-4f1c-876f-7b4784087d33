import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { UpdateTrialRequest, UpdateTrialResponse } from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateTrialCommandInput extends UpdateTrialRequest {}
export interface UpdateTrialCommandOutput
  extends UpdateTrialResponse,
    __MetadataBearer {}
declare const UpdateTrialCommand_base: {
  new (
    input: UpdateTrialCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateTrialCommandInput,
    UpdateTrialCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateTrialCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateTrialCommandInput,
    UpdateTrialCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateTrialCommand extends UpdateTrialCommand_base {
  protected static __types: {
    api: {
      input: UpdateTrialRequest;
      output: UpdateTrialResponse;
    };
    sdk: {
      input: UpdateTrialCommandInput;
      output: UpdateTrialCommandOutput;
    };
  };
}
