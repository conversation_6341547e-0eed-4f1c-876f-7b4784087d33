import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  UpdateModelCardRequest,
  UpdateModelCardResponse,
} from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateModelCardCommandInput extends UpdateModelCardRequest {}
export interface UpdateModelCardCommandOutput
  extends UpdateModelCardResponse,
    __MetadataBearer {}
declare const UpdateModelCardCommand_base: {
  new (
    input: UpdateModelCardCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateModelCardCommandInput,
    UpdateModelCardCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateModelCardCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateModelCardCommandInput,
    UpdateModelCardCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateModelCardCommand extends UpdateModelCardCommand_base {
  protected static __types: {
    api: {
      input: UpdateModelCardRequest;
      output: UpdateModelCardResponse;
    };
    sdk: {
      input: UpdateModelCardCommandInput;
      output: UpdateModelCardCommandOutput;
    };
  };
}
