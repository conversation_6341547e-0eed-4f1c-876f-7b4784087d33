import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateClusterRequest,
  CreateClusterResponse,
} from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateClusterCommandInput extends CreateClusterRequest {}
export interface CreateClusterCommandOutput
  extends CreateClusterResponse,
    __MetadataBearer {}
declare const CreateClusterCommand_base: {
  new (
    input: CreateClusterCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateClusterCommandInput,
    CreateClusterCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateClusterCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateClusterCommandInput,
    CreateClusterCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateClusterCommand extends CreateClusterCommand_base {
  protected static __types: {
    api: {
      input: CreateClusterRequest;
      output: CreateClusterResponse;
    };
    sdk: {
      input: CreateClusterCommandInput;
      output: CreateClusterCommandOutput;
    };
  };
}
