import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreatePartnerAppPresignedUrlRequest,
  CreatePartnerAppPresignedUrlResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreatePartnerAppPresignedUrlCommandInput
  extends CreatePartnerAppPresignedUrlRequest {}
export interface CreatePartnerAppPresignedUrlCommandOutput
  extends CreatePartnerAppPresignedUrlResponse,
    __MetadataBearer {}
declare const CreatePartnerAppPresignedUrlCommand_base: {
  new (
    input: CreatePartnerAppPresignedUrlCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreatePartnerAppPresignedUrlCommandInput,
    CreatePartnerAppPresignedUrlCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreatePartnerAppPresignedUrlCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreatePartnerAppPresignedUrlCommandInput,
    CreatePartnerAppPresignedUrlCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreatePartnerAppPresignedUrlCommand extends CreatePartnerAppPresignedUrlCommand_base {
  protected static __types: {
    api: {
      input: CreatePartnerAppPresignedUrlRequest;
      output: CreatePartnerAppPresignedUrlResponse;
    };
    sdk: {
      input: CreatePartnerAppPresignedUrlCommandInput;
      output: CreatePartnerAppPresignedUrlCommandOutput;
    };
  };
}
