import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  RetryPipelineExecutionRequest,
  RetryPipelineExecutionResponse,
} from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface RetryPipelineExecutionCommandInput
  extends RetryPipelineExecutionRequest {}
export interface RetryPipelineExecutionCommandOutput
  extends RetryPipelineExecutionResponse,
    __MetadataBearer {}
declare const RetryPipelineExecutionCommand_base: {
  new (
    input: RetryPipelineExecutionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    RetryPipelineExecutionCommandInput,
    RetryPipelineExecutionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: RetryPipelineExecutionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    RetryPipelineExecutionCommandInput,
    RetryPipelineExecutionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class RetryPipelineExecutionCommand extends RetryPipelineExecutionCommand_base {
  protected static __types: {
    api: {
      input: RetryPipelineExecutionRequest;
      output: RetryPipelineExecutionResponse;
    };
    sdk: {
      input: RetryPipelineExecutionCommandInput;
      output: RetryPipelineExecutionCommandOutput;
    };
  };
}
