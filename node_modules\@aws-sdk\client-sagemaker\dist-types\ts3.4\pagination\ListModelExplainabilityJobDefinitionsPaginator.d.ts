import { Paginator } from "@smithy/types";
import {
  ListModelExplainabilityJobDefinitionsCommandInput,
  ListModelExplainabilityJobDefinitionsCommandOutput,
} from "../commands/ListModelExplainabilityJobDefinitionsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
export declare const paginateListModelExplainabilityJobDefinitions: (
  config: SageMakerPaginationConfiguration,
  input: ListModelExplainabilityJobDefinitionsCommandInput,
  ...rest: any[]
) => Paginator<ListModelExplainabilityJobDefinitionsCommandOutput>;
