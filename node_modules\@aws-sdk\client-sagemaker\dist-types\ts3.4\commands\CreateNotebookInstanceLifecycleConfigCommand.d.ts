import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateNotebookInstanceLifecycleConfigInput,
  CreateNotebookInstanceLifecycleConfigOutput,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateNotebookInstanceLifecycleConfigCommandInput
  extends CreateNotebookInstanceLifecycleConfigInput {}
export interface CreateNotebookInstanceLifecycleConfigCommandOutput
  extends CreateNotebookInstanceLifecycleConfigOutput,
    __MetadataBearer {}
declare const CreateNotebookInstanceLifecycleConfigCommand_base: {
  new (
    input: CreateNotebookInstanceLifecycleConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateNotebookInstanceLifecycleConfigCommandInput,
    CreateNotebookInstanceLifecycleConfigCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateNotebookInstanceLifecycleConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateNotebookInstanceLifecycleConfigCommandInput,
    CreateNotebookInstanceLifecycleConfigCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateNotebookInstanceLifecycleConfigCommand extends CreateNotebookInstanceLifecycleConfigCommand_base {
  protected static __types: {
    api: {
      input: CreateNotebookInstanceLifecycleConfigInput;
      output: CreateNotebookInstanceLifecycleConfigOutput;
    };
    sdk: {
      input: CreateNotebookInstanceLifecycleConfigCommandInput;
      output: CreateNotebookInstanceLifecycleConfigCommandOutput;
    };
  };
}
