import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { ListAliasesRequest, ListAliasesResponse } from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface ListAliasesCommandInput extends ListAliasesRequest {}
export interface ListAliasesCommandOutput
  extends ListAliasesResponse,
    __MetadataBearer {}
declare const ListAliasesCommand_base: {
  new (
    input: ListAliasesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListAliasesCommandInput,
    ListAliasesCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: ListAliasesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListAliasesCommandInput,
    ListAliasesCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListAliasesCommand extends ListAliasesCommand_base {
  protected static __types: {
    api: {
      input: ListAliasesRequest;
      output: ListAliasesResponse;
    };
    sdk: {
      input: ListAliasesCommandInput;
      output: ListAliasesCommandOutput;
    };
  };
}
