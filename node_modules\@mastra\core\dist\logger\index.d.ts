import { I as IMastraLogger, L as LoggerTransport, a as LogLevel, M as MastraLogger } from '../logger-EhZkzZOr.js';
export { B as BaseLogMessage, R as RegisteredLogger } from '../logger-EhZkzZOr.js';
import 'stream';

declare class MultiLogger implements IMastraLogger {
    private loggers;
    constructor(loggers: IMastraLogger[]);
    debug(message: string, ...args: any[]): void;
    info(message: string, ...args: any[]): void;
    warn(message: string, ...args: any[]): void;
    error(message: string, ...args: any[]): void;
    getTransports(): Map<string, LoggerTransport>;
    getLogs(transportId: string): Promise<any[]>;
    getLogsByRunId(args: {
        transportId: string;
        runId: string;
    }): Promise<any[]>;
}

declare const noopLogger: IMastraLogger;

declare const createLogger: (options: {
    name?: string;
    level?: LogLevel;
    transports?: Record<string, LoggerTransport>;
}) => ConsoleLogger;
declare class ConsoleLogger extends MastraLogger {
    constructor(options?: {
        name?: string;
        level?: LogLevel;
    });
    debug(message: string, ...args: any[]): void;
    info(message: string, ...args: any[]): void;
    warn(message: string, ...args: any[]): void;
    error(message: string, ...args: any[]): void;
    getLogs(_transportId: string): Promise<never[]>;
    getLogsByRunId(_args: {
        transportId: string;
        runId: string;
    }): Promise<never[]>;
}

export { ConsoleLogger, IMastraLogger, LogLevel, LoggerTransport, MastraLogger, MultiLogger, createLogger, noopLogger };
