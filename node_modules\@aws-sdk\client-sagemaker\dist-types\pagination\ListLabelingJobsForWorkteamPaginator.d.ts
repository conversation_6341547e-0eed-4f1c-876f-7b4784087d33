import { Paginator } from "@smithy/types";
import { ListLabelingJobsForWorkteamCommandInput, ListLabelingJobsForWorkteamCommandOutput } from "../commands/ListLabelingJobsForWorkteamCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListLabelingJobsForWorkteam: (config: SageMakerPaginationConfiguration, input: ListLabelingJobsForWorkteamCommandInput, ...rest: any[]) => Paginator<ListLabelingJobsForWorkteamCommandOutput>;
