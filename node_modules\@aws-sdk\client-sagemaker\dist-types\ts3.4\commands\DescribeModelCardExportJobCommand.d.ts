import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeModelCardExportJobRequest,
  DescribeModelCardExportJobResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeModelCardExportJobCommandInput
  extends DescribeModelCardExportJobRequest {}
export interface DescribeModelCardExportJobCommandOutput
  extends DescribeModelCardExportJobResponse,
    __MetadataBearer {}
declare const DescribeModelCardExportJobCommand_base: {
  new (
    input: DescribeModelCardExportJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeModelCardExportJobCommandInput,
    DescribeModelCardExportJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeModelCardExportJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeModelCardExportJobCommandInput,
    DescribeModelCardExportJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeModelCardExportJobCommand extends DescribeModelCardExportJobCommand_base {
  protected static __types: {
    api: {
      input: DescribeModelCardExportJobRequest;
      output: DescribeModelCardExportJobResponse;
    };
    sdk: {
      input: DescribeModelCardExportJobCommandInput;
      output: DescribeModelCardExportJobCommandOutput;
    };
  };
}
