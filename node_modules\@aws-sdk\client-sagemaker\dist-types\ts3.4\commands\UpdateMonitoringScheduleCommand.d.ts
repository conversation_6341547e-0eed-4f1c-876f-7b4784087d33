import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  UpdateMonitoringScheduleRequest,
  UpdateMonitoringScheduleResponse,
} from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateMonitoringScheduleCommandInput
  extends UpdateMonitoringScheduleRequest {}
export interface UpdateMonitoringScheduleCommandOutput
  extends UpdateMonitoringScheduleResponse,
    __MetadataBearer {}
declare const UpdateMonitoringScheduleCommand_base: {
  new (
    input: UpdateMonitoringScheduleCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateMonitoringScheduleCommandInput,
    UpdateMonitoringScheduleCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateMonitoringScheduleCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateMonitoringScheduleCommandInput,
    UpdateMonitoringScheduleCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateMonitoringScheduleCommand extends UpdateMonitoringScheduleCommand_base {
  protected static __types: {
    api: {
      input: UpdateMonitoringScheduleRequest;
      output: UpdateMonitoringScheduleResponse;
    };
    sdk: {
      input: UpdateMonitoringScheduleCommandInput;
      output: UpdateMonitoringScheduleCommandOutput;
    };
  };
}
