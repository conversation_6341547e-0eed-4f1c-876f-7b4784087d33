import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  UpdateModelPackageInput,
  UpdateModelPackageOutput,
} from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateModelPackageCommandInput
  extends UpdateModelPackageInput {}
export interface UpdateModelPackageCommandOutput
  extends UpdateModelPackageOutput,
    __MetadataBearer {}
declare const UpdateModelPackageCommand_base: {
  new (
    input: UpdateModelPackageCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateModelPackageCommandInput,
    UpdateModelPackageCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateModelPackageCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateModelPackageCommandInput,
    UpdateModelPackageCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateModelPackageCommand extends UpdateModelPackageCommand_base {
  protected static __types: {
    api: {
      input: UpdateModelPackageInput;
      output: UpdateModelPackageOutput;
    };
    sdk: {
      input: UpdateModelPackageCommandInput;
      output: UpdateModelPackageCommandOutput;
    };
  };
}
