import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ListMonitoringAlertHistoryRequest,
  ListMonitoringAlertHistoryResponse,
} from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface ListMonitoringAlertHistoryCommandInput
  extends ListMonitoringAlertHistoryRequest {}
export interface ListMonitoringAlertHistoryCommandOutput
  extends ListMonitoringAlertHistoryResponse,
    __MetadataBearer {}
declare const ListMonitoringAlertHistoryCommand_base: {
  new (
    input: ListMonitoringAlertHistoryCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListMonitoringAlertHistoryCommandInput,
    ListMonitoringAlertHistoryCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    ...[input]: [] | [ListMonitoringAlertHistoryCommandInput]
  ): import("@smithy/smithy-client").CommandImpl<
    ListMonitoringAlertHistoryCommandInput,
    ListMonitoringAlertHistoryCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListMonitoringAlertHistoryCommand extends ListMonitoringAlertHistoryCommand_base {
  protected static __types: {
    api: {
      input: ListMonitoringAlertHistoryRequest;
      output: ListMonitoringAlertHistoryResponse;
    };
    sdk: {
      input: ListMonitoringAlertHistoryCommandInput;
      output: ListMonitoringAlertHistoryCommandOutput;
    };
  };
}
