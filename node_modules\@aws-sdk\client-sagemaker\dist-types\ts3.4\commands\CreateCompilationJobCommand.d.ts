import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateCompilationJobRequest,
  CreateCompilationJobResponse,
} from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateCompilationJobCommandInput
  extends CreateCompilationJobRequest {}
export interface CreateCompilationJobCommandOutput
  extends CreateCompilationJobResponse,
    __MetadataBearer {}
declare const CreateCompilationJobCommand_base: {
  new (
    input: CreateCompilationJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateCompilationJobCommandInput,
    CreateCompilationJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateCompilationJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateCompilationJobCommandInput,
    CreateCompilationJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateCompilationJobCommand extends CreateCompilationJobCommand_base {
  protected static __types: {
    api: {
      input: CreateCompilationJobRequest;
      output: CreateCompilationJobResponse;
    };
    sdk: {
      input: CreateCompilationJobCommandInput;
      output: CreateCompilationJobCommandOutput;
    };
  };
}
