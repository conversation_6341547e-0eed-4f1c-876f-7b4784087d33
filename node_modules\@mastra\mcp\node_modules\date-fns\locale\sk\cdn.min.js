(()=>{var V;function W(B,C){var J=Object.keys(B);if(Object.getOwnPropertySymbols){var X=Object.getOwnPropertySymbols(B);C&&(X=X.filter(function(Z){return Object.getOwnPropertyDescriptor(B,Z).enumerable})),J.push.apply(J,X)}return J}function T(B){for(var C=1;C<arguments.length;C++){var J=arguments[C]!=null?arguments[C]:{};C%2?W(Object(J),!0).forEach(function(X){P(B,X,J[X])}):Object.getOwnPropertyDescriptors?Object.defineProperties(B,Object.getOwnPropertyDescriptors(J)):W(Object(J)).forEach(function(X){Object.defineProperty(B,X,Object.getOwnPropertyDescriptor(J,X))})}return B}function P(B,C,J){if(C=v(C),C in B)Object.defineProperty(B,C,{value:J,enumerable:!0,configurable:!0,writable:!0});else B[C]=J;return B}function v(B){var C=b(B,"string");return K(C)=="symbol"?C:String(C)}function b(B,C){if(K(B)!="object"||!B)return B;var J=B[Symbol.toPrimitive];if(J!==void 0){var X=J.call(B,C||"default");if(K(X)!="object")return X;throw new TypeError("@@toPrimitive must return a primitive value.")}return(C==="string"?String:Number)(B)}function h(B,C){return g(B)||_(B,C)||f(B,C)||k()}function k(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function f(B,C){if(!B)return;if(typeof B==="string")return $(B,C);var J=Object.prototype.toString.call(B).slice(8,-1);if(J==="Object"&&B.constructor)J=B.constructor.name;if(J==="Map"||J==="Set")return Array.from(B);if(J==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(J))return $(B,C)}function $(B,C){if(C==null||C>B.length)C=B.length;for(var J=0,X=new Array(C);J<C;J++)X[J]=B[J];return X}function _(B,C){var J=B==null?null:typeof Symbol!="undefined"&&B[Symbol.iterator]||B["@@iterator"];if(J!=null){var X,Z,G,U,H=[],Y=!0,Q=!1;try{if(G=(J=J.call(B)).next,C===0){if(Object(J)!==J)return;Y=!1}else for(;!(Y=(X=G.call(J)).done)&&(H.push(X.value),H.length!==C);Y=!0);}catch(q){Q=!0,Z=q}finally{try{if(!Y&&J.return!=null&&(U=J.return(),Object(U)!==U))return}finally{if(Q)throw Z}}return H}}function g(B){if(Array.isArray(B))return B}function K(B){return K=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(C){return typeof C}:function(C){return C&&typeof Symbol=="function"&&C.constructor===Symbol&&C!==Symbol.prototype?"symbol":typeof C},K(B)}var m=Object.defineProperty,hB=function B(C,J){for(var X in J)m(C,X,{get:J[X],enumerable:!0,configurable:!0,set:function Z(G){return J[X]=function(){return G}}})};function y(B,C){if(C===1&&B.one)return B.one;if(C>=2&&C<=4&&B.twoFour)return B.twoFour;return B.other}function I(B,C,J){var X=y(B,C),Z=X[J];return Z.replace("{{count}}",String(C))}function c(B){var C=["lessThan","about","over","almost"].filter(function(J){return!!B.match(new RegExp("^"+J))});return C[0]}function z(B){var C="";if(B==="almost")C="takmer";if(B==="about")C="pribli\u017Ene";return C.length>0?C+" ":""}function M(B){var C="";if(B==="lessThan")C="menej ne\u017E";if(B==="over")C="viac ne\u017E";return C.length>0?C+" ":""}function u(B){return B.charAt(0).toLowerCase()+B.slice(1)}var d={xSeconds:{one:{present:"sekunda",past:"sekundou",future:"sekundu"},twoFour:{present:"{{count}} sekundy",past:"{{count}} sekundami",future:"{{count}} sekundy"},other:{present:"{{count}} sek\xFAnd",past:"{{count}} sekundami",future:"{{count}} sek\xFAnd"}},halfAMinute:{other:{present:"pol min\xFAty",past:"pol min\xFAtou",future:"pol min\xFAty"}},xMinutes:{one:{present:"min\xFAta",past:"min\xFAtou",future:"min\xFAtu"},twoFour:{present:"{{count}} min\xFAty",past:"{{count}} min\xFAtami",future:"{{count}} min\xFAty"},other:{present:"{{count}} min\xFAt",past:"{{count}} min\xFAtami",future:"{{count}} min\xFAt"}},xHours:{one:{present:"hodina",past:"hodinou",future:"hodinu"},twoFour:{present:"{{count}} hodiny",past:"{{count}} hodinami",future:"{{count}} hodiny"},other:{present:"{{count}} hod\xEDn",past:"{{count}} hodinami",future:"{{count}} hod\xEDn"}},xDays:{one:{present:"de\u0148",past:"d\u0148om",future:"de\u0148"},twoFour:{present:"{{count}} dni",past:"{{count}} d\u0148ami",future:"{{count}} dni"},other:{present:"{{count}} dn\xED",past:"{{count}} d\u0148ami",future:"{{count}} dn\xED"}},xWeeks:{one:{present:"t\xFD\u017Ede\u0148",past:"t\xFD\u017Ed\u0148om",future:"t\xFD\u017Ede\u0148"},twoFour:{present:"{{count}} t\xFD\u017Edne",past:"{{count}} t\xFD\u017Ed\u0148ami",future:"{{count}} t\xFD\u017Edne"},other:{present:"{{count}} t\xFD\u017Ed\u0148ov",past:"{{count}} t\xFD\u017Ed\u0148ami",future:"{{count}} t\xFD\u017Ed\u0148ov"}},xMonths:{one:{present:"mesiac",past:"mesiacom",future:"mesiac"},twoFour:{present:"{{count}} mesiace",past:"{{count}} mesiacmi",future:"{{count}} mesiace"},other:{present:"{{count}} mesiacov",past:"{{count}} mesiacmi",future:"{{count}} mesiacov"}},xYears:{one:{present:"rok",past:"rokom",future:"rok"},twoFour:{present:"{{count}} roky",past:"{{count}} rokmi",future:"{{count}} roky"},other:{present:"{{count}} rokov",past:"{{count}} rokmi",future:"{{count}} rokov"}}},l=function B(C,J,X){var Z=c(C)||"",G=u(C.substring(Z.length)),U=d[G];if(!(X!==null&&X!==void 0&&X.addSuffix))return z(Z)+M(Z)+I(U,J,"present");if(X.comparison&&X.comparison>0)return z(Z)+"o "+M(Z)+I(U,J,"future");else return z(Z)+"pred "+M(Z)+I(U,J,"past")};function R(B){return function(){var C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},J=C.width?String(C.width):B.defaultWidth,X=B.formats[J]||B.formats[B.defaultWidth];return X}}var p={full:"EEEE d. MMMM y",long:"d. MMMM y",medium:"d. M. y",short:"d. M. y"},i={full:"H:mm:ss zzzz",long:"H:mm:ss z",medium:"H:mm:ss",short:"H:mm"},s={full:"{{date}}, {{time}}",long:"{{date}}, {{time}}",medium:"{{date}}, {{time}}",short:"{{date}} {{time}}"},n={date:R({formats:p,defaultWidth:"full"}),time:R({formats:i,defaultWidth:"full"}),dateTime:R({formats:s,defaultWidth:"full"})},kB=7,r=365.2425,o=Math.pow(10,8)*24*60*60*1000,fB=-o,_B=604800000,gB=86400000,mB=60000,yB=3600000,cB=1000,uB=525600,dB=43200,lB=1440,pB=60,iB=3,sB=12,nB=4,a=3600,rB=60,j=a*24,oB=j*7,t=j*r,e=t/12,aB=e*3,x=Symbol.for("constructDateFrom");function L(B,C){if(typeof B==="function")return B(C);if(B&&K(B)==="object"&&x in B)return B[x](C);if(B instanceof Date)return new B.constructor(C);return new Date(C)}function BB(B){for(var C=arguments.length,J=new Array(C>1?C-1:0),X=1;X<C;X++)J[X-1]=arguments[X];var Z=L.bind(null,B||J.find(function(G){return K(G)==="object"}));return J.map(Z)}function CB(){return O}function tB(B){O=B}var O={};function JB(B,C){return L(C||B,B)}function D(B,C){var J,X,Z,G,U,H,Y=CB(),Q=(J=(X=(Z=(G=C===null||C===void 0?void 0:C.weekStartsOn)!==null&&G!==void 0?G:C===null||C===void 0||(U=C.locale)===null||U===void 0||(U=U.options)===null||U===void 0?void 0:U.weekStartsOn)!==null&&Z!==void 0?Z:Y.weekStartsOn)!==null&&X!==void 0?X:(H=Y.locale)===null||H===void 0||(H=H.options)===null||H===void 0?void 0:H.weekStartsOn)!==null&&J!==void 0?J:0,q=JB(B,C===null||C===void 0?void 0:C.in),E=q.getDay(),bB=(E<Q?7:0)+E-Q;return q.setDate(q.getDate()-bB),q.setHours(0,0,0,0),q}function F(B,C,J){var X=BB(J===null||J===void 0?void 0:J.in,B,C),Z=h(X,2),G=Z[0],U=Z[1];return+D(G,J)===+D(U,J)}function XB(B){var C=S[B];switch(B){case 0:case 3:case 6:return"'minul\xFA "+C+" o' p";default:return"'minul\xFD' eeee 'o' p"}}function w(B){var C=S[B];if(B===4)return"'vo' eeee 'o' p";else return"'v "+C+" o' p"}function ZB(B){var C=S[B];switch(B){case 0:case 4:case 6:return"'bud\xFAcu "+C+" o' p";default:return"'bud\xFAci' eeee 'o' p"}}var S=["nede\u013Eu","pondelok","utorok","stredu","\u0161tvrtok","piatok","sobotu"],GB={lastWeek:function B(C,J,X){var Z=C.getDay();if(F(C,J,X))return w(Z);else return XB(Z)},yesterday:"'v\u010Dera o' p",today:"'dnes o' p",tomorrow:"'zajtra o' p",nextWeek:function B(C,J,X){var Z=C.getDay();if(F(C,J,X))return w(Z);else return ZB(Z)},other:"P"},UB=function B(C,J,X,Z){var G=GB[C];if(typeof G==="function")return G(J,X,Z);return G};function N(B){return function(C,J){var X=J!==null&&J!==void 0&&J.context?String(J.context):"standalone",Z;if(X==="formatting"&&B.formattingValues){var G=B.defaultFormattingWidth||B.defaultWidth,U=J!==null&&J!==void 0&&J.width?String(J.width):G;Z=B.formattingValues[U]||B.formattingValues[G]}else{var H=B.defaultWidth,Y=J!==null&&J!==void 0&&J.width?String(J.width):B.defaultWidth;Z=B.values[Y]||B.values[H]}var Q=B.argumentCallback?B.argumentCallback(C):C;return Z[Q]}}var HB={narrow:["pred Kr.","po Kr."],abbreviated:["pred Kr.","po Kr."],wide:["pred Kristom","po Kristovi"]},QB={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1. \u0161tvr\u0165rok","2. \u0161tvr\u0165rok","3. \u0161tvr\u0165rok","4. \u0161tvr\u0165rok"]},YB={narrow:["j","f","m","a","m","j","j","a","s","o","n","d"],abbreviated:["jan","feb","mar","apr","m\xE1j","j\xFAn","j\xFAl","aug","sep","okt","nov","dec"],wide:["janu\xE1r","febru\xE1r","marec","apr\xEDl","m\xE1j","j\xFAn","j\xFAl","august","september","okt\xF3ber","november","december"]},qB={narrow:["j","f","m","a","m","j","j","a","s","o","n","d"],abbreviated:["jan","feb","mar","apr","m\xE1j","j\xFAn","j\xFAl","aug","sep","okt","nov","dec"],wide:["janu\xE1ra","febru\xE1ra","marca","apr\xEDla","m\xE1ja","j\xFAna","j\xFAla","augusta","septembra","okt\xF3bra","novembra","decembra"]},KB={narrow:["n","p","u","s","\u0161","p","s"],short:["ne","po","ut","st","\u0161t","pi","so"],abbreviated:["ne","po","ut","st","\u0161t","pi","so"],wide:["nede\u013Ea","pondelok","utorok","streda","\u0161tvrtok","piatok","sobota"]},EB={narrow:{am:"AM",pm:"PM",midnight:"poln.",noon:"pol.",morning:"r\xE1no",afternoon:"pop.",evening:"ve\u010D.",night:"noc"},abbreviated:{am:"AM",pm:"PM",midnight:"poln.",noon:"pol.",morning:"r\xE1no",afternoon:"popol.",evening:"ve\u010Der",night:"noc"},wide:{am:"AM",pm:"PM",midnight:"polnoc",noon:"poludnie",morning:"r\xE1no",afternoon:"popoludnie",evening:"ve\u010Der",night:"noc"}},NB={narrow:{am:"AM",pm:"PM",midnight:"o poln.",noon:"nap.",morning:"r\xE1no",afternoon:"pop.",evening:"ve\u010D.",night:"v n."},abbreviated:{am:"AM",pm:"PM",midnight:"o poln.",noon:"napol.",morning:"r\xE1no",afternoon:"popol.",evening:"ve\u010Der",night:"v noci"},wide:{am:"AM",pm:"PM",midnight:"o polnoci",noon:"napoludnie",morning:"r\xE1no",afternoon:"popoludn\xED",evening:"ve\u010Der",night:"v noci"}},AB=function B(C,J){var X=Number(C);return X+"."},TB={ordinalNumber:AB,era:N({values:HB,defaultWidth:"wide"}),quarter:N({values:QB,defaultWidth:"wide",argumentCallback:function B(C){return C-1}}),month:N({values:YB,defaultWidth:"wide",formattingValues:qB,defaultFormattingWidth:"wide"}),day:N({values:KB,defaultWidth:"wide"}),dayPeriod:N({values:EB,defaultWidth:"wide",formattingValues:NB,defaultFormattingWidth:"wide"})};function A(B){return function(C){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=J.width,Z=X&&B.matchPatterns[X]||B.matchPatterns[B.defaultMatchWidth],G=C.match(Z);if(!G)return null;var U=G[0],H=X&&B.parsePatterns[X]||B.parsePatterns[B.defaultParseWidth],Y=Array.isArray(H)?IB(H,function(E){return E.test(U)}):VB(H,function(E){return E.test(U)}),Q;Q=B.valueCallback?B.valueCallback(Y):Y,Q=J.valueCallback?J.valueCallback(Q):Q;var q=C.slice(U.length);return{value:Q,rest:q}}}function VB(B,C){for(var J in B)if(Object.prototype.hasOwnProperty.call(B,J)&&C(B[J]))return J;return}function IB(B,C){for(var J=0;J<B.length;J++)if(C(B[J]))return J;return}function zB(B){return function(C){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=C.match(B.matchPattern);if(!X)return null;var Z=X[0],G=C.match(B.parsePattern);if(!G)return null;var U=B.valueCallback?B.valueCallback(G[0]):G[0];U=J.valueCallback?J.valueCallback(U):U;var H=C.slice(Z.length);return{value:U,rest:H}}}var MB=/^(\d+)\.?/i,RB=/\d+/i,SB={narrow:/^(pred Kr\.|pred n\. l\.|po Kr\.|n\. l\.)/i,abbreviated:/^(pred Kr\.|pred n\. l\.|po Kr\.|n\. l\.)/i,wide:/^(pred Kristom|pred na[šs][íi]m letopo[čc]tom|po Kristovi|n[áa][šs]ho letopo[čc]tu)/i},WB={any:[/^pr/i,/^(po|n)/i]},$B={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234]\. [šs]tvr[ťt]rok/i},jB={any:[/1/i,/2/i,/3/i,/4/i]},xB={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|m[áa]j|j[úu]n|j[úu]l|aug|sep|okt|nov|dec)/i,wide:/^(janu[áa]ra?|febru[áa]ra?|(marec|marca)|apr[íi]la?|m[áa]ja?|j[úu]na?|j[úu]la?|augusta?|(september|septembra)|(okt[óo]ber|okt[óo]bra)|(november|novembra)|(december|decembra))/i},LB={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^m[áa]j/i,/^j[úu]n/i,/^j[úu]l/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},OB={narrow:/^[npusšp]/i,short:/^(ne|po|ut|st|št|pi|so)/i,abbreviated:/^(ne|po|ut|st|št|pi|so)/i,wide:/^(nede[ľl]a|pondelok|utorok|streda|[šs]tvrtok|piatok|sobota])/i},DB={narrow:[/^n/i,/^p/i,/^u/i,/^s/i,/^š/i,/^p/i,/^s/i],any:[/^n/i,/^po/i,/^u/i,/^st/i,/^(št|stv)/i,/^pi/i,/^so/i]},FB={narrow:/^(am|pm|(o )?poln\.?|(nap\.?|pol\.?)|r[áa]no|pop\.?|ve[čc]\.?|(v n\.?|noc))/i,abbreviated:/^(am|pm|(o )?poln\.?|(napol\.?|pol\.?)|r[áa]no|pop\.?|ve[čc]er|(v )?noci?)/i,any:/^(am|pm|(o )?polnoci?|(na)?poludnie|r[áa]no|popoludn(ie|í|i)|ve[čc]er|(v )?noci?)/i},wB={any:{am:/^am/i,pm:/^pm/i,midnight:/poln/i,noon:/^(nap|(na)?pol(\.|u))/i,morning:/^r[áa]no/i,afternoon:/^pop/i,evening:/^ve[čc]/i,night:/^(noc|v n\.)/i}},PB={ordinalNumber:zB({matchPattern:MB,parsePattern:RB,valueCallback:function B(C){return parseInt(C,10)}}),era:A({matchPatterns:SB,defaultMatchWidth:"wide",parsePatterns:WB,defaultParseWidth:"any"}),quarter:A({matchPatterns:$B,defaultMatchWidth:"wide",parsePatterns:jB,defaultParseWidth:"any",valueCallback:function B(C){return C+1}}),month:A({matchPatterns:xB,defaultMatchWidth:"wide",parsePatterns:LB,defaultParseWidth:"any"}),day:A({matchPatterns:OB,defaultMatchWidth:"wide",parsePatterns:DB,defaultParseWidth:"any"}),dayPeriod:A({matchPatterns:FB,defaultMatchWidth:"any",parsePatterns:wB,defaultParseWidth:"any"})},vB={code:"sk",formatDistance:l,formatLong:n,formatRelative:UB,localize:TB,match:PB,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=T(T({},window.dateFns),{},{locale:T(T({},(V=window.dateFns)===null||V===void 0?void 0:V.locale),{},{sk:vB})})})();

//# debugId=AFCDD8983AA3EE5F64756E2164756E21
