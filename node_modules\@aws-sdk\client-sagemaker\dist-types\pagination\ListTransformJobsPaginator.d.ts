import { Paginator } from "@smithy/types";
import { ListTransformJobsCommandInput, ListTransformJobsCommandOutput } from "../commands/ListTransformJobsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListTransformJobs: (config: SageMakerPaginationConfiguration, input: ListTransformJobsCommandInput, ...rest: any[]) => Paginator<ListTransformJobsCommandOutput>;
