import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeInferenceExperimentRequest,
  DescribeInferenceExperimentResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeInferenceExperimentCommandInput
  extends DescribeInferenceExperimentRequest {}
export interface DescribeInferenceExperimentCommandOutput
  extends DescribeInferenceExperimentResponse,
    __MetadataBearer {}
declare const DescribeInferenceExperimentCommand_base: {
  new (
    input: DescribeInferenceExperimentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeInferenceExperimentCommandInput,
    DescribeInferenceExperimentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeInferenceExperimentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeInferenceExperimentCommandInput,
    DescribeInferenceExperimentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeInferenceExperimentCommand extends DescribeInferenceExperimentCommand_base {
  protected static __types: {
    api: {
      input: DescribeInferenceExperimentRequest;
      output: DescribeInferenceExperimentResponse;
    };
    sdk: {
      input: DescribeInferenceExperimentCommandInput;
      output: DescribeInferenceExperimentCommandOutput;
    };
  };
}
