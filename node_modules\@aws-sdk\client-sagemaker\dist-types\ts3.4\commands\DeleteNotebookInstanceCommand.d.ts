import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DeleteNotebookInstanceInput } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteNotebookInstanceCommandInput
  extends DeleteNotebookInstanceInput {}
export interface DeleteNotebookInstanceCommandOutput extends __MetadataBearer {}
declare const DeleteNotebookInstanceCommand_base: {
  new (
    input: DeleteNotebookInstanceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteNotebookInstanceCommandInput,
    DeleteNotebookInstanceCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteNotebookInstanceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteNotebookInstanceCommandInput,
    DeleteNotebookInstanceCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteNotebookInstanceCommand extends DeleteNotebookInstanceCommand_base {
  protected static __types: {
    api: {
      input: DeleteNotebookInstanceInput;
      output: {};
    };
    sdk: {
      input: DeleteNotebookInstanceCommandInput;
      output: DeleteNotebookInstanceCommandOutput;
    };
  };
}
