import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeClusterSchedulerConfigRequest,
  DescribeClusterSchedulerConfigResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeClusterSchedulerConfigCommandInput
  extends DescribeClusterSchedulerConfigRequest {}
export interface DescribeClusterSchedulerConfigCommandOutput
  extends DescribeClusterSchedulerConfigResponse,
    __MetadataBearer {}
declare const DescribeClusterSchedulerConfigCommand_base: {
  new (
    input: DescribeClusterSchedulerConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeClusterSchedulerConfigCommandInput,
    DescribeClusterSchedulerConfigCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeClusterSchedulerConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeClusterSchedulerConfigCommandInput,
    DescribeClusterSchedulerConfigCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeClusterSchedulerConfigCommand extends DescribeClusterSchedulerConfigCommand_base {
  protected static __types: {
    api: {
      input: DescribeClusterSchedulerConfigRequest;
      output: DescribeClusterSchedulerConfigResponse;
    };
    sdk: {
      input: DescribeClusterSchedulerConfigCommandInput;
      output: DescribeClusterSchedulerConfigCommandOutput;
    };
  };
}
