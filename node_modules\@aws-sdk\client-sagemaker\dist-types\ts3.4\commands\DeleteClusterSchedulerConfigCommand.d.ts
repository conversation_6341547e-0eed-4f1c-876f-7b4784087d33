import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { DeleteClusterSchedulerConfigRequest } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteClusterSchedulerConfigCommandInput
  extends DeleteClusterSchedulerConfigRequest {}
export interface DeleteClusterSchedulerConfigCommandOutput
  extends __MetadataBearer {}
declare const DeleteClusterSchedulerConfigCommand_base: {
  new (
    input: DeleteClusterSchedulerConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteClusterSchedulerConfigCommandInput,
    DeleteClusterSchedulerConfigCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteClusterSchedulerConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteClusterSchedulerConfigCommandInput,
    DeleteClusterSchedulerConfigCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteClusterSchedulerConfigCommand extends DeleteClusterSchedulerConfigCommand_base {
  protected static __types: {
    api: {
      input: DeleteClusterSchedulerConfigRequest;
      output: {};
    };
    sdk: {
      input: DeleteClusterSchedulerConfigCommandInput;
      output: DeleteClusterSchedulerConfigCommandOutput;
    };
  };
}
