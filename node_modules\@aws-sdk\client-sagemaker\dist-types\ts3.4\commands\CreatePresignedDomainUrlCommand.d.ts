import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreatePresignedDomainUrlRequest,
  CreatePresignedDomainUrlResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreatePresignedDomainUrlCommandInput
  extends CreatePresignedDomainUrlRequest {}
export interface CreatePresignedDomainUrlCommandOutput
  extends CreatePresignedDomainUrlResponse,
    __MetadataBearer {}
declare const CreatePresignedDomainUrlCommand_base: {
  new (
    input: CreatePresignedDomainUrlCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreatePresignedDomainUrlCommandInput,
    CreatePresignedDomainUrlCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreatePresignedDomainUrlCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreatePresignedDomainUrlCommandInput,
    CreatePresignedDomainUrlCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreatePresignedDomainUrlCommand extends CreatePresignedDomainUrlCommand_base {
  protected static __types: {
    api: {
      input: CreatePresignedDomainUrlRequest;
      output: CreatePresignedDomainUrlResponse;
    };
    sdk: {
      input: CreatePresignedDomainUrlCommandInput;
      output: CreatePresignedDomainUrlCommandOutput;
    };
  };
}
