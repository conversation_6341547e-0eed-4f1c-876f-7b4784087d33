import { HttpRequest } from "@smithy/types";
export interface TestCase {
    name: string;
    request: HttpRequest;
    authorization: string;
}
export declare const region = "us-east-1";
export declare const service = "service";
export declare const credentials: {
    accessKeyId: string;
    secretAccessKey: string;
};
export declare const signingDate: Date;
export declare const requests: Array<TestCase>;
