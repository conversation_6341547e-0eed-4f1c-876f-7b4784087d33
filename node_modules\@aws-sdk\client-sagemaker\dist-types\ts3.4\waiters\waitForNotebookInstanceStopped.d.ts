import { WaiterConfiguration, WaiterResult } from "@smithy/util-waiter";
import { DescribeNotebookInstanceCommandInput } from "../commands/DescribeNotebookInstanceCommand";
import { SageMakerClient } from "../SageMakerClient";
export declare const waitForNotebookInstanceStopped: (
  params: WaiterConfiguration<SageMakerClient>,
  input: DescribeNotebookInstanceCommandInput
) => Promise<WaiterResult>;
export declare const waitUntilNotebookInstanceStopped: (
  params: WaiterConfiguration<SageMakerClient>,
  input: DescribeNotebookInstanceCommandInput
) => Promise<WaiterResult>;
