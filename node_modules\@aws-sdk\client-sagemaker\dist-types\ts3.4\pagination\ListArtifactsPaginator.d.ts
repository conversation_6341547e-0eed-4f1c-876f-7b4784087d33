import { Paginator } from "@smithy/types";
import {
  ListArtifactsCommandInput,
  ListArtifactsCommandOutput,
} from "../commands/ListArtifactsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
export declare const paginateListArtifacts: (
  config: SageMakerPaginationConfiguration,
  input: ListArtifactsCommandInput,
  ...rest: any[]
) => Paginator<ListArtifactsCommandOutput>;
