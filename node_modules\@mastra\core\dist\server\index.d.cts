import { <PERSON><PERSON>, Context, MiddlewareHandler } from 'hono';
import { DescribeRouteOptions } from 'hono-openapi';
import { M as Methods, a as Mastra, A as ApiRoute, b as MastraAuthConfig } from '../base-B96VvaWm.cjs';
export { C as ContextWithMastra } from '../base-B96VvaWm.cjs';
import 'ai';
import '../base-aPYtPBT2.cjs';
import '@opentelemetry/api';
import '../logger-EhZkzZOr.cjs';
import 'stream';
import '@opentelemetry/sdk-trace-base';
import '../types-Bo1uigWx.cjs';
import 'sift';
import 'zod';
import 'json-schema';
import '../deployer/index.cjs';
import '../bundler/index.cjs';
import 'node:http';
import '../runtime-context/index.cjs';
import '../tts/index.cjs';
import '../vector/index.cjs';
import '../vector/filter/index.cjs';
import 'xstate';
import 'node:events';
import 'events';
import '../workflows/constants.cjs';
import 'hono/cors';
import 'ai/test';

type ParamsFromPath<P extends string> = {
    [K in P extends `${string}:${infer Param}/${string}` | `${string}:${infer Param}` ? Param : never]: string;
};
declare function registerApiRoute<P extends string>(path: P, options: P extends `/api/${string}` ? never : {
    method: Methods;
    openapi?: DescribeRouteOptions;
    handler?: Handler<{
        Variables: {
            mastra: Mastra;
        };
    }, P, ParamsFromPath<P>>;
    createHandler?: (c: Context) => Promise<Handler<{
        Variables: {
            mastra: Mastra;
        };
    }, P, ParamsFromPath<P>>>;
    middleware?: MiddlewareHandler | MiddlewareHandler[];
}): P extends `/api/${string}` ? never : ApiRoute;
declare function defineAuth<TUser>(config: MastraAuthConfig<TUser>): MastraAuthConfig<TUser>;

export { MastraAuthConfig, defineAuth, registerApiRoute };
