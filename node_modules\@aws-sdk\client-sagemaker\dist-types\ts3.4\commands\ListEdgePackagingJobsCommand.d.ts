import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ListEdgePackagingJobsRequest,
  ListEdgePackagingJobsResponse,
} from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface ListEdgePackagingJobsCommandInput
  extends ListEdgePackagingJobsRequest {}
export interface ListEdgePackagingJobsCommandOutput
  extends ListEdgePackagingJobsResponse,
    __MetadataBearer {}
declare const ListEdgePackagingJobsCommand_base: {
  new (
    input: ListEdgePackagingJobsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListEdgePackagingJobsCommandInput,
    ListEdgePackagingJobsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    ...[input]: [] | [ListEdgePackagingJobsCommandInput]
  ): import("@smithy/smithy-client").CommandImpl<
    ListEdgePackagingJobsCommandInput,
    ListEdgePackagingJobsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListEdgePackagingJobsCommand extends ListEdgePackagingJobsCommand_base {
  protected static __types: {
    api: {
      input: ListEdgePackagingJobsRequest;
      output: ListEdgePackagingJobsResponse;
    };
    sdk: {
      input: ListEdgePackagingJobsCommandInput;
      output: ListEdgePackagingJobsCommandOutput;
    };
  };
}
