import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { UpdateDevicesRequest } from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateDevicesCommandInput extends UpdateDevicesRequest {}
export interface UpdateDevicesCommandOutput extends __MetadataBearer {}
declare const UpdateDevicesCommand_base: {
  new (
    input: UpdateDevicesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateDevicesCommandInput,
    UpdateDevicesCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateDevicesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateDevicesCommandInput,
    UpdateDevicesCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateDevicesCommand extends UpdateDevicesCommand_base {
  protected static __types: {
    api: {
      input: UpdateDevicesRequest;
      output: {};
    };
    sdk: {
      input: UpdateDevicesCommandInput;
      output: UpdateDevicesCommandOutput;
    };
  };
}
