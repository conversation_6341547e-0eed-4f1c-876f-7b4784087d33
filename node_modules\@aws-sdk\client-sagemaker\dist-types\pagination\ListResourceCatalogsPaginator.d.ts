import { Paginator } from "@smithy/types";
import { ListResourceCatalogsCommandInput, ListResourceCatalogsCommandOutput } from "../commands/ListResourceCatalogsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListResourceCatalogs: (config: SageMakerPaginationConfiguration, input: ListResourceCatalogsCommandInput, ...rest: any[]) => Paginator<ListResourceCatalogsCommandOutput>;
