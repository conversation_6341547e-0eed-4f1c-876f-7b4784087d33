import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeFeatureMetadataRequest,
  DescribeFeatureMetadataResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeFeatureMetadataCommandInput
  extends DescribeFeatureMetadataRequest {}
export interface DescribeFeatureMetadataCommandOutput
  extends DescribeFeatureMetadataResponse,
    __MetadataBearer {}
declare const DescribeFeatureMetadataCommand_base: {
  new (
    input: DescribeFeatureMetadataCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeFeatureMetadataCommandInput,
    DescribeFeatureMetadataCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeFeatureMetadataCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeFeatureMetadataCommandInput,
    DescribeFeatureMetadataCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeFeatureMetadataCommand extends DescribeFeatureMetadataCommand_base {
  protected static __types: {
    api: {
      input: DescribeFeatureMetadataRequest;
      output: DescribeFeatureMetadataResponse;
    };
    sdk: {
      input: DescribeFeatureMetadataCommandInput;
      output: DescribeFeatureMetadataCommandOutput;
    };
  };
}
