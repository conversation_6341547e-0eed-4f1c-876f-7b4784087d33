import { Transform } from 'stream';

interface BaseLogMessage {
    runId?: string;
    msg: string;
    level: number;
    time: Date;
    pid: number;
    hostname: string;
    name: string;
}
declare abstract class LoggerTransport extends Transform {
    constructor(opts?: any);
    getLogsByRunId(_args: {
        runId: string;
    }): Promise<BaseLogMessage[]>;
    getLogs(): Promise<BaseLogMessage[]>;
}

declare const RegisteredLogger: {
    readonly AGENT: "AGENT";
    readonly NETWORK: "NETWORK";
    readonly WORKFLOW: "WORKFLOW";
    readonly LLM: "LLM";
    readonly TTS: "TTS";
    readonly VOICE: "VOICE";
    readonly VECTOR: "VECTOR";
    readonly BUNDLER: "BUNDLER";
    readonly DEPLOYER: "DEPLOYER";
    readonly MEMORY: "MEMORY";
    readonly STORAGE: "STORAGE";
    readonly EMBEDDINGS: "EMBEDDINGS";
    readonly MCP_SERVER: "MCP_SERVER";
};
type RegisteredLogger = (typeof RegisteredLogger)[keyof typeof RegisteredLogger];
declare const LogLevel: {
    readonly DEBUG: "debug";
    readonly INFO: "info";
    readonly WARN: "warn";
    readonly ERROR: "error";
    readonly NONE: "silent";
};
type LogLevel = (typeof LogLevel)[keyof typeof LogLevel];

interface IMastraLogger {
    debug(message: string, ...args: any[]): void;
    info(message: string, ...args: any[]): void;
    warn(message: string, ...args: any[]): void;
    error(message: string, ...args: any[]): void;
    getTransports(): Map<string, LoggerTransport>;
    getLogs(_transportId: string): Promise<any[]>;
    getLogsByRunId(_args: {
        transportId: string;
        runId: string;
    }): Promise<any[]>;
}
declare abstract class MastraLogger implements IMastraLogger {
    protected name: string;
    protected level: LogLevel;
    protected transports: Map<string, LoggerTransport>;
    constructor(options?: {
        name?: string;
        level?: LogLevel;
        transports?: Record<string, LoggerTransport>;
    });
    abstract debug(message: string, ...args: any[]): void;
    abstract info(message: string, ...args: any[]): void;
    abstract warn(message: string, ...args: any[]): void;
    abstract error(message: string, ...args: any[]): void;
    getTransports(): Map<string, LoggerTransport>;
    getLogs(transportId: string): Promise<BaseLogMessage[]>;
    getLogsByRunId({ transportId, runId }: {
        transportId: string;
        runId: string;
    }): Promise<BaseLogMessage[]>;
}

export { type BaseLogMessage as B, type IMastraLogger as I, LoggerTransport as L, MastraLogger as M, RegisteredLogger as R, LogLevel as a };
