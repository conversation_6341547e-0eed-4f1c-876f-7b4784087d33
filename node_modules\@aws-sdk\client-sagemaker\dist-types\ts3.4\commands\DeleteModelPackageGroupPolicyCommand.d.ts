import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { DeleteModelPackageGroupPolicyInput } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteModelPackageGroupPolicyCommandInput
  extends DeleteModelPackageGroupPolicyInput {}
export interface DeleteModelPackageGroupPolicyCommandOutput
  extends __MetadataBearer {}
declare const DeleteModelPackageGroupPolicyCommand_base: {
  new (
    input: DeleteModelPackageGroupPolicyCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteModelPackageGroupPolicyCommandInput,
    DeleteModelPackageGroupPolicyCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteModelPackageGroupPolicyCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteModelPackageGroupPolicyCommandInput,
    DeleteModelPackageGroupPolicyCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteModelPackageGroupPolicyCommand extends DeleteModelPackageGroupPolicyCommand_base {
  protected static __types: {
    api: {
      input: DeleteModelPackageGroupPolicyInput;
      output: {};
    };
    sdk: {
      input: DeleteModelPackageGroupPolicyCommandInput;
      output: DeleteModelPackageGroupPolicyCommandOutput;
    };
  };
}
