import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DeleteImageRequest, DeleteImageResponse } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteImageCommandInput extends DeleteImageRequest {}
export interface DeleteImageCommandOutput
  extends DeleteImageResponse,
    __MetadataBearer {}
declare const DeleteImageCommand_base: {
  new (
    input: DeleteImageCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteImageCommandInput,
    DeleteImageCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteImageCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteImageCommandInput,
    DeleteImageCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteImageCommand extends DeleteImageCommand_base {
  protected static __types: {
    api: {
      input: DeleteImageRequest;
      output: {};
    };
    sdk: {
      input: DeleteImageCommandInput;
      output: DeleteImageCommandOutput;
    };
  };
}
