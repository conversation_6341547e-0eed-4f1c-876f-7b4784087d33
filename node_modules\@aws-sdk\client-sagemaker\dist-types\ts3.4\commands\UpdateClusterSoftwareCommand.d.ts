import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  UpdateClusterSoftwareRequest,
  UpdateClusterSoftwareResponse,
} from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateClusterSoftwareCommandInput
  extends UpdateClusterSoftwareRequest {}
export interface UpdateClusterSoftwareCommandOutput
  extends UpdateClusterSoftwareResponse,
    __MetadataBearer {}
declare const UpdateClusterSoftwareCommand_base: {
  new (
    input: UpdateClusterSoftwareCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateClusterSoftwareCommandInput,
    UpdateClusterSoftwareCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateClusterSoftwareCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateClusterSoftwareCommandInput,
    UpdateClusterSoftwareCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateClusterSoftwareCommand extends UpdateClusterSoftwareCommand_base {
  protected static __types: {
    api: {
      input: UpdateClusterSoftwareRequest;
      output: UpdateClusterSoftwareResponse;
    };
    sdk: {
      input: UpdateClusterSoftwareCommandInput;
      output: UpdateClusterSoftwareCommandOutput;
    };
  };
}
