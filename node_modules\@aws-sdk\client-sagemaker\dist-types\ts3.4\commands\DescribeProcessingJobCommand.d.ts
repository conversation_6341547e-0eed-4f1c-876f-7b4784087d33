import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeProcessingJobRequest,
  DescribeProcessingJobResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeProcessingJobCommandInput
  extends DescribeProcessingJobRequest {}
export interface DescribeProcessingJobCommandOutput
  extends DescribeProcessingJobResponse,
    __MetadataBearer {}
declare const DescribeProcessingJobCommand_base: {
  new (
    input: DescribeProcessingJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeProcessingJobCommandInput,
    DescribeProcessingJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeProcessingJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeProcessingJobCommandInput,
    DescribeProcessingJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeProcessingJobCommand extends DescribeProcessingJobCommand_base {
  protected static __types: {
    api: {
      input: DescribeProcessingJobRequest;
      output: DescribeProcessingJobResponse;
    };
    sdk: {
      input: DescribeProcessingJobCommandInput;
      output: DescribeProcessingJobCommandOutput;
    };
  };
}
