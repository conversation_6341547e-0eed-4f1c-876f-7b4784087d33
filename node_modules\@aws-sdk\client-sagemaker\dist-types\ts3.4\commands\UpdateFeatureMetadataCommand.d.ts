import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { UpdateFeatureMetadataRequest } from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateFeatureMetadataCommandInput
  extends UpdateFeatureMetadataRequest {}
export interface UpdateFeatureMetadataCommandOutput extends __MetadataBearer {}
declare const UpdateFeatureMetadataCommand_base: {
  new (
    input: UpdateFeatureMetadataCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateFeatureMetadataCommandInput,
    UpdateFeatureMetadataCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateFeatureMetadataCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateFeatureMetadataCommandInput,
    UpdateFeatureMetadataCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateFeatureMetadataCommand extends UpdateFeatureMetadataCommand_base {
  protected static __types: {
    api: {
      input: UpdateFeatureMetadataRequest;
      output: {};
    };
    sdk: {
      input: UpdateFeatureMetadataCommandInput;
      output: UpdateFeatureMetadataCommandOutput;
    };
  };
}
