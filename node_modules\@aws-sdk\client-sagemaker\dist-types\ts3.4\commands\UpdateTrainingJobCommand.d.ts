import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  UpdateTrainingJobRequest,
  UpdateTrainingJobResponse,
} from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateTrainingJobCommandInput
  extends UpdateTrainingJobRequest {}
export interface UpdateTrainingJobCommandOutput
  extends UpdateTrainingJobResponse,
    __MetadataBearer {}
declare const UpdateTrainingJobCommand_base: {
  new (
    input: UpdateTrainingJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateTrainingJobCommandInput,
    UpdateTrainingJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateTrainingJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateTrainingJobCommandInput,
    UpdateTrainingJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateTrainingJobCommand extends UpdateTrainingJobCommand_base {
  protected static __types: {
    api: {
      input: UpdateTrainingJobRequest;
      output: UpdateTrainingJobResponse;
    };
    sdk: {
      input: UpdateTrainingJobCommandInput;
      output: UpdateTrainingJobCommandOutput;
    };
  };
}
