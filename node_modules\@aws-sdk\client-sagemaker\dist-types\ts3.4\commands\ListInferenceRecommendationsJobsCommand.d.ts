import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ListInferenceRecommendationsJobsRequest,
  ListInferenceRecommendationsJobsResponse,
} from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface ListInferenceRecommendationsJobsCommandInput
  extends ListInferenceRecommendationsJobsRequest {}
export interface ListInferenceRecommendationsJobsCommandOutput
  extends ListInferenceRecommendationsJobsResponse,
    __MetadataBearer {}
declare const ListInferenceRecommendationsJobsCommand_base: {
  new (
    input: ListInferenceRecommendationsJobsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListInferenceRecommendationsJobsCommandInput,
    ListInferenceRecommendationsJobsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    ...[input]: [] | [ListInferenceRecommendationsJobsCommandInput]
  ): import("@smithy/smithy-client").CommandImpl<
    ListInferenceRecommendationsJobsCommandInput,
    ListInferenceRecommendationsJobsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListInferenceRecommendationsJobsCommand extends ListInferenceRecommendationsJobsCommand_base {
  protected static __types: {
    api: {
      input: ListInferenceRecommendationsJobsRequest;
      output: ListInferenceRecommendationsJobsResponse;
    };
    sdk: {
      input: ListInferenceRecommendationsJobsCommandInput;
      output: ListInferenceRecommendationsJobsCommandOutput;
    };
  };
}
