import { a$ as ExecutionEngine, b5 as StepResult, a_ as ExecutionGraph, aR as StepFlowEntry, ae as Step, b0 as ExecuteFunction } from '../base-QP4OC4dB.js';
export { b7 as DynamicMapping, ba as ExtractSchemaFromStep, b9 as ExtractSchemaType, b8 as PathsToStringProps, aZ as Run, aS as SerializedStep, aT as SerializedStepFlowEntry, b2 as StepFailure, b4 as StepRunning, b1 as StepSuccess, b3 as StepSuspended, b6 as StepsRecord, bb as VariableReference, bc as WatchEvent, af as Workflow, ag as WorkflowConfig, aY as WorkflowResult, be as WorkflowRunState, bd as ZodPathType, aV as cloneStep, aX as cloneWorkflow, aU as createStep, aW as createWorkflow } from '../base-QP4OC4dB.js';
import { Span } from '@opentelemetry/api';
import { RuntimeContext } from '../runtime-context/index.js';
import 'ai';
import '../base-tc5kgDTD.js';
import '../logger-EhZkzZOr.js';
import 'stream';
import '@opentelemetry/sdk-trace-base';
import '../types-Bo1uigWx.js';
import 'sift';
import 'zod';
import 'json-schema';
import '../deployer/index.js';
import '../bundler/index.js';
import 'node:http';
import 'hono';
import '../tts/index.js';
import '../vector/index.js';
import '../vector/filter/index.js';
import 'xstate';
import 'node:events';
import 'events';
import './constants.js';
import 'hono/cors';
import 'hono-openapi';
import 'ai/test';

type ExecutionContext = {
    workflowId: string;
    runId: string;
    executionPath: number[];
    suspendedPaths: Record<string, number[]>;
    retryConfig: {
        attempts: number;
        delay: number;
    };
    executionSpan: Span;
};
/**
 * Default implementation of the ExecutionEngine using XState
 */
declare class DefaultExecutionEngine extends ExecutionEngine {
    protected fmtReturnValue<TOutput>(executionSpan: Span | undefined, emitter: {
        emit: (event: string, data: any) => Promise<void>;
    }, stepResults: Record<string, StepResult<any, any, any, any>>, lastOutput: StepResult<any, any, any, any>, error?: Error | string): Promise<TOutput>;
    /**
     * Executes a workflow run with the provided execution graph and input
     * @param graph The execution graph to execute
     * @param input The input data for the workflow
     * @returns A promise that resolves to the workflow output
     */
    execute<TInput, TOutput>(params: {
        workflowId: string;
        runId: string;
        graph: ExecutionGraph;
        input?: TInput;
        resume?: {
            steps: string[];
            stepResults: Record<string, StepResult<any, any, any, any>>;
            resumePayload: any;
            resumePath: number[];
        };
        emitter: {
            emit: (event: string, data: any) => Promise<void>;
        };
        retryConfig?: {
            attempts?: number;
            delay?: number;
        };
        runtimeContext: RuntimeContext;
    }): Promise<TOutput>;
    getStepOutput(stepResults: Record<string, any>, step?: StepFlowEntry): any;
    executeStep({ workflowId, runId, step, stepResults, executionContext, resume, prevOutput, emitter, runtimeContext, }: {
        workflowId: string;
        runId: string;
        step: Step<string, any, any>;
        stepResults: Record<string, StepResult<any, any, any, any>>;
        executionContext: ExecutionContext;
        resume?: {
            steps: string[];
            resumePayload: any;
        };
        prevOutput: any;
        emitter: {
            emit: (event: string, data: any) => Promise<void>;
        };
        runtimeContext: RuntimeContext;
    }): Promise<StepResult<any, any, any, any>>;
    executeParallel({ workflowId, runId, entry, prevStep, stepResults, resume, executionContext, emitter, runtimeContext, }: {
        workflowId: string;
        runId: string;
        entry: {
            type: 'parallel';
            steps: StepFlowEntry[];
        };
        prevStep: StepFlowEntry;
        stepResults: Record<string, StepResult<any, any, any, any>>;
        resume?: {
            steps: string[];
            stepResults: Record<string, StepResult<any, any, any, any>>;
            resumePayload: any;
            resumePath: number[];
        };
        executionContext: ExecutionContext;
        emitter: {
            emit: (event: string, data: any) => Promise<void>;
        };
        runtimeContext: RuntimeContext;
    }): Promise<StepResult<any, any, any, any>>;
    executeConditional({ workflowId, runId, entry, prevOutput, prevStep, stepResults, resume, executionContext, emitter, runtimeContext, }: {
        workflowId: string;
        runId: string;
        entry: {
            type: 'conditional';
            steps: StepFlowEntry[];
            conditions: ExecuteFunction<any, any, any, any>[];
        };
        prevStep: StepFlowEntry;
        prevOutput: any;
        stepResults: Record<string, StepResult<any, any, any, any>>;
        resume?: {
            steps: string[];
            stepResults: Record<string, StepResult<any, any, any, any>>;
            resumePayload: any;
            resumePath: number[];
        };
        executionContext: ExecutionContext;
        emitter: {
            emit: (event: string, data: any) => Promise<void>;
        };
        runtimeContext: RuntimeContext;
    }): Promise<StepResult<any, any, any, any>>;
    executeLoop({ workflowId, runId, entry, prevOutput, stepResults, resume, executionContext, emitter, runtimeContext, }: {
        workflowId: string;
        runId: string;
        entry: {
            type: 'loop';
            step: Step;
            condition: ExecuteFunction<any, any, any, any>;
            loopType: 'dowhile' | 'dountil';
        };
        prevStep: StepFlowEntry;
        prevOutput: any;
        stepResults: Record<string, StepResult<any, any, any, any>>;
        resume?: {
            steps: string[];
            stepResults: Record<string, StepResult<any, any, any, any>>;
            resumePayload: any;
            resumePath: number[];
        };
        executionContext: ExecutionContext;
        emitter: {
            emit: (event: string, data: any) => Promise<void>;
        };
        runtimeContext: RuntimeContext;
    }): Promise<StepResult<any, any, any, any>>;
    executeForeach({ workflowId, runId, entry, prevOutput, stepResults, resume, executionContext, emitter, runtimeContext, }: {
        workflowId: string;
        runId: string;
        entry: {
            type: 'foreach';
            step: Step;
            opts: {
                concurrency: number;
            };
        };
        prevStep: StepFlowEntry;
        prevOutput: any;
        stepResults: Record<string, StepResult<any, any, any, any>>;
        resume?: {
            steps: string[];
            stepResults: Record<string, StepResult<any, any, any, any>>;
            resumePayload: any;
            resumePath: number[];
        };
        executionContext: ExecutionContext;
        emitter: {
            emit: (event: string, data: any) => Promise<void>;
        };
        runtimeContext: RuntimeContext;
    }): Promise<StepResult<any, any, any, any>>;
    protected persistStepUpdate({ workflowId, runId, stepResults, executionContext, }: {
        workflowId: string;
        runId: string;
        stepResults: Record<string, StepResult<any, any, any, any>>;
        executionContext: ExecutionContext;
    }): Promise<void>;
    executeEntry({ workflowId, runId, entry, prevStep, stepResults, resume, executionContext, emitter, runtimeContext, }: {
        workflowId: string;
        runId: string;
        entry: StepFlowEntry;
        prevStep: StepFlowEntry;
        stepResults: Record<string, StepResult<any, any, any, any>>;
        resume?: {
            steps: string[];
            stepResults: Record<string, StepResult<any, any, any, any>>;
            resumePayload: any;
            resumePath: number[];
        };
        executionContext: ExecutionContext;
        emitter: {
            emit: (event: string, data: any) => Promise<void>;
        };
        runtimeContext: RuntimeContext;
    }): Promise<StepResult<any, any, any, any>>;
}

export { DefaultExecutionEngine, ExecuteFunction, type ExecutionContext, ExecutionEngine, ExecutionGraph, Step, StepFlowEntry, StepResult };
