import { V as VariableReference, o as StepResult, p as LegacyStep, q as LegacyWorkflow, r as Agent, s as WorkflowContext, t as ToolsInput, a as <PERSON><PERSON>, u as StepAction, v as LegacyWorkflowRunResult } from '../../base-QP4OC4dB.js';
export { F as ActionContext, D as BaseCondition, X as DependencyCheckOutput, a2 as ExtractSchemaFromStep, a5 as ExtractSchemaType, a3 as ExtractStepResult, a7 as LegacyWorkflowRunState, a0 as LegacyWorkflowState, a6 as PathsToStringProps, P as ResolverFunctionInput, Q as ResolverFunctionOutput, R as RetryConfig, I as StepCondition, J as StepConfig, H as StepDef, x as StepExecutionContext, B as StepGraph, a1 as StepId, a4 as StepInputType, z as StepNode, Y as StepResolverOutput, y as StepVariableType, K as StepsR<PERSON>ord, U as SubscriberFunctionOutput, G as WhenConditionReturnValue, _ as WorkflowActionParams, $ as WorkflowActions, Z as WorkflowActors, O as WorkflowEvent, N as WorkflowLogMessage, w as WorkflowOptions, a8 as WorkflowResumeResult } from '../../base-QP4OC4dB.js';
import { z } from 'zod';
import { M as Metric } from '../../types-Bo1uigWx.js';
import { I as IMastraLogger } from '../../logger-EhZkzZOr.js';
import '../../base-tc5kgDTD.js';
import 'ai';
import 'sift';
import 'json-schema';
import '../../deployer/index.js';
import '../../bundler/index.js';
import 'node:http';
import 'hono';
import '../../runtime-context/index.js';
import '../../tts/index.js';
import '../../vector/index.js';
import '../../vector/filter/index.js';
import '@opentelemetry/api';
import 'xstate';
import 'node:events';
import 'events';
import '../constants.js';
import 'hono/cors';
import 'hono-openapi';
import 'ai/test';
import 'stream';
import '@opentelemetry/sdk-trace-base';

declare function isErrorEvent(stateEvent: any): stateEvent is {
    type: `xstate.error.actor.${string}`;
    error: Error;
};
declare function isTransitionEvent(stateEvent: any): stateEvent is {
    type: `xstate.done.actor.${string}`;
    output?: unknown;
};
declare function isVariableReference(value: any): value is VariableReference<any, any>;
declare function getStepResult(result?: StepResult<any>): any;
declare function getSuspendedPaths({ value, path, suspendedPaths, }: {
    value: string | Record<string, string>;
    path: string;
    suspendedPaths: Set<string>;
}): void;
declare function isFinalState(status: string): boolean;
declare function isLimboState(status: string): boolean;
declare function recursivelyCheckForFinalState({ value, suspendedPaths, path, }: {
    value: string | Record<string, string>;
    suspendedPaths: Set<string>;
    path: string;
}): boolean;
declare function getActivePathsAndStatus(value: Record<string, any>): Array<{
    stepPath: string[];
    stepId: string;
    status: string;
}>;
declare function mergeChildValue(startStepId: string, parent: Record<string, any>, child: Record<string, any>): Record<string, any>;
declare const updateStepInHierarchy: (value: Record<string, any>, targetStepId: string) => Record<string, any>;
declare function getResultActivePaths(state: {
    value: Record<string, string>;
    context: {
        steps: Record<string, any>;
    };
}): Map<string, {
    status: string;
    suspendPayload?: any;
    stepPath: string[];
}>;
declare function isWorkflow(step: LegacyStep<any, any, any, any> | LegacyWorkflow<any, any, any, any> | Agent<any, any, any>): step is LegacyWorkflow<any, any, any, any>;
declare function isAgent(step: LegacyStep<any, any, any, any> | Agent<any, any, any> | LegacyWorkflow<any, any, any, any>): step is Agent<any, any, any>;
declare function resolveVariables({ runId, logger, variables, context, }: {
    runId: string;
    logger: IMastraLogger;
    variables: Record<string, VariableReference<any, any>>;
    context: WorkflowContext;
}): Record<string, any>;
declare function agentToStep<TAgentId extends string = string, TTools extends ToolsInput = ToolsInput, TMetrics extends Record<string, Metric> = Record<string, Metric>>(agent: Agent<TAgentId, TTools, TMetrics>, { mastra }?: {
    mastra?: Mastra;
}): StepAction<TAgentId, z.ZodObject<{
    prompt: z.ZodString;
}>, z.ZodObject<{
    text: z.ZodString;
}>, any>;
declare function workflowToStep<TSteps extends LegacyStep<any, any, any, any>[], TStepId extends string = any, TTriggerSchema extends z.ZodObject<any> = any, TResultSchema extends z.ZodObject<any> = any>(workflow: LegacyWorkflow<TSteps, TStepId, TTriggerSchema, TResultSchema>, { mastra }: {
    mastra?: Mastra;
}): StepAction<TStepId, TTriggerSchema, z.ZodType<LegacyWorkflowRunResult<TTriggerSchema, TSteps, TResultSchema>>, any>;
declare function isConditionalKey(key: string): boolean;

export { LegacyStep, LegacyWorkflow, LegacyWorkflowRunResult, StepAction, StepResult, VariableReference, WorkflowContext, agentToStep, getActivePathsAndStatus, getResultActivePaths, getStepResult, getSuspendedPaths, isAgent, isConditionalKey, isErrorEvent, isFinalState, isLimboState, isTransitionEvent, isVariableReference, isWorkflow, mergeChildValue, recursivelyCheckForFinalState, resolveVariables, updateStepInHierarchy, workflowToStep };
