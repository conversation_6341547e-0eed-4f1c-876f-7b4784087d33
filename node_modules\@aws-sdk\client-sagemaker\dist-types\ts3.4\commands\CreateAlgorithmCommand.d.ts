import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateAlgorithmInput,
  CreateAlgorithmOutput,
} from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateAlgorithmCommandInput extends CreateAlgorithmInput {}
export interface CreateAlgorithmCommandOutput
  extends CreateAlgorithmOutput,
    __MetadataBearer {}
declare const CreateAlgorithmCommand_base: {
  new (
    input: CreateAlgorithmCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateAlgorithmCommandInput,
    CreateAlgorithmCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateAlgorithmCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateAlgorithmCommandInput,
    CreateAlgorithmCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateAlgorithmCommand extends CreateAlgorithmCommand_base {
  protected static __types: {
    api: {
      input: CreateAlgorithmInput;
      output: CreateAlgorithmOutput;
    };
    sdk: {
      input: CreateAlgorithmCommandInput;
      output: CreateAlgorithmCommandOutput;
    };
  };
}
