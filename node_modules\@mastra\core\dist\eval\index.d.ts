import { M as Metric, T as TestInfo, E as EvaluationResult } from '../types-Bo1uigWx.js';
export { a as MetricResult } from '../types-Bo1uigWx.js';
import { r as Agent } from '../base-QP4OC4dB.js';
import 'ai';
import '../base-tc5kgDTD.js';
import '@opentelemetry/api';
import '../logger-EhZkzZOr.js';
import 'stream';
import '@opentelemetry/sdk-trace-base';
import 'sift';
import 'zod';
import 'json-schema';
import '../deployer/index.js';
import '../bundler/index.js';
import 'node:http';
import 'hono';
import '../runtime-context/index.js';
import '../tts/index.js';
import '../vector/index.js';
import '../vector/filter/index.js';
import 'xstate';
import 'node:events';
import 'events';
import '../workflows/constants.js';
import 'hono/cors';
import 'hono-openapi';
import 'ai/test';

declare function evaluate<T extends Agent>({ agentName, input, metric, output, runId, globalRunId, testInfo, instructions, }: {
    agentName: string;
    input: Parameters<T['generate']>[0];
    metric: Metric;
    output: string;
    globalRunId: string;
    runId?: string;
    testInfo?: TestInfo;
    instructions: string;
}): Promise<EvaluationResult>;

export { EvaluationResult, Metric, TestInfo, evaluate };
