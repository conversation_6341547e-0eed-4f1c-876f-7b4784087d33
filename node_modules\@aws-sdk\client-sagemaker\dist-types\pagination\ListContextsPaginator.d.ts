import { Paginator } from "@smithy/types";
import { ListContextsCommandInput, ListContextsCommandOutput } from "../commands/ListContextsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListContexts: (config: SageMakerPaginationConfiguration, input: ListContextsCommandInput, ...rest: any[]) => Paginator<ListContextsCommandOutput>;
