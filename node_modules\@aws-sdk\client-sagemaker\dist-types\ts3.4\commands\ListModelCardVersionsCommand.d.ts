import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ListModelCardVersionsRequest,
  ListModelCardVersionsResponse,
} from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface ListModelCardVersionsCommandInput
  extends ListModelCardVersionsRequest {}
export interface ListModelCardVersionsCommandOutput
  extends ListModelCardVersionsResponse,
    __MetadataBearer {}
declare const ListModelCardVersionsCommand_base: {
  new (
    input: ListModelCardVersionsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListModelCardVersionsCommandInput,
    ListModelCardVersionsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: ListModelCardVersionsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListModelCardVersionsCommandInput,
    ListModelCardVersionsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListModelCardVersionsCommand extends ListModelCardVersionsCommand_base {
  protected static __types: {
    api: {
      input: ListModelCardVersionsRequest;
      output: ListModelCardVersionsResponse;
    };
    sdk: {
      input: ListModelCardVersionsCommandInput;
      output: ListModelCardVersionsCommandOutput;
    };
  };
}
