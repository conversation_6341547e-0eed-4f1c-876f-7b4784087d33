import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribePartnerAppRequest,
  DescribePartnerAppResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribePartnerAppCommandInput
  extends DescribePartnerAppRequest {}
export interface DescribePartnerAppCommandOutput
  extends DescribePartnerAppResponse,
    __MetadataBearer {}
declare const DescribePartnerAppCommand_base: {
  new (
    input: DescribePartnerAppCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribePartnerAppCommandInput,
    DescribePartnerAppCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribePartnerAppCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribePartnerAppCommandInput,
    DescribePartnerAppCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribePartnerAppCommand extends DescribePartnerAppCommand_base {
  protected static __types: {
    api: {
      input: DescribePartnerAppRequest;
      output: DescribePartnerAppResponse;
    };
    sdk: {
      input: DescribePartnerAppCommandInput;
      output: DescribePartnerAppCommandOutput;
    };
  };
}
