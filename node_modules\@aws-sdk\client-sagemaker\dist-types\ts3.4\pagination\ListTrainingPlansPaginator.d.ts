import { Paginator } from "@smithy/types";
import {
  ListTrainingPlansCommandInput,
  ListTrainingPlansCommandOutput,
} from "../commands/ListTrainingPlansCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
export declare const paginateListTrainingPlans: (
  config: SageMakerPaginationConfiguration,
  input: ListTrainingPlansCommandInput,
  ...rest: any[]
) => Paginator<ListTrainingPlansCommandOutput>;
