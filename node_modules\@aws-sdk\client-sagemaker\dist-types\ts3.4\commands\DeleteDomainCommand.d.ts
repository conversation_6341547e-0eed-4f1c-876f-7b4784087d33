import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DeleteDomainRequest } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteDomainCommandInput extends DeleteDomainRequest {}
export interface DeleteDomainCommandOutput extends __MetadataBearer {}
declare const DeleteDomainCommand_base: {
  new (
    input: DeleteDomainCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteDomainCommandInput,
    DeleteDomainCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteDomainCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteDomainCommandInput,
    DeleteDomainCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteDomainCommand extends DeleteDomainCommand_base {
  protected static __types: {
    api: {
      input: DeleteDomainRequest;
      output: {};
    };
    sdk: {
      input: DeleteDomainCommandInput;
      output: DeleteDomainCommandOutput;
    };
  };
}
