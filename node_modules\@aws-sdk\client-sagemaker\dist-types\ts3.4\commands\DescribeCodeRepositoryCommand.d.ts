import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeCodeRepositoryInput,
  DescribeCodeRepositoryOutput,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeCodeRepositoryCommandInput
  extends DescribeCodeRepositoryInput {}
export interface DescribeCodeRepositoryCommandOutput
  extends DescribeCodeRepositoryOutput,
    __MetadataBearer {}
declare const DescribeCodeRepositoryCommand_base: {
  new (
    input: DescribeCodeRepositoryCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeCodeRepositoryCommandInput,
    DescribeCodeRepositoryCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeCodeRepositoryCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeCodeRepositoryCommandInput,
    DescribeCodeRepositoryCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeCodeRepositoryCommand extends DescribeCodeRepositoryCommand_base {
  protected static __types: {
    api: {
      input: DescribeCodeRepositoryInput;
      output: DescribeCodeRepositoryOutput;
    };
    sdk: {
      input: DescribeCodeRepositoryCommandInput;
      output: DescribeCodeRepositoryCommandOutput;
    };
  };
}
