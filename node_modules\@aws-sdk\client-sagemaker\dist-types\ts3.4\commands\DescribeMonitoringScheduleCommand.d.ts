import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeMonitoringScheduleRequest,
  DescribeMonitoringScheduleResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeMonitoringScheduleCommandInput
  extends DescribeMonitoringScheduleRequest {}
export interface DescribeMonitoringScheduleCommandOutput
  extends DescribeMonitoringScheduleResponse,
    __MetadataBearer {}
declare const DescribeMonitoringScheduleCommand_base: {
  new (
    input: DescribeMonitoringScheduleCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeMonitoringScheduleCommandInput,
    DescribeMonitoringScheduleCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeMonitoringScheduleCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeMonitoringScheduleCommandInput,
    DescribeMonitoringScheduleCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeMonitoringScheduleCommand extends DescribeMonitoringScheduleCommand_base {
  protected static __types: {
    api: {
      input: DescribeMonitoringScheduleRequest;
      output: DescribeMonitoringScheduleResponse;
    };
    sdk: {
      input: DescribeMonitoringScheduleCommandInput;
      output: DescribeMonitoringScheduleCommandOutput;
    };
  };
}
