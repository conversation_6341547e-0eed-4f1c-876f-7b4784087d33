import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeSpaceRequest,
  DescribeSpaceResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeSpaceCommandInput extends DescribeSpaceRequest {}
export interface DescribeSpaceCommandOutput
  extends DescribeSpaceResponse,
    __MetadataBearer {}
declare const DescribeSpaceCommand_base: {
  new (
    input: DescribeSpaceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeSpaceCommandInput,
    DescribeSpaceCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeSpaceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeSpaceCommandInput,
    DescribeSpaceCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeSpaceCommand extends DescribeSpaceCommand_base {
  protected static __types: {
    api: {
      input: DescribeSpaceRequest;
      output: DescribeSpaceResponse;
    };
    sdk: {
      input: DescribeSpaceCommandInput;
      output: DescribeSpaceCommandOutput;
    };
  };
}
