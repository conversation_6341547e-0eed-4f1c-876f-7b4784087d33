import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeHumanTaskUiRequest,
  DescribeHumanTaskUiResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeHumanTaskUiCommandInput
  extends DescribeHumanTaskUiRequest {}
export interface DescribeHumanTaskUiCommandOutput
  extends DescribeHumanTaskUiResponse,
    __MetadataBearer {}
declare const DescribeHumanTaskUiCommand_base: {
  new (
    input: DescribeHumanTaskUiCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeHumanTaskUiCommandInput,
    DescribeHumanTaskUiCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeHumanTaskUiCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeHumanTaskUiCommandInput,
    DescribeHumanTaskUiCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeHumanTaskUiCommand extends DescribeHumanTaskUiCommand_base {
  protected static __types: {
    api: {
      input: DescribeHumanTaskUiRequest;
      output: DescribeHumanTaskUiResponse;
    };
    sdk: {
      input: DescribeHumanTaskUiCommandInput;
      output: DescribeHumanTaskUiCommandOutput;
    };
  };
}
