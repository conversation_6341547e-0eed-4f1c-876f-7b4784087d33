import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeModelPackageGroupInput,
  DescribeModelPackageGroupOutput,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeModelPackageGroupCommandInput
  extends DescribeModelPackageGroupInput {}
export interface DescribeModelPackageGroupCommandOutput
  extends DescribeModelPackageGroupOutput,
    __MetadataBearer {}
declare const DescribeModelPackageGroupCommand_base: {
  new (
    input: DescribeModelPackageGroupCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeModelPackageGroupCommandInput,
    DescribeModelPackageGroupCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeModelPackageGroupCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeModelPackageGroupCommandInput,
    DescribeModelPackageGroupCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeModelPackageGroupCommand extends DescribeModelPackageGroupCommand_base {
  protected static __types: {
    api: {
      input: DescribeModelPackageGroupInput;
      output: DescribeModelPackageGroupOutput;
    };
    sdk: {
      input: DescribeModelPackageGroupCommandInput;
      output: DescribeModelPackageGroupCommandOutput;
    };
  };
}
