import { Paginator } from "@smithy/types";
import {
  ListAppImageConfigsCommandInput,
  ListAppImageConfigsCommandOutput,
} from "../commands/ListAppImageConfigsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
export declare const paginateListAppImageConfigs: (
  config: SageMakerPaginationConfiguration,
  input: ListAppImageConfigsCommandInput,
  ...rest: any[]
) => Paginator<ListAppImageConfigsCommandOutput>;
