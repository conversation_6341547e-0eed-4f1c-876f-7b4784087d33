import { c as MastraStorage, T as TABLE_NAMES, S as StorageColumn, d as StorageThreadType, e as MessageType, f as StorageGetMessagesArg, E as EvalRow, W as WorkflowRuns, g as WorkflowRun } from '../base-QP4OC4dB.js';
export { h as LegacyWorkflowRun, L as LegacyWorkflowRuns, j as TABLE_EVALS, k as TABLE_MESSAGES, n as TABLE_SCHEMAS, l as TABLE_THREADS, m as TABLE_TRACES, i as TABLE_WORKFLOW_SNAPSHOT } from '../base-QP4OC4dB.js';
import 'ai';
import '../base-tc5kgDTD.js';
import '@opentelemetry/api';
import '../logger-EhZkzZOr.js';
import 'stream';
import '@opentelemetry/sdk-trace-base';
import '../types-Bo1uigWx.js';
import 'sift';
import 'zod';
import 'json-schema';
import '../deployer/index.js';
import '../bundler/index.js';
import 'node:http';
import 'hono';
import '../runtime-context/index.js';
import '../tts/index.js';
import '../vector/index.js';
import '../vector/filter/index.js';
import 'xstate';
import 'node:events';
import 'events';
import '../workflows/constants.js';
import 'hono/cors';
import 'hono-openapi';
import 'ai/test';

declare class MockStore extends MastraStorage {
    private data;
    constructor();
    createTable({ tableName }: {
        tableName: TABLE_NAMES;
        schema: Record<string, StorageColumn>;
    }): Promise<void>;
    clearTable({ tableName }: {
        tableName: TABLE_NAMES;
    }): Promise<void>;
    insert({ tableName, record }: {
        tableName: TABLE_NAMES;
        record: Record<string, any>;
    }): Promise<void>;
    batchInsert({ tableName, records }: {
        tableName: TABLE_NAMES;
        records: Record<string, any>[];
    }): Promise<void>;
    load<R>({ tableName, keys }: {
        tableName: TABLE_NAMES;
        keys: Record<string, string>;
    }): Promise<R | null>;
    getThreadById({ threadId }: {
        threadId: string;
    }): Promise<StorageThreadType | null>;
    getThreadsByResourceId({ resourceId }: {
        resourceId: string;
    }): Promise<StorageThreadType[]>;
    saveThread({ thread }: {
        thread: StorageThreadType;
    }): Promise<StorageThreadType>;
    updateThread({ id, title, metadata, }: {
        id: string;
        title: string;
        metadata: Record<string, unknown>;
    }): Promise<StorageThreadType>;
    deleteThread({ threadId }: {
        threadId: string;
    }): Promise<void>;
    getMessages<T extends MessageType[]>({ threadId, selectBy }: StorageGetMessagesArg): Promise<T>;
    saveMessages({ messages }: {
        messages: MessageType[];
    }): Promise<MessageType[]>;
    getTraces({ name, scope, page, perPage, attributes, filters, fromDate, toDate, }: {
        name?: string;
        scope?: string;
        page: number;
        perPage: number;
        attributes?: Record<string, string>;
        filters?: Record<string, any>;
        fromDate?: Date;
        toDate?: Date;
    }): Promise<any[]>;
    getEvalsByAgentName(agentName: string, type?: 'test' | 'live'): Promise<EvalRow[]>;
    getWorkflowRuns({ workflowName, fromDate, toDate, limit, offset, resourceId, }?: {
        workflowName?: string;
        fromDate?: Date;
        toDate?: Date;
        limit?: number;
        offset?: number;
        resourceId?: string;
    }): Promise<WorkflowRuns>;
    getWorkflowRunById({ runId, workflowName, }: {
        runId: string;
        workflowName?: string;
    }): Promise<WorkflowRun | null>;
}

export { EvalRow, MastraStorage, MockStore, StorageColumn, StorageGetMessagesArg, TABLE_NAMES, WorkflowRun, WorkflowRuns };
