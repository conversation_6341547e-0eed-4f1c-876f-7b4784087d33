import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { DeleteDataQualityJobDefinitionRequest } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteDataQualityJobDefinitionCommandInput
  extends DeleteDataQualityJobDefinitionRequest {}
export interface DeleteDataQualityJobDefinitionCommandOutput
  extends __MetadataBearer {}
declare const DeleteDataQualityJobDefinitionCommand_base: {
  new (
    input: DeleteDataQualityJobDefinitionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteDataQualityJobDefinitionCommandInput,
    DeleteDataQualityJobDefinitionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteDataQualityJobDefinitionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteDataQualityJobDefinitionCommandInput,
    DeleteDataQualityJobDefinitionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteDataQualityJobDefinitionCommand extends DeleteDataQualityJobDefinitionCommand_base {
  protected static __types: {
    api: {
      input: DeleteDataQualityJobDefinitionRequest;
      output: {};
    };
    sdk: {
      input: DeleteDataQualityJobDefinitionCommandInput;
      output: DeleteDataQualityJobDefinitionCommandOutput;
    };
  };
}
