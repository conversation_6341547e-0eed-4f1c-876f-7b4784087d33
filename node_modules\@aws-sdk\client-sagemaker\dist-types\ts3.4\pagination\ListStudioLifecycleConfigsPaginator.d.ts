import { Paginator } from "@smithy/types";
import {
  ListStudioLifecycleConfigsCommandInput,
  ListStudioLifecycleConfigsCommandOutput,
} from "../commands/ListStudioLifecycleConfigsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
export declare const paginateListStudioLifecycleConfigs: (
  config: SageMakerPaginationConfiguration,
  input: ListStudioLifecycleConfigsCommandInput,
  ...rest: any[]
) => Paginator<ListStudioLifecycleConfigsCommandOutput>;
