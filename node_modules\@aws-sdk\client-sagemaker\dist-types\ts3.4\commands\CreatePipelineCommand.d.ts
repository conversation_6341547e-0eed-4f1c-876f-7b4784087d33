import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreatePipelineRequest,
  CreatePipelineResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreatePipelineCommandInput extends CreatePipelineRequest {}
export interface CreatePipelineCommandOutput
  extends CreatePipelineResponse,
    __MetadataBearer {}
declare const CreatePipelineCommand_base: {
  new (
    input: CreatePipelineCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreatePipelineCommandInput,
    CreatePipelineCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreatePipelineCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreatePipelineCommandInput,
    CreatePipelineCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreatePipelineCommand extends CreatePipelineCommand_base {
  protected static __types: {
    api: {
      input: CreatePipelineRequest;
      output: CreatePipelineResponse;
    };
    sdk: {
      input: CreatePipelineCommandInput;
      output: CreatePipelineCommandOutput;
    };
  };
}
