import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  UpdateInferenceComponentInput,
  UpdateInferenceComponentOutput,
} from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateInferenceComponentCommandInput
  extends UpdateInferenceComponentInput {}
export interface UpdateInferenceComponentCommandOutput
  extends UpdateInferenceComponentOutput,
    __MetadataBearer {}
declare const UpdateInferenceComponentCommand_base: {
  new (
    input: UpdateInferenceComponentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateInferenceComponentCommandInput,
    UpdateInferenceComponentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateInferenceComponentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateInferenceComponentCommandInput,
    UpdateInferenceComponentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateInferenceComponentCommand extends UpdateInferenceComponentCommand_base {
  protected static __types: {
    api: {
      input: UpdateInferenceComponentInput;
      output: UpdateInferenceComponentOutput;
    };
    sdk: {
      input: UpdateInferenceComponentCommandInput;
      output: UpdateInferenceComponentCommandOutput;
    };
  };
}
