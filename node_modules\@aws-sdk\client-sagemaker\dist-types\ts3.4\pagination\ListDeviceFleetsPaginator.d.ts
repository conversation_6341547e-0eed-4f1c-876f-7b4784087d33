import { Paginator } from "@smithy/types";
import {
  ListDeviceFleetsCommandInput,
  ListDeviceFleetsCommandOutput,
} from "../commands/ListDeviceFleetsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
export declare const paginateListDeviceFleets: (
  config: SageMakerPaginationConfiguration,
  input: ListDeviceFleetsCommandInput,
  ...rest: any[]
) => Paginator<ListDeviceFleetsCommandOutput>;
