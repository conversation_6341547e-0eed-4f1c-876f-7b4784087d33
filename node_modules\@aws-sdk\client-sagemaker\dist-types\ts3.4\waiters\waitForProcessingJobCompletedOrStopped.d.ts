import { WaiterConfiguration, WaiterR<PERSON>ult } from "@smithy/util-waiter";
import { DescribeProcessingJobCommandInput } from "../commands/DescribeProcessingJobCommand";
import { SageMakerClient } from "../SageMakerClient";
export declare const waitForProcessingJobCompletedOrStopped: (
  params: WaiterConfiguration<SageMakerClient>,
  input: DescribeProcessingJobCommandInput
) => Promise<WaiterResult>;
export declare const waitUntilProcessingJobCompletedOrStopped: (
  params: WaiterConfiguration<SageMakerClient>,
  input: DescribeProcessingJobCommandInput
) => Promise<WaiterResult>;
