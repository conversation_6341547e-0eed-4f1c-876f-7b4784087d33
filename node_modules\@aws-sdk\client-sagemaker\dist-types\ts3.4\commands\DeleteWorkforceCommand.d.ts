import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DeleteWorkforceRequest,
  DeleteWorkforceResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteWorkforceCommandInput extends DeleteWorkforceRequest {}
export interface DeleteWorkforceCommandOutput
  extends DeleteWorkforceResponse,
    __MetadataBearer {}
declare const DeleteWorkforceCommand_base: {
  new (
    input: DeleteWorkforceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteWorkforceCommandInput,
    DeleteWorkforceCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteWorkforceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteWorkforceCommandInput,
    DeleteWorkforceCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteWorkforceCommand extends DeleteWorkforceCommand_base {
  protected static __types: {
    api: {
      input: DeleteWorkforceRequest;
      output: {};
    };
    sdk: {
      input: DeleteWorkforceCommandInput;
      output: DeleteWorkforceCommandOutput;
    };
  };
}
