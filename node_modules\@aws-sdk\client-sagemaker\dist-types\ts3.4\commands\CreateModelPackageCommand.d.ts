import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateModelPackageInput,
  CreateModelPackageOutput,
} from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateModelPackageCommandInput
  extends CreateModelPackageInput {}
export interface CreateModelPackageCommandOutput
  extends CreateModelPackageOutput,
    __MetadataBearer {}
declare const CreateModelPackageCommand_base: {
  new (
    input: CreateModelPackageCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateModelPackageCommandInput,
    CreateModelPackageCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    ...[input]: [] | [CreateModelPackageCommandInput]
  ): import("@smithy/smithy-client").CommandImpl<
    CreateModelPackageCommandInput,
    CreateModelPackageCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateModelPackageCommand extends CreateModelPackageCommand_base {
  protected static __types: {
    api: {
      input: CreateModelPackageInput;
      output: CreateModelPackageOutput;
    };
    sdk: {
      input: CreateModelPackageCommandInput;
      output: CreateModelPackageCommandOutput;
    };
  };
}
