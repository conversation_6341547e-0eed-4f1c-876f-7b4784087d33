import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeDeviceFleetRequest,
  DescribeDeviceFleetResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeDeviceFleetCommandInput
  extends DescribeDeviceFleetRequest {}
export interface DescribeDeviceFleetCommandOutput
  extends DescribeDeviceFleetResponse,
    __MetadataBearer {}
declare const DescribeDeviceFleetCommand_base: {
  new (
    input: DescribeDeviceFleetCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeDeviceFleetCommandInput,
    DescribeDeviceFleetCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeDeviceFleetCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeDeviceFleetCommandInput,
    DescribeDeviceFleetCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeDeviceFleetCommand extends DescribeDeviceFleetCommand_base {
  protected static __types: {
    api: {
      input: DescribeDeviceFleetRequest;
      output: DescribeDeviceFleetResponse;
    };
    sdk: {
      input: DescribeDeviceFleetCommandInput;
      output: DescribeDeviceFleetCommandOutput;
    };
  };
}
