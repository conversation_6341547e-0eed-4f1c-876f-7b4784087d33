import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateTrainingPlanRequest,
  CreateTrainingPlanResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateTrainingPlanCommandInput
  extends CreateTrainingPlanRequest {}
export interface CreateTrainingPlanCommandOutput
  extends CreateTrainingPlanResponse,
    __MetadataBearer {}
declare const CreateTrainingPlanCommand_base: {
  new (
    input: CreateTrainingPlanCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateTrainingPlanCommandInput,
    CreateTrainingPlanCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateTrainingPlanCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateTrainingPlanCommandInput,
    CreateTrainingPlanCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateTrainingPlanCommand extends CreateTrainingPlanCommand_base {
  protected static __types: {
    api: {
      input: CreateTrainingPlanRequest;
      output: CreateTrainingPlanResponse;
    };
    sdk: {
      input: CreateTrainingPlanCommandInput;
      output: CreateTrainingPlanCommandOutput;
    };
  };
}
