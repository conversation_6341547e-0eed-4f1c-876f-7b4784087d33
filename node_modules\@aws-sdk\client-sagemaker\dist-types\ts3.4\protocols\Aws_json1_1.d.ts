import {
  HttpRequest as __HttpRequest,
  HttpResponse as __HttpResponse,
} from "@smithy/protocol-http";
import { SerdeContext as __SerdeContext } from "@smithy/types";
import {
  AddAssociationCommandInput,
  AddAssociationCommandOutput,
} from "../commands/AddAssociationCommand";
import {
  AddTagsCommandInput,
  AddTagsCommandOutput,
} from "../commands/AddTagsCommand";
import {
  AssociateTrialComponentCommandInput,
  AssociateTrialComponentCommandOutput,
} from "../commands/AssociateTrialComponentCommand";
import {
  BatchDeleteClusterNodesCommandInput,
  BatchDeleteClusterNodesCommandOutput,
} from "../commands/BatchDeleteClusterNodesCommand";
import {
  BatchDescribeModelPackageCommandInput,
  BatchDescribeModelPackageCommandOutput,
} from "../commands/BatchDescribeModelPackageCommand";
import {
  CreateActionCommandInput,
  CreateActionCommandOutput,
} from "../commands/CreateActionCommand";
import {
  CreateAlgorithmCommandInput,
  CreateAlgorithmCommandOutput,
} from "../commands/CreateAlgorithmCommand";
import {
  CreateAppCommandInput,
  CreateAppCommandOutput,
} from "../commands/CreateAppCommand";
import {
  CreateAppImageConfigCommandInput,
  CreateAppImageConfigCommandOutput,
} from "../commands/CreateAppImageConfigCommand";
import {
  CreateArtifactCommandInput,
  CreateArtifactCommandOutput,
} from "../commands/CreateArtifactCommand";
import {
  CreateAutoMLJobCommandInput,
  CreateAutoMLJobCommandOutput,
} from "../commands/CreateAutoMLJobCommand";
import {
  CreateAutoMLJobV2CommandInput,
  CreateAutoMLJobV2CommandOutput,
} from "../commands/CreateAutoMLJobV2Command";
import {
  CreateClusterCommandInput,
  CreateClusterCommandOutput,
} from "../commands/CreateClusterCommand";
import {
  CreateClusterSchedulerConfigCommandInput,
  CreateClusterSchedulerConfigCommandOutput,
} from "../commands/CreateClusterSchedulerConfigCommand";
import {
  CreateCodeRepositoryCommandInput,
  CreateCodeRepositoryCommandOutput,
} from "../commands/CreateCodeRepositoryCommand";
import {
  CreateCompilationJobCommandInput,
  CreateCompilationJobCommandOutput,
} from "../commands/CreateCompilationJobCommand";
import {
  CreateComputeQuotaCommandInput,
  CreateComputeQuotaCommandOutput,
} from "../commands/CreateComputeQuotaCommand";
import {
  CreateContextCommandInput,
  CreateContextCommandOutput,
} from "../commands/CreateContextCommand";
import {
  CreateDataQualityJobDefinitionCommandInput,
  CreateDataQualityJobDefinitionCommandOutput,
} from "../commands/CreateDataQualityJobDefinitionCommand";
import {
  CreateDeviceFleetCommandInput,
  CreateDeviceFleetCommandOutput,
} from "../commands/CreateDeviceFleetCommand";
import {
  CreateDomainCommandInput,
  CreateDomainCommandOutput,
} from "../commands/CreateDomainCommand";
import {
  CreateEdgeDeploymentPlanCommandInput,
  CreateEdgeDeploymentPlanCommandOutput,
} from "../commands/CreateEdgeDeploymentPlanCommand";
import {
  CreateEdgeDeploymentStageCommandInput,
  CreateEdgeDeploymentStageCommandOutput,
} from "../commands/CreateEdgeDeploymentStageCommand";
import {
  CreateEdgePackagingJobCommandInput,
  CreateEdgePackagingJobCommandOutput,
} from "../commands/CreateEdgePackagingJobCommand";
import {
  CreateEndpointCommandInput,
  CreateEndpointCommandOutput,
} from "../commands/CreateEndpointCommand";
import {
  CreateEndpointConfigCommandInput,
  CreateEndpointConfigCommandOutput,
} from "../commands/CreateEndpointConfigCommand";
import {
  CreateExperimentCommandInput,
  CreateExperimentCommandOutput,
} from "../commands/CreateExperimentCommand";
import {
  CreateFeatureGroupCommandInput,
  CreateFeatureGroupCommandOutput,
} from "../commands/CreateFeatureGroupCommand";
import {
  CreateFlowDefinitionCommandInput,
  CreateFlowDefinitionCommandOutput,
} from "../commands/CreateFlowDefinitionCommand";
import {
  CreateHubCommandInput,
  CreateHubCommandOutput,
} from "../commands/CreateHubCommand";
import {
  CreateHubContentReferenceCommandInput,
  CreateHubContentReferenceCommandOutput,
} from "../commands/CreateHubContentReferenceCommand";
import {
  CreateHumanTaskUiCommandInput,
  CreateHumanTaskUiCommandOutput,
} from "../commands/CreateHumanTaskUiCommand";
import {
  CreateHyperParameterTuningJobCommandInput,
  CreateHyperParameterTuningJobCommandOutput,
} from "../commands/CreateHyperParameterTuningJobCommand";
import {
  CreateImageCommandInput,
  CreateImageCommandOutput,
} from "../commands/CreateImageCommand";
import {
  CreateImageVersionCommandInput,
  CreateImageVersionCommandOutput,
} from "../commands/CreateImageVersionCommand";
import {
  CreateInferenceComponentCommandInput,
  CreateInferenceComponentCommandOutput,
} from "../commands/CreateInferenceComponentCommand";
import {
  CreateInferenceExperimentCommandInput,
  CreateInferenceExperimentCommandOutput,
} from "../commands/CreateInferenceExperimentCommand";
import {
  CreateInferenceRecommendationsJobCommandInput,
  CreateInferenceRecommendationsJobCommandOutput,
} from "../commands/CreateInferenceRecommendationsJobCommand";
import {
  CreateLabelingJobCommandInput,
  CreateLabelingJobCommandOutput,
} from "../commands/CreateLabelingJobCommand";
import {
  CreateMlflowTrackingServerCommandInput,
  CreateMlflowTrackingServerCommandOutput,
} from "../commands/CreateMlflowTrackingServerCommand";
import {
  CreateModelBiasJobDefinitionCommandInput,
  CreateModelBiasJobDefinitionCommandOutput,
} from "../commands/CreateModelBiasJobDefinitionCommand";
import {
  CreateModelCardCommandInput,
  CreateModelCardCommandOutput,
} from "../commands/CreateModelCardCommand";
import {
  CreateModelCardExportJobCommandInput,
  CreateModelCardExportJobCommandOutput,
} from "../commands/CreateModelCardExportJobCommand";
import {
  CreateModelCommandInput,
  CreateModelCommandOutput,
} from "../commands/CreateModelCommand";
import {
  CreateModelExplainabilityJobDefinitionCommandInput,
  CreateModelExplainabilityJobDefinitionCommandOutput,
} from "../commands/CreateModelExplainabilityJobDefinitionCommand";
import {
  CreateModelPackageCommandInput,
  CreateModelPackageCommandOutput,
} from "../commands/CreateModelPackageCommand";
import {
  CreateModelPackageGroupCommandInput,
  CreateModelPackageGroupCommandOutput,
} from "../commands/CreateModelPackageGroupCommand";
import {
  CreateModelQualityJobDefinitionCommandInput,
  CreateModelQualityJobDefinitionCommandOutput,
} from "../commands/CreateModelQualityJobDefinitionCommand";
import {
  CreateMonitoringScheduleCommandInput,
  CreateMonitoringScheduleCommandOutput,
} from "../commands/CreateMonitoringScheduleCommand";
import {
  CreateNotebookInstanceCommandInput,
  CreateNotebookInstanceCommandOutput,
} from "../commands/CreateNotebookInstanceCommand";
import {
  CreateNotebookInstanceLifecycleConfigCommandInput,
  CreateNotebookInstanceLifecycleConfigCommandOutput,
} from "../commands/CreateNotebookInstanceLifecycleConfigCommand";
import {
  CreateOptimizationJobCommandInput,
  CreateOptimizationJobCommandOutput,
} from "../commands/CreateOptimizationJobCommand";
import {
  CreatePartnerAppCommandInput,
  CreatePartnerAppCommandOutput,
} from "../commands/CreatePartnerAppCommand";
import {
  CreatePartnerAppPresignedUrlCommandInput,
  CreatePartnerAppPresignedUrlCommandOutput,
} from "../commands/CreatePartnerAppPresignedUrlCommand";
import {
  CreatePipelineCommandInput,
  CreatePipelineCommandOutput,
} from "../commands/CreatePipelineCommand";
import {
  CreatePresignedDomainUrlCommandInput,
  CreatePresignedDomainUrlCommandOutput,
} from "../commands/CreatePresignedDomainUrlCommand";
import {
  CreatePresignedMlflowTrackingServerUrlCommandInput,
  CreatePresignedMlflowTrackingServerUrlCommandOutput,
} from "../commands/CreatePresignedMlflowTrackingServerUrlCommand";
import {
  CreatePresignedNotebookInstanceUrlCommandInput,
  CreatePresignedNotebookInstanceUrlCommandOutput,
} from "../commands/CreatePresignedNotebookInstanceUrlCommand";
import {
  CreateProcessingJobCommandInput,
  CreateProcessingJobCommandOutput,
} from "../commands/CreateProcessingJobCommand";
import {
  CreateProjectCommandInput,
  CreateProjectCommandOutput,
} from "../commands/CreateProjectCommand";
import {
  CreateSpaceCommandInput,
  CreateSpaceCommandOutput,
} from "../commands/CreateSpaceCommand";
import {
  CreateStudioLifecycleConfigCommandInput,
  CreateStudioLifecycleConfigCommandOutput,
} from "../commands/CreateStudioLifecycleConfigCommand";
import {
  CreateTrainingJobCommandInput,
  CreateTrainingJobCommandOutput,
} from "../commands/CreateTrainingJobCommand";
import {
  CreateTrainingPlanCommandInput,
  CreateTrainingPlanCommandOutput,
} from "../commands/CreateTrainingPlanCommand";
import {
  CreateTransformJobCommandInput,
  CreateTransformJobCommandOutput,
} from "../commands/CreateTransformJobCommand";
import {
  CreateTrialCommandInput,
  CreateTrialCommandOutput,
} from "../commands/CreateTrialCommand";
import {
  CreateTrialComponentCommandInput,
  CreateTrialComponentCommandOutput,
} from "../commands/CreateTrialComponentCommand";
import {
  CreateUserProfileCommandInput,
  CreateUserProfileCommandOutput,
} from "../commands/CreateUserProfileCommand";
import {
  CreateWorkforceCommandInput,
  CreateWorkforceCommandOutput,
} from "../commands/CreateWorkforceCommand";
import {
  CreateWorkteamCommandInput,
  CreateWorkteamCommandOutput,
} from "../commands/CreateWorkteamCommand";
import {
  DeleteActionCommandInput,
  DeleteActionCommandOutput,
} from "../commands/DeleteActionCommand";
import {
  DeleteAlgorithmCommandInput,
  DeleteAlgorithmCommandOutput,
} from "../commands/DeleteAlgorithmCommand";
import {
  DeleteAppCommandInput,
  DeleteAppCommandOutput,
} from "../commands/DeleteAppCommand";
import {
  DeleteAppImageConfigCommandInput,
  DeleteAppImageConfigCommandOutput,
} from "../commands/DeleteAppImageConfigCommand";
import {
  DeleteArtifactCommandInput,
  DeleteArtifactCommandOutput,
} from "../commands/DeleteArtifactCommand";
import {
  DeleteAssociationCommandInput,
  DeleteAssociationCommandOutput,
} from "../commands/DeleteAssociationCommand";
import {
  DeleteClusterCommandInput,
  DeleteClusterCommandOutput,
} from "../commands/DeleteClusterCommand";
import {
  DeleteClusterSchedulerConfigCommandInput,
  DeleteClusterSchedulerConfigCommandOutput,
} from "../commands/DeleteClusterSchedulerConfigCommand";
import {
  DeleteCodeRepositoryCommandInput,
  DeleteCodeRepositoryCommandOutput,
} from "../commands/DeleteCodeRepositoryCommand";
import {
  DeleteCompilationJobCommandInput,
  DeleteCompilationJobCommandOutput,
} from "../commands/DeleteCompilationJobCommand";
import {
  DeleteComputeQuotaCommandInput,
  DeleteComputeQuotaCommandOutput,
} from "../commands/DeleteComputeQuotaCommand";
import {
  DeleteContextCommandInput,
  DeleteContextCommandOutput,
} from "../commands/DeleteContextCommand";
import {
  DeleteDataQualityJobDefinitionCommandInput,
  DeleteDataQualityJobDefinitionCommandOutput,
} from "../commands/DeleteDataQualityJobDefinitionCommand";
import {
  DeleteDeviceFleetCommandInput,
  DeleteDeviceFleetCommandOutput,
} from "../commands/DeleteDeviceFleetCommand";
import {
  DeleteDomainCommandInput,
  DeleteDomainCommandOutput,
} from "../commands/DeleteDomainCommand";
import {
  DeleteEdgeDeploymentPlanCommandInput,
  DeleteEdgeDeploymentPlanCommandOutput,
} from "../commands/DeleteEdgeDeploymentPlanCommand";
import {
  DeleteEdgeDeploymentStageCommandInput,
  DeleteEdgeDeploymentStageCommandOutput,
} from "../commands/DeleteEdgeDeploymentStageCommand";
import {
  DeleteEndpointCommandInput,
  DeleteEndpointCommandOutput,
} from "../commands/DeleteEndpointCommand";
import {
  DeleteEndpointConfigCommandInput,
  DeleteEndpointConfigCommandOutput,
} from "../commands/DeleteEndpointConfigCommand";
import {
  DeleteExperimentCommandInput,
  DeleteExperimentCommandOutput,
} from "../commands/DeleteExperimentCommand";
import {
  DeleteFeatureGroupCommandInput,
  DeleteFeatureGroupCommandOutput,
} from "../commands/DeleteFeatureGroupCommand";
import {
  DeleteFlowDefinitionCommandInput,
  DeleteFlowDefinitionCommandOutput,
} from "../commands/DeleteFlowDefinitionCommand";
import {
  DeleteHubCommandInput,
  DeleteHubCommandOutput,
} from "../commands/DeleteHubCommand";
import {
  DeleteHubContentCommandInput,
  DeleteHubContentCommandOutput,
} from "../commands/DeleteHubContentCommand";
import {
  DeleteHubContentReferenceCommandInput,
  DeleteHubContentReferenceCommandOutput,
} from "../commands/DeleteHubContentReferenceCommand";
import {
  DeleteHumanTaskUiCommandInput,
  DeleteHumanTaskUiCommandOutput,
} from "../commands/DeleteHumanTaskUiCommand";
import {
  DeleteHyperParameterTuningJobCommandInput,
  DeleteHyperParameterTuningJobCommandOutput,
} from "../commands/DeleteHyperParameterTuningJobCommand";
import {
  DeleteImageCommandInput,
  DeleteImageCommandOutput,
} from "../commands/DeleteImageCommand";
import {
  DeleteImageVersionCommandInput,
  DeleteImageVersionCommandOutput,
} from "../commands/DeleteImageVersionCommand";
import {
  DeleteInferenceComponentCommandInput,
  DeleteInferenceComponentCommandOutput,
} from "../commands/DeleteInferenceComponentCommand";
import {
  DeleteInferenceExperimentCommandInput,
  DeleteInferenceExperimentCommandOutput,
} from "../commands/DeleteInferenceExperimentCommand";
import {
  DeleteMlflowTrackingServerCommandInput,
  DeleteMlflowTrackingServerCommandOutput,
} from "../commands/DeleteMlflowTrackingServerCommand";
import {
  DeleteModelBiasJobDefinitionCommandInput,
  DeleteModelBiasJobDefinitionCommandOutput,
} from "../commands/DeleteModelBiasJobDefinitionCommand";
import {
  DeleteModelCardCommandInput,
  DeleteModelCardCommandOutput,
} from "../commands/DeleteModelCardCommand";
import {
  DeleteModelCommandInput,
  DeleteModelCommandOutput,
} from "../commands/DeleteModelCommand";
import {
  DeleteModelExplainabilityJobDefinitionCommandInput,
  DeleteModelExplainabilityJobDefinitionCommandOutput,
} from "../commands/DeleteModelExplainabilityJobDefinitionCommand";
import {
  DeleteModelPackageCommandInput,
  DeleteModelPackageCommandOutput,
} from "../commands/DeleteModelPackageCommand";
import {
  DeleteModelPackageGroupCommandInput,
  DeleteModelPackageGroupCommandOutput,
} from "../commands/DeleteModelPackageGroupCommand";
import {
  DeleteModelPackageGroupPolicyCommandInput,
  DeleteModelPackageGroupPolicyCommandOutput,
} from "../commands/DeleteModelPackageGroupPolicyCommand";
import {
  DeleteModelQualityJobDefinitionCommandInput,
  DeleteModelQualityJobDefinitionCommandOutput,
} from "../commands/DeleteModelQualityJobDefinitionCommand";
import {
  DeleteMonitoringScheduleCommandInput,
  DeleteMonitoringScheduleCommandOutput,
} from "../commands/DeleteMonitoringScheduleCommand";
import {
  DeleteNotebookInstanceCommandInput,
  DeleteNotebookInstanceCommandOutput,
} from "../commands/DeleteNotebookInstanceCommand";
import {
  DeleteNotebookInstanceLifecycleConfigCommandInput,
  DeleteNotebookInstanceLifecycleConfigCommandOutput,
} from "../commands/DeleteNotebookInstanceLifecycleConfigCommand";
import {
  DeleteOptimizationJobCommandInput,
  DeleteOptimizationJobCommandOutput,
} from "../commands/DeleteOptimizationJobCommand";
import {
  DeletePartnerAppCommandInput,
  DeletePartnerAppCommandOutput,
} from "../commands/DeletePartnerAppCommand";
import {
  DeletePipelineCommandInput,
  DeletePipelineCommandOutput,
} from "../commands/DeletePipelineCommand";
import {
  DeleteProjectCommandInput,
  DeleteProjectCommandOutput,
} from "../commands/DeleteProjectCommand";
import {
  DeleteSpaceCommandInput,
  DeleteSpaceCommandOutput,
} from "../commands/DeleteSpaceCommand";
import {
  DeleteStudioLifecycleConfigCommandInput,
  DeleteStudioLifecycleConfigCommandOutput,
} from "../commands/DeleteStudioLifecycleConfigCommand";
import {
  DeleteTagsCommandInput,
  DeleteTagsCommandOutput,
} from "../commands/DeleteTagsCommand";
import {
  DeleteTrialCommandInput,
  DeleteTrialCommandOutput,
} from "../commands/DeleteTrialCommand";
import {
  DeleteTrialComponentCommandInput,
  DeleteTrialComponentCommandOutput,
} from "../commands/DeleteTrialComponentCommand";
import {
  DeleteUserProfileCommandInput,
  DeleteUserProfileCommandOutput,
} from "../commands/DeleteUserProfileCommand";
import {
  DeleteWorkforceCommandInput,
  DeleteWorkforceCommandOutput,
} from "../commands/DeleteWorkforceCommand";
import {
  DeleteWorkteamCommandInput,
  DeleteWorkteamCommandOutput,
} from "../commands/DeleteWorkteamCommand";
import {
  DeregisterDevicesCommandInput,
  DeregisterDevicesCommandOutput,
} from "../commands/DeregisterDevicesCommand";
import {
  DescribeActionCommandInput,
  DescribeActionCommandOutput,
} from "../commands/DescribeActionCommand";
import {
  DescribeAlgorithmCommandInput,
  DescribeAlgorithmCommandOutput,
} from "../commands/DescribeAlgorithmCommand";
import {
  DescribeAppCommandInput,
  DescribeAppCommandOutput,
} from "../commands/DescribeAppCommand";
import {
  DescribeAppImageConfigCommandInput,
  DescribeAppImageConfigCommandOutput,
} from "../commands/DescribeAppImageConfigCommand";
import {
  DescribeArtifactCommandInput,
  DescribeArtifactCommandOutput,
} from "../commands/DescribeArtifactCommand";
import {
  DescribeAutoMLJobCommandInput,
  DescribeAutoMLJobCommandOutput,
} from "../commands/DescribeAutoMLJobCommand";
import {
  DescribeAutoMLJobV2CommandInput,
  DescribeAutoMLJobV2CommandOutput,
} from "../commands/DescribeAutoMLJobV2Command";
import {
  DescribeClusterCommandInput,
  DescribeClusterCommandOutput,
} from "../commands/DescribeClusterCommand";
import {
  DescribeClusterNodeCommandInput,
  DescribeClusterNodeCommandOutput,
} from "../commands/DescribeClusterNodeCommand";
import {
  DescribeClusterSchedulerConfigCommandInput,
  DescribeClusterSchedulerConfigCommandOutput,
} from "../commands/DescribeClusterSchedulerConfigCommand";
import {
  DescribeCodeRepositoryCommandInput,
  DescribeCodeRepositoryCommandOutput,
} from "../commands/DescribeCodeRepositoryCommand";
import {
  DescribeCompilationJobCommandInput,
  DescribeCompilationJobCommandOutput,
} from "../commands/DescribeCompilationJobCommand";
import {
  DescribeComputeQuotaCommandInput,
  DescribeComputeQuotaCommandOutput,
} from "../commands/DescribeComputeQuotaCommand";
import {
  DescribeContextCommandInput,
  DescribeContextCommandOutput,
} from "../commands/DescribeContextCommand";
import {
  DescribeDataQualityJobDefinitionCommandInput,
  DescribeDataQualityJobDefinitionCommandOutput,
} from "../commands/DescribeDataQualityJobDefinitionCommand";
import {
  DescribeDeviceCommandInput,
  DescribeDeviceCommandOutput,
} from "../commands/DescribeDeviceCommand";
import {
  DescribeDeviceFleetCommandInput,
  DescribeDeviceFleetCommandOutput,
} from "../commands/DescribeDeviceFleetCommand";
import {
  DescribeDomainCommandInput,
  DescribeDomainCommandOutput,
} from "../commands/DescribeDomainCommand";
import {
  DescribeEdgeDeploymentPlanCommandInput,
  DescribeEdgeDeploymentPlanCommandOutput,
} from "../commands/DescribeEdgeDeploymentPlanCommand";
import {
  DescribeEdgePackagingJobCommandInput,
  DescribeEdgePackagingJobCommandOutput,
} from "../commands/DescribeEdgePackagingJobCommand";
import {
  DescribeEndpointCommandInput,
  DescribeEndpointCommandOutput,
} from "../commands/DescribeEndpointCommand";
import {
  DescribeEndpointConfigCommandInput,
  DescribeEndpointConfigCommandOutput,
} from "../commands/DescribeEndpointConfigCommand";
import {
  DescribeExperimentCommandInput,
  DescribeExperimentCommandOutput,
} from "../commands/DescribeExperimentCommand";
import {
  DescribeFeatureGroupCommandInput,
  DescribeFeatureGroupCommandOutput,
} from "../commands/DescribeFeatureGroupCommand";
import {
  DescribeFeatureMetadataCommandInput,
  DescribeFeatureMetadataCommandOutput,
} from "../commands/DescribeFeatureMetadataCommand";
import {
  DescribeFlowDefinitionCommandInput,
  DescribeFlowDefinitionCommandOutput,
} from "../commands/DescribeFlowDefinitionCommand";
import {
  DescribeHubCommandInput,
  DescribeHubCommandOutput,
} from "../commands/DescribeHubCommand";
import {
  DescribeHubContentCommandInput,
  DescribeHubContentCommandOutput,
} from "../commands/DescribeHubContentCommand";
import {
  DescribeHumanTaskUiCommandInput,
  DescribeHumanTaskUiCommandOutput,
} from "../commands/DescribeHumanTaskUiCommand";
import {
  DescribeHyperParameterTuningJobCommandInput,
  DescribeHyperParameterTuningJobCommandOutput,
} from "../commands/DescribeHyperParameterTuningJobCommand";
import {
  DescribeImageCommandInput,
  DescribeImageCommandOutput,
} from "../commands/DescribeImageCommand";
import {
  DescribeImageVersionCommandInput,
  DescribeImageVersionCommandOutput,
} from "../commands/DescribeImageVersionCommand";
import {
  DescribeInferenceComponentCommandInput,
  DescribeInferenceComponentCommandOutput,
} from "../commands/DescribeInferenceComponentCommand";
import {
  DescribeInferenceExperimentCommandInput,
  DescribeInferenceExperimentCommandOutput,
} from "../commands/DescribeInferenceExperimentCommand";
import {
  DescribeInferenceRecommendationsJobCommandInput,
  DescribeInferenceRecommendationsJobCommandOutput,
} from "../commands/DescribeInferenceRecommendationsJobCommand";
import {
  DescribeLabelingJobCommandInput,
  DescribeLabelingJobCommandOutput,
} from "../commands/DescribeLabelingJobCommand";
import {
  DescribeLineageGroupCommandInput,
  DescribeLineageGroupCommandOutput,
} from "../commands/DescribeLineageGroupCommand";
import {
  DescribeMlflowTrackingServerCommandInput,
  DescribeMlflowTrackingServerCommandOutput,
} from "../commands/DescribeMlflowTrackingServerCommand";
import {
  DescribeModelBiasJobDefinitionCommandInput,
  DescribeModelBiasJobDefinitionCommandOutput,
} from "../commands/DescribeModelBiasJobDefinitionCommand";
import {
  DescribeModelCardCommandInput,
  DescribeModelCardCommandOutput,
} from "../commands/DescribeModelCardCommand";
import {
  DescribeModelCardExportJobCommandInput,
  DescribeModelCardExportJobCommandOutput,
} from "../commands/DescribeModelCardExportJobCommand";
import {
  DescribeModelCommandInput,
  DescribeModelCommandOutput,
} from "../commands/DescribeModelCommand";
import {
  DescribeModelExplainabilityJobDefinitionCommandInput,
  DescribeModelExplainabilityJobDefinitionCommandOutput,
} from "../commands/DescribeModelExplainabilityJobDefinitionCommand";
import {
  DescribeModelPackageCommandInput,
  DescribeModelPackageCommandOutput,
} from "../commands/DescribeModelPackageCommand";
import {
  DescribeModelPackageGroupCommandInput,
  DescribeModelPackageGroupCommandOutput,
} from "../commands/DescribeModelPackageGroupCommand";
import {
  DescribeModelQualityJobDefinitionCommandInput,
  DescribeModelQualityJobDefinitionCommandOutput,
} from "../commands/DescribeModelQualityJobDefinitionCommand";
import {
  DescribeMonitoringScheduleCommandInput,
  DescribeMonitoringScheduleCommandOutput,
} from "../commands/DescribeMonitoringScheduleCommand";
import {
  DescribeNotebookInstanceCommandInput,
  DescribeNotebookInstanceCommandOutput,
} from "../commands/DescribeNotebookInstanceCommand";
import {
  DescribeNotebookInstanceLifecycleConfigCommandInput,
  DescribeNotebookInstanceLifecycleConfigCommandOutput,
} from "../commands/DescribeNotebookInstanceLifecycleConfigCommand";
import {
  DescribeOptimizationJobCommandInput,
  DescribeOptimizationJobCommandOutput,
} from "../commands/DescribeOptimizationJobCommand";
import {
  DescribePartnerAppCommandInput,
  DescribePartnerAppCommandOutput,
} from "../commands/DescribePartnerAppCommand";
import {
  DescribePipelineCommandInput,
  DescribePipelineCommandOutput,
} from "../commands/DescribePipelineCommand";
import {
  DescribePipelineDefinitionForExecutionCommandInput,
  DescribePipelineDefinitionForExecutionCommandOutput,
} from "../commands/DescribePipelineDefinitionForExecutionCommand";
import {
  DescribePipelineExecutionCommandInput,
  DescribePipelineExecutionCommandOutput,
} from "../commands/DescribePipelineExecutionCommand";
import {
  DescribeProcessingJobCommandInput,
  DescribeProcessingJobCommandOutput,
} from "../commands/DescribeProcessingJobCommand";
import {
  DescribeProjectCommandInput,
  DescribeProjectCommandOutput,
} from "../commands/DescribeProjectCommand";
import {
  DescribeSpaceCommandInput,
  DescribeSpaceCommandOutput,
} from "../commands/DescribeSpaceCommand";
import {
  DescribeStudioLifecycleConfigCommandInput,
  DescribeStudioLifecycleConfigCommandOutput,
} from "../commands/DescribeStudioLifecycleConfigCommand";
import {
  DescribeSubscribedWorkteamCommandInput,
  DescribeSubscribedWorkteamCommandOutput,
} from "../commands/DescribeSubscribedWorkteamCommand";
import {
  DescribeTrainingJobCommandInput,
  DescribeTrainingJobCommandOutput,
} from "../commands/DescribeTrainingJobCommand";
import {
  DescribeTrainingPlanCommandInput,
  DescribeTrainingPlanCommandOutput,
} from "../commands/DescribeTrainingPlanCommand";
import {
  DescribeTransformJobCommandInput,
  DescribeTransformJobCommandOutput,
} from "../commands/DescribeTransformJobCommand";
import {
  DescribeTrialCommandInput,
  DescribeTrialCommandOutput,
} from "../commands/DescribeTrialCommand";
import {
  DescribeTrialComponentCommandInput,
  DescribeTrialComponentCommandOutput,
} from "../commands/DescribeTrialComponentCommand";
import {
  DescribeUserProfileCommandInput,
  DescribeUserProfileCommandOutput,
} from "../commands/DescribeUserProfileCommand";
import {
  DescribeWorkforceCommandInput,
  DescribeWorkforceCommandOutput,
} from "../commands/DescribeWorkforceCommand";
import {
  DescribeWorkteamCommandInput,
  DescribeWorkteamCommandOutput,
} from "../commands/DescribeWorkteamCommand";
import {
  DisableSagemakerServicecatalogPortfolioCommandInput,
  DisableSagemakerServicecatalogPortfolioCommandOutput,
} from "../commands/DisableSagemakerServicecatalogPortfolioCommand";
import {
  DisassociateTrialComponentCommandInput,
  DisassociateTrialComponentCommandOutput,
} from "../commands/DisassociateTrialComponentCommand";
import {
  EnableSagemakerServicecatalogPortfolioCommandInput,
  EnableSagemakerServicecatalogPortfolioCommandOutput,
} from "../commands/EnableSagemakerServicecatalogPortfolioCommand";
import {
  GetDeviceFleetReportCommandInput,
  GetDeviceFleetReportCommandOutput,
} from "../commands/GetDeviceFleetReportCommand";
import {
  GetLineageGroupPolicyCommandInput,
  GetLineageGroupPolicyCommandOutput,
} from "../commands/GetLineageGroupPolicyCommand";
import {
  GetModelPackageGroupPolicyCommandInput,
  GetModelPackageGroupPolicyCommandOutput,
} from "../commands/GetModelPackageGroupPolicyCommand";
import {
  GetSagemakerServicecatalogPortfolioStatusCommandInput,
  GetSagemakerServicecatalogPortfolioStatusCommandOutput,
} from "../commands/GetSagemakerServicecatalogPortfolioStatusCommand";
import {
  GetScalingConfigurationRecommendationCommandInput,
  GetScalingConfigurationRecommendationCommandOutput,
} from "../commands/GetScalingConfigurationRecommendationCommand";
import {
  GetSearchSuggestionsCommandInput,
  GetSearchSuggestionsCommandOutput,
} from "../commands/GetSearchSuggestionsCommand";
import {
  ImportHubContentCommandInput,
  ImportHubContentCommandOutput,
} from "../commands/ImportHubContentCommand";
import {
  ListActionsCommandInput,
  ListActionsCommandOutput,
} from "../commands/ListActionsCommand";
import {
  ListAlgorithmsCommandInput,
  ListAlgorithmsCommandOutput,
} from "../commands/ListAlgorithmsCommand";
import {
  ListAliasesCommandInput,
  ListAliasesCommandOutput,
} from "../commands/ListAliasesCommand";
import {
  ListAppImageConfigsCommandInput,
  ListAppImageConfigsCommandOutput,
} from "../commands/ListAppImageConfigsCommand";
import {
  ListAppsCommandInput,
  ListAppsCommandOutput,
} from "../commands/ListAppsCommand";
import {
  ListArtifactsCommandInput,
  ListArtifactsCommandOutput,
} from "../commands/ListArtifactsCommand";
import {
  ListAssociationsCommandInput,
  ListAssociationsCommandOutput,
} from "../commands/ListAssociationsCommand";
import {
  ListAutoMLJobsCommandInput,
  ListAutoMLJobsCommandOutput,
} from "../commands/ListAutoMLJobsCommand";
import {
  ListCandidatesForAutoMLJobCommandInput,
  ListCandidatesForAutoMLJobCommandOutput,
} from "../commands/ListCandidatesForAutoMLJobCommand";
import {
  ListClusterNodesCommandInput,
  ListClusterNodesCommandOutput,
} from "../commands/ListClusterNodesCommand";
import {
  ListClusterSchedulerConfigsCommandInput,
  ListClusterSchedulerConfigsCommandOutput,
} from "../commands/ListClusterSchedulerConfigsCommand";
import {
  ListClustersCommandInput,
  ListClustersCommandOutput,
} from "../commands/ListClustersCommand";
import {
  ListCodeRepositoriesCommandInput,
  ListCodeRepositoriesCommandOutput,
} from "../commands/ListCodeRepositoriesCommand";
import {
  ListCompilationJobsCommandInput,
  ListCompilationJobsCommandOutput,
} from "../commands/ListCompilationJobsCommand";
import {
  ListComputeQuotasCommandInput,
  ListComputeQuotasCommandOutput,
} from "../commands/ListComputeQuotasCommand";
import {
  ListContextsCommandInput,
  ListContextsCommandOutput,
} from "../commands/ListContextsCommand";
import {
  ListDataQualityJobDefinitionsCommandInput,
  ListDataQualityJobDefinitionsCommandOutput,
} from "../commands/ListDataQualityJobDefinitionsCommand";
import {
  ListDeviceFleetsCommandInput,
  ListDeviceFleetsCommandOutput,
} from "../commands/ListDeviceFleetsCommand";
import {
  ListDevicesCommandInput,
  ListDevicesCommandOutput,
} from "../commands/ListDevicesCommand";
import {
  ListDomainsCommandInput,
  ListDomainsCommandOutput,
} from "../commands/ListDomainsCommand";
import {
  ListEdgeDeploymentPlansCommandInput,
  ListEdgeDeploymentPlansCommandOutput,
} from "../commands/ListEdgeDeploymentPlansCommand";
import {
  ListEdgePackagingJobsCommandInput,
  ListEdgePackagingJobsCommandOutput,
} from "../commands/ListEdgePackagingJobsCommand";
import {
  ListEndpointConfigsCommandInput,
  ListEndpointConfigsCommandOutput,
} from "../commands/ListEndpointConfigsCommand";
import {
  ListEndpointsCommandInput,
  ListEndpointsCommandOutput,
} from "../commands/ListEndpointsCommand";
import {
  ListExperimentsCommandInput,
  ListExperimentsCommandOutput,
} from "../commands/ListExperimentsCommand";
import {
  ListFeatureGroupsCommandInput,
  ListFeatureGroupsCommandOutput,
} from "../commands/ListFeatureGroupsCommand";
import {
  ListFlowDefinitionsCommandInput,
  ListFlowDefinitionsCommandOutput,
} from "../commands/ListFlowDefinitionsCommand";
import {
  ListHubContentsCommandInput,
  ListHubContentsCommandOutput,
} from "../commands/ListHubContentsCommand";
import {
  ListHubContentVersionsCommandInput,
  ListHubContentVersionsCommandOutput,
} from "../commands/ListHubContentVersionsCommand";
import {
  ListHubsCommandInput,
  ListHubsCommandOutput,
} from "../commands/ListHubsCommand";
import {
  ListHumanTaskUisCommandInput,
  ListHumanTaskUisCommandOutput,
} from "../commands/ListHumanTaskUisCommand";
import {
  ListHyperParameterTuningJobsCommandInput,
  ListHyperParameterTuningJobsCommandOutput,
} from "../commands/ListHyperParameterTuningJobsCommand";
import {
  ListImagesCommandInput,
  ListImagesCommandOutput,
} from "../commands/ListImagesCommand";
import {
  ListImageVersionsCommandInput,
  ListImageVersionsCommandOutput,
} from "../commands/ListImageVersionsCommand";
import {
  ListInferenceComponentsCommandInput,
  ListInferenceComponentsCommandOutput,
} from "../commands/ListInferenceComponentsCommand";
import {
  ListInferenceExperimentsCommandInput,
  ListInferenceExperimentsCommandOutput,
} from "../commands/ListInferenceExperimentsCommand";
import {
  ListInferenceRecommendationsJobsCommandInput,
  ListInferenceRecommendationsJobsCommandOutput,
} from "../commands/ListInferenceRecommendationsJobsCommand";
import {
  ListInferenceRecommendationsJobStepsCommandInput,
  ListInferenceRecommendationsJobStepsCommandOutput,
} from "../commands/ListInferenceRecommendationsJobStepsCommand";
import {
  ListLabelingJobsCommandInput,
  ListLabelingJobsCommandOutput,
} from "../commands/ListLabelingJobsCommand";
import {
  ListLabelingJobsForWorkteamCommandInput,
  ListLabelingJobsForWorkteamCommandOutput,
} from "../commands/ListLabelingJobsForWorkteamCommand";
import {
  ListLineageGroupsCommandInput,
  ListLineageGroupsCommandOutput,
} from "../commands/ListLineageGroupsCommand";
import {
  ListMlflowTrackingServersCommandInput,
  ListMlflowTrackingServersCommandOutput,
} from "../commands/ListMlflowTrackingServersCommand";
import {
  ListModelBiasJobDefinitionsCommandInput,
  ListModelBiasJobDefinitionsCommandOutput,
} from "../commands/ListModelBiasJobDefinitionsCommand";
import {
  ListModelCardExportJobsCommandInput,
  ListModelCardExportJobsCommandOutput,
} from "../commands/ListModelCardExportJobsCommand";
import {
  ListModelCardsCommandInput,
  ListModelCardsCommandOutput,
} from "../commands/ListModelCardsCommand";
import {
  ListModelCardVersionsCommandInput,
  ListModelCardVersionsCommandOutput,
} from "../commands/ListModelCardVersionsCommand";
import {
  ListModelExplainabilityJobDefinitionsCommandInput,
  ListModelExplainabilityJobDefinitionsCommandOutput,
} from "../commands/ListModelExplainabilityJobDefinitionsCommand";
import {
  ListModelMetadataCommandInput,
  ListModelMetadataCommandOutput,
} from "../commands/ListModelMetadataCommand";
import {
  ListModelPackageGroupsCommandInput,
  ListModelPackageGroupsCommandOutput,
} from "../commands/ListModelPackageGroupsCommand";
import {
  ListModelPackagesCommandInput,
  ListModelPackagesCommandOutput,
} from "../commands/ListModelPackagesCommand";
import {
  ListModelQualityJobDefinitionsCommandInput,
  ListModelQualityJobDefinitionsCommandOutput,
} from "../commands/ListModelQualityJobDefinitionsCommand";
import {
  ListModelsCommandInput,
  ListModelsCommandOutput,
} from "../commands/ListModelsCommand";
import {
  ListMonitoringAlertHistoryCommandInput,
  ListMonitoringAlertHistoryCommandOutput,
} from "../commands/ListMonitoringAlertHistoryCommand";
import {
  ListMonitoringAlertsCommandInput,
  ListMonitoringAlertsCommandOutput,
} from "../commands/ListMonitoringAlertsCommand";
import {
  ListMonitoringExecutionsCommandInput,
  ListMonitoringExecutionsCommandOutput,
} from "../commands/ListMonitoringExecutionsCommand";
import {
  ListMonitoringSchedulesCommandInput,
  ListMonitoringSchedulesCommandOutput,
} from "../commands/ListMonitoringSchedulesCommand";
import {
  ListNotebookInstanceLifecycleConfigsCommandInput,
  ListNotebookInstanceLifecycleConfigsCommandOutput,
} from "../commands/ListNotebookInstanceLifecycleConfigsCommand";
import {
  ListNotebookInstancesCommandInput,
  ListNotebookInstancesCommandOutput,
} from "../commands/ListNotebookInstancesCommand";
import {
  ListOptimizationJobsCommandInput,
  ListOptimizationJobsCommandOutput,
} from "../commands/ListOptimizationJobsCommand";
import {
  ListPartnerAppsCommandInput,
  ListPartnerAppsCommandOutput,
} from "../commands/ListPartnerAppsCommand";
import {
  ListPipelineExecutionsCommandInput,
  ListPipelineExecutionsCommandOutput,
} from "../commands/ListPipelineExecutionsCommand";
import {
  ListPipelineExecutionStepsCommandInput,
  ListPipelineExecutionStepsCommandOutput,
} from "../commands/ListPipelineExecutionStepsCommand";
import {
  ListPipelineParametersForExecutionCommandInput,
  ListPipelineParametersForExecutionCommandOutput,
} from "../commands/ListPipelineParametersForExecutionCommand";
import {
  ListPipelinesCommandInput,
  ListPipelinesCommandOutput,
} from "../commands/ListPipelinesCommand";
import {
  ListProcessingJobsCommandInput,
  ListProcessingJobsCommandOutput,
} from "../commands/ListProcessingJobsCommand";
import {
  ListProjectsCommandInput,
  ListProjectsCommandOutput,
} from "../commands/ListProjectsCommand";
import {
  ListResourceCatalogsCommandInput,
  ListResourceCatalogsCommandOutput,
} from "../commands/ListResourceCatalogsCommand";
import {
  ListSpacesCommandInput,
  ListSpacesCommandOutput,
} from "../commands/ListSpacesCommand";
import {
  ListStageDevicesCommandInput,
  ListStageDevicesCommandOutput,
} from "../commands/ListStageDevicesCommand";
import {
  ListStudioLifecycleConfigsCommandInput,
  ListStudioLifecycleConfigsCommandOutput,
} from "../commands/ListStudioLifecycleConfigsCommand";
import {
  ListSubscribedWorkteamsCommandInput,
  ListSubscribedWorkteamsCommandOutput,
} from "../commands/ListSubscribedWorkteamsCommand";
import {
  ListTagsCommandInput,
  ListTagsCommandOutput,
} from "../commands/ListTagsCommand";
import {
  ListTrainingJobsCommandInput,
  ListTrainingJobsCommandOutput,
} from "../commands/ListTrainingJobsCommand";
import {
  ListTrainingJobsForHyperParameterTuningJobCommandInput,
  ListTrainingJobsForHyperParameterTuningJobCommandOutput,
} from "../commands/ListTrainingJobsForHyperParameterTuningJobCommand";
import {
  ListTrainingPlansCommandInput,
  ListTrainingPlansCommandOutput,
} from "../commands/ListTrainingPlansCommand";
import {
  ListTransformJobsCommandInput,
  ListTransformJobsCommandOutput,
} from "../commands/ListTransformJobsCommand";
import {
  ListTrialComponentsCommandInput,
  ListTrialComponentsCommandOutput,
} from "../commands/ListTrialComponentsCommand";
import {
  ListTrialsCommandInput,
  ListTrialsCommandOutput,
} from "../commands/ListTrialsCommand";
import {
  ListUserProfilesCommandInput,
  ListUserProfilesCommandOutput,
} from "../commands/ListUserProfilesCommand";
import {
  ListWorkforcesCommandInput,
  ListWorkforcesCommandOutput,
} from "../commands/ListWorkforcesCommand";
import {
  ListWorkteamsCommandInput,
  ListWorkteamsCommandOutput,
} from "../commands/ListWorkteamsCommand";
import {
  PutModelPackageGroupPolicyCommandInput,
  PutModelPackageGroupPolicyCommandOutput,
} from "../commands/PutModelPackageGroupPolicyCommand";
import {
  QueryLineageCommandInput,
  QueryLineageCommandOutput,
} from "../commands/QueryLineageCommand";
import {
  RegisterDevicesCommandInput,
  RegisterDevicesCommandOutput,
} from "../commands/RegisterDevicesCommand";
import {
  RenderUiTemplateCommandInput,
  RenderUiTemplateCommandOutput,
} from "../commands/RenderUiTemplateCommand";
import {
  RetryPipelineExecutionCommandInput,
  RetryPipelineExecutionCommandOutput,
} from "../commands/RetryPipelineExecutionCommand";
import {
  SearchCommandInput,
  SearchCommandOutput,
} from "../commands/SearchCommand";
import {
  SearchTrainingPlanOfferingsCommandInput,
  SearchTrainingPlanOfferingsCommandOutput,
} from "../commands/SearchTrainingPlanOfferingsCommand";
import {
  SendPipelineExecutionStepFailureCommandInput,
  SendPipelineExecutionStepFailureCommandOutput,
} from "../commands/SendPipelineExecutionStepFailureCommand";
import {
  SendPipelineExecutionStepSuccessCommandInput,
  SendPipelineExecutionStepSuccessCommandOutput,
} from "../commands/SendPipelineExecutionStepSuccessCommand";
import {
  StartEdgeDeploymentStageCommandInput,
  StartEdgeDeploymentStageCommandOutput,
} from "../commands/StartEdgeDeploymentStageCommand";
import {
  StartInferenceExperimentCommandInput,
  StartInferenceExperimentCommandOutput,
} from "../commands/StartInferenceExperimentCommand";
import {
  StartMlflowTrackingServerCommandInput,
  StartMlflowTrackingServerCommandOutput,
} from "../commands/StartMlflowTrackingServerCommand";
import {
  StartMonitoringScheduleCommandInput,
  StartMonitoringScheduleCommandOutput,
} from "../commands/StartMonitoringScheduleCommand";
import {
  StartNotebookInstanceCommandInput,
  StartNotebookInstanceCommandOutput,
} from "../commands/StartNotebookInstanceCommand";
import {
  StartPipelineExecutionCommandInput,
  StartPipelineExecutionCommandOutput,
} from "../commands/StartPipelineExecutionCommand";
import {
  StopAutoMLJobCommandInput,
  StopAutoMLJobCommandOutput,
} from "../commands/StopAutoMLJobCommand";
import {
  StopCompilationJobCommandInput,
  StopCompilationJobCommandOutput,
} from "../commands/StopCompilationJobCommand";
import {
  StopEdgeDeploymentStageCommandInput,
  StopEdgeDeploymentStageCommandOutput,
} from "../commands/StopEdgeDeploymentStageCommand";
import {
  StopEdgePackagingJobCommandInput,
  StopEdgePackagingJobCommandOutput,
} from "../commands/StopEdgePackagingJobCommand";
import {
  StopHyperParameterTuningJobCommandInput,
  StopHyperParameterTuningJobCommandOutput,
} from "../commands/StopHyperParameterTuningJobCommand";
import {
  StopInferenceExperimentCommandInput,
  StopInferenceExperimentCommandOutput,
} from "../commands/StopInferenceExperimentCommand";
import {
  StopInferenceRecommendationsJobCommandInput,
  StopInferenceRecommendationsJobCommandOutput,
} from "../commands/StopInferenceRecommendationsJobCommand";
import {
  StopLabelingJobCommandInput,
  StopLabelingJobCommandOutput,
} from "../commands/StopLabelingJobCommand";
import {
  StopMlflowTrackingServerCommandInput,
  StopMlflowTrackingServerCommandOutput,
} from "../commands/StopMlflowTrackingServerCommand";
import {
  StopMonitoringScheduleCommandInput,
  StopMonitoringScheduleCommandOutput,
} from "../commands/StopMonitoringScheduleCommand";
import {
  StopNotebookInstanceCommandInput,
  StopNotebookInstanceCommandOutput,
} from "../commands/StopNotebookInstanceCommand";
import {
  StopOptimizationJobCommandInput,
  StopOptimizationJobCommandOutput,
} from "../commands/StopOptimizationJobCommand";
import {
  StopPipelineExecutionCommandInput,
  StopPipelineExecutionCommandOutput,
} from "../commands/StopPipelineExecutionCommand";
import {
  StopProcessingJobCommandInput,
  StopProcessingJobCommandOutput,
} from "../commands/StopProcessingJobCommand";
import {
  StopTrainingJobCommandInput,
  StopTrainingJobCommandOutput,
} from "../commands/StopTrainingJobCommand";
import {
  StopTransformJobCommandInput,
  StopTransformJobCommandOutput,
} from "../commands/StopTransformJobCommand";
import {
  UpdateActionCommandInput,
  UpdateActionCommandOutput,
} from "../commands/UpdateActionCommand";
import {
  UpdateAppImageConfigCommandInput,
  UpdateAppImageConfigCommandOutput,
} from "../commands/UpdateAppImageConfigCommand";
import {
  UpdateArtifactCommandInput,
  UpdateArtifactCommandOutput,
} from "../commands/UpdateArtifactCommand";
import {
  UpdateClusterCommandInput,
  UpdateClusterCommandOutput,
} from "../commands/UpdateClusterCommand";
import {
  UpdateClusterSchedulerConfigCommandInput,
  UpdateClusterSchedulerConfigCommandOutput,
} from "../commands/UpdateClusterSchedulerConfigCommand";
import {
  UpdateClusterSoftwareCommandInput,
  UpdateClusterSoftwareCommandOutput,
} from "../commands/UpdateClusterSoftwareCommand";
import {
  UpdateCodeRepositoryCommandInput,
  UpdateCodeRepositoryCommandOutput,
} from "../commands/UpdateCodeRepositoryCommand";
import {
  UpdateComputeQuotaCommandInput,
  UpdateComputeQuotaCommandOutput,
} from "../commands/UpdateComputeQuotaCommand";
import {
  UpdateContextCommandInput,
  UpdateContextCommandOutput,
} from "../commands/UpdateContextCommand";
import {
  UpdateDeviceFleetCommandInput,
  UpdateDeviceFleetCommandOutput,
} from "../commands/UpdateDeviceFleetCommand";
import {
  UpdateDevicesCommandInput,
  UpdateDevicesCommandOutput,
} from "../commands/UpdateDevicesCommand";
import {
  UpdateDomainCommandInput,
  UpdateDomainCommandOutput,
} from "../commands/UpdateDomainCommand";
import {
  UpdateEndpointCommandInput,
  UpdateEndpointCommandOutput,
} from "../commands/UpdateEndpointCommand";
import {
  UpdateEndpointWeightsAndCapacitiesCommandInput,
  UpdateEndpointWeightsAndCapacitiesCommandOutput,
} from "../commands/UpdateEndpointWeightsAndCapacitiesCommand";
import {
  UpdateExperimentCommandInput,
  UpdateExperimentCommandOutput,
} from "../commands/UpdateExperimentCommand";
import {
  UpdateFeatureGroupCommandInput,
  UpdateFeatureGroupCommandOutput,
} from "../commands/UpdateFeatureGroupCommand";
import {
  UpdateFeatureMetadataCommandInput,
  UpdateFeatureMetadataCommandOutput,
} from "../commands/UpdateFeatureMetadataCommand";
import {
  UpdateHubCommandInput,
  UpdateHubCommandOutput,
} from "../commands/UpdateHubCommand";
import {
  UpdateHubContentCommandInput,
  UpdateHubContentCommandOutput,
} from "../commands/UpdateHubContentCommand";
import {
  UpdateHubContentReferenceCommandInput,
  UpdateHubContentReferenceCommandOutput,
} from "../commands/UpdateHubContentReferenceCommand";
import {
  UpdateImageCommandInput,
  UpdateImageCommandOutput,
} from "../commands/UpdateImageCommand";
import {
  UpdateImageVersionCommandInput,
  UpdateImageVersionCommandOutput,
} from "../commands/UpdateImageVersionCommand";
import {
  UpdateInferenceComponentCommandInput,
  UpdateInferenceComponentCommandOutput,
} from "../commands/UpdateInferenceComponentCommand";
import {
  UpdateInferenceComponentRuntimeConfigCommandInput,
  UpdateInferenceComponentRuntimeConfigCommandOutput,
} from "../commands/UpdateInferenceComponentRuntimeConfigCommand";
import {
  UpdateInferenceExperimentCommandInput,
  UpdateInferenceExperimentCommandOutput,
} from "../commands/UpdateInferenceExperimentCommand";
import {
  UpdateMlflowTrackingServerCommandInput,
  UpdateMlflowTrackingServerCommandOutput,
} from "../commands/UpdateMlflowTrackingServerCommand";
import {
  UpdateModelCardCommandInput,
  UpdateModelCardCommandOutput,
} from "../commands/UpdateModelCardCommand";
import {
  UpdateModelPackageCommandInput,
  UpdateModelPackageCommandOutput,
} from "../commands/UpdateModelPackageCommand";
import {
  UpdateMonitoringAlertCommandInput,
  UpdateMonitoringAlertCommandOutput,
} from "../commands/UpdateMonitoringAlertCommand";
import {
  UpdateMonitoringScheduleCommandInput,
  UpdateMonitoringScheduleCommandOutput,
} from "../commands/UpdateMonitoringScheduleCommand";
import {
  UpdateNotebookInstanceCommandInput,
  UpdateNotebookInstanceCommandOutput,
} from "../commands/UpdateNotebookInstanceCommand";
import {
  UpdateNotebookInstanceLifecycleConfigCommandInput,
  UpdateNotebookInstanceLifecycleConfigCommandOutput,
} from "../commands/UpdateNotebookInstanceLifecycleConfigCommand";
import {
  UpdatePartnerAppCommandInput,
  UpdatePartnerAppCommandOutput,
} from "../commands/UpdatePartnerAppCommand";
import {
  UpdatePipelineCommandInput,
  UpdatePipelineCommandOutput,
} from "../commands/UpdatePipelineCommand";
import {
  UpdatePipelineExecutionCommandInput,
  UpdatePipelineExecutionCommandOutput,
} from "../commands/UpdatePipelineExecutionCommand";
import {
  UpdateProjectCommandInput,
  UpdateProjectCommandOutput,
} from "../commands/UpdateProjectCommand";
import {
  UpdateSpaceCommandInput,
  UpdateSpaceCommandOutput,
} from "../commands/UpdateSpaceCommand";
import {
  UpdateTrainingJobCommandInput,
  UpdateTrainingJobCommandOutput,
} from "../commands/UpdateTrainingJobCommand";
import {
  UpdateTrialCommandInput,
  UpdateTrialCommandOutput,
} from "../commands/UpdateTrialCommand";
import {
  UpdateTrialComponentCommandInput,
  UpdateTrialComponentCommandOutput,
} from "../commands/UpdateTrialComponentCommand";
import {
  UpdateUserProfileCommandInput,
  UpdateUserProfileCommandOutput,
} from "../commands/UpdateUserProfileCommand";
import {
  UpdateWorkforceCommandInput,
  UpdateWorkforceCommandOutput,
} from "../commands/UpdateWorkforceCommand";
import {
  UpdateWorkteamCommandInput,
  UpdateWorkteamCommandOutput,
} from "../commands/UpdateWorkteamCommand";
export declare const se_AddAssociationCommand: (
  input: AddAssociationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_AddTagsCommand: (
  input: AddTagsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_AssociateTrialComponentCommand: (
  input: AssociateTrialComponentCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_BatchDeleteClusterNodesCommand: (
  input: BatchDeleteClusterNodesCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_BatchDescribeModelPackageCommand: (
  input: BatchDescribeModelPackageCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateActionCommand: (
  input: CreateActionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateAlgorithmCommand: (
  input: CreateAlgorithmCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateAppCommand: (
  input: CreateAppCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateAppImageConfigCommand: (
  input: CreateAppImageConfigCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateArtifactCommand: (
  input: CreateArtifactCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateAutoMLJobCommand: (
  input: CreateAutoMLJobCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateAutoMLJobV2Command: (
  input: CreateAutoMLJobV2CommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateClusterCommand: (
  input: CreateClusterCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateClusterSchedulerConfigCommand: (
  input: CreateClusterSchedulerConfigCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateCodeRepositoryCommand: (
  input: CreateCodeRepositoryCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateCompilationJobCommand: (
  input: CreateCompilationJobCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateComputeQuotaCommand: (
  input: CreateComputeQuotaCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateContextCommand: (
  input: CreateContextCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateDataQualityJobDefinitionCommand: (
  input: CreateDataQualityJobDefinitionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateDeviceFleetCommand: (
  input: CreateDeviceFleetCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateDomainCommand: (
  input: CreateDomainCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateEdgeDeploymentPlanCommand: (
  input: CreateEdgeDeploymentPlanCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateEdgeDeploymentStageCommand: (
  input: CreateEdgeDeploymentStageCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateEdgePackagingJobCommand: (
  input: CreateEdgePackagingJobCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateEndpointCommand: (
  input: CreateEndpointCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateEndpointConfigCommand: (
  input: CreateEndpointConfigCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateExperimentCommand: (
  input: CreateExperimentCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateFeatureGroupCommand: (
  input: CreateFeatureGroupCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateFlowDefinitionCommand: (
  input: CreateFlowDefinitionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateHubCommand: (
  input: CreateHubCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateHubContentReferenceCommand: (
  input: CreateHubContentReferenceCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateHumanTaskUiCommand: (
  input: CreateHumanTaskUiCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateHyperParameterTuningJobCommand: (
  input: CreateHyperParameterTuningJobCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateImageCommand: (
  input: CreateImageCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateImageVersionCommand: (
  input: CreateImageVersionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateInferenceComponentCommand: (
  input: CreateInferenceComponentCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateInferenceExperimentCommand: (
  input: CreateInferenceExperimentCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateInferenceRecommendationsJobCommand: (
  input: CreateInferenceRecommendationsJobCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateLabelingJobCommand: (
  input: CreateLabelingJobCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateMlflowTrackingServerCommand: (
  input: CreateMlflowTrackingServerCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateModelCommand: (
  input: CreateModelCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateModelBiasJobDefinitionCommand: (
  input: CreateModelBiasJobDefinitionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateModelCardCommand: (
  input: CreateModelCardCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateModelCardExportJobCommand: (
  input: CreateModelCardExportJobCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateModelExplainabilityJobDefinitionCommand: (
  input: CreateModelExplainabilityJobDefinitionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateModelPackageCommand: (
  input: CreateModelPackageCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateModelPackageGroupCommand: (
  input: CreateModelPackageGroupCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateModelQualityJobDefinitionCommand: (
  input: CreateModelQualityJobDefinitionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateMonitoringScheduleCommand: (
  input: CreateMonitoringScheduleCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateNotebookInstanceCommand: (
  input: CreateNotebookInstanceCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateNotebookInstanceLifecycleConfigCommand: (
  input: CreateNotebookInstanceLifecycleConfigCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateOptimizationJobCommand: (
  input: CreateOptimizationJobCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreatePartnerAppCommand: (
  input: CreatePartnerAppCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreatePartnerAppPresignedUrlCommand: (
  input: CreatePartnerAppPresignedUrlCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreatePipelineCommand: (
  input: CreatePipelineCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreatePresignedDomainUrlCommand: (
  input: CreatePresignedDomainUrlCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreatePresignedMlflowTrackingServerUrlCommand: (
  input: CreatePresignedMlflowTrackingServerUrlCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreatePresignedNotebookInstanceUrlCommand: (
  input: CreatePresignedNotebookInstanceUrlCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateProcessingJobCommand: (
  input: CreateProcessingJobCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateProjectCommand: (
  input: CreateProjectCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateSpaceCommand: (
  input: CreateSpaceCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateStudioLifecycleConfigCommand: (
  input: CreateStudioLifecycleConfigCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateTrainingJobCommand: (
  input: CreateTrainingJobCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateTrainingPlanCommand: (
  input: CreateTrainingPlanCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateTransformJobCommand: (
  input: CreateTransformJobCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateTrialCommand: (
  input: CreateTrialCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateTrialComponentCommand: (
  input: CreateTrialComponentCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateUserProfileCommand: (
  input: CreateUserProfileCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateWorkforceCommand: (
  input: CreateWorkforceCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateWorkteamCommand: (
  input: CreateWorkteamCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteActionCommand: (
  input: DeleteActionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteAlgorithmCommand: (
  input: DeleteAlgorithmCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteAppCommand: (
  input: DeleteAppCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteAppImageConfigCommand: (
  input: DeleteAppImageConfigCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteArtifactCommand: (
  input: DeleteArtifactCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteAssociationCommand: (
  input: DeleteAssociationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteClusterCommand: (
  input: DeleteClusterCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteClusterSchedulerConfigCommand: (
  input: DeleteClusterSchedulerConfigCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteCodeRepositoryCommand: (
  input: DeleteCodeRepositoryCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteCompilationJobCommand: (
  input: DeleteCompilationJobCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteComputeQuotaCommand: (
  input: DeleteComputeQuotaCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteContextCommand: (
  input: DeleteContextCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteDataQualityJobDefinitionCommand: (
  input: DeleteDataQualityJobDefinitionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteDeviceFleetCommand: (
  input: DeleteDeviceFleetCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteDomainCommand: (
  input: DeleteDomainCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteEdgeDeploymentPlanCommand: (
  input: DeleteEdgeDeploymentPlanCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteEdgeDeploymentStageCommand: (
  input: DeleteEdgeDeploymentStageCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteEndpointCommand: (
  input: DeleteEndpointCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteEndpointConfigCommand: (
  input: DeleteEndpointConfigCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteExperimentCommand: (
  input: DeleteExperimentCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteFeatureGroupCommand: (
  input: DeleteFeatureGroupCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteFlowDefinitionCommand: (
  input: DeleteFlowDefinitionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteHubCommand: (
  input: DeleteHubCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteHubContentCommand: (
  input: DeleteHubContentCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteHubContentReferenceCommand: (
  input: DeleteHubContentReferenceCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteHumanTaskUiCommand: (
  input: DeleteHumanTaskUiCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteHyperParameterTuningJobCommand: (
  input: DeleteHyperParameterTuningJobCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteImageCommand: (
  input: DeleteImageCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteImageVersionCommand: (
  input: DeleteImageVersionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteInferenceComponentCommand: (
  input: DeleteInferenceComponentCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteInferenceExperimentCommand: (
  input: DeleteInferenceExperimentCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteMlflowTrackingServerCommand: (
  input: DeleteMlflowTrackingServerCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteModelCommand: (
  input: DeleteModelCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteModelBiasJobDefinitionCommand: (
  input: DeleteModelBiasJobDefinitionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteModelCardCommand: (
  input: DeleteModelCardCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteModelExplainabilityJobDefinitionCommand: (
  input: DeleteModelExplainabilityJobDefinitionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteModelPackageCommand: (
  input: DeleteModelPackageCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteModelPackageGroupCommand: (
  input: DeleteModelPackageGroupCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteModelPackageGroupPolicyCommand: (
  input: DeleteModelPackageGroupPolicyCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteModelQualityJobDefinitionCommand: (
  input: DeleteModelQualityJobDefinitionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteMonitoringScheduleCommand: (
  input: DeleteMonitoringScheduleCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteNotebookInstanceCommand: (
  input: DeleteNotebookInstanceCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteNotebookInstanceLifecycleConfigCommand: (
  input: DeleteNotebookInstanceLifecycleConfigCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteOptimizationJobCommand: (
  input: DeleteOptimizationJobCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeletePartnerAppCommand: (
  input: DeletePartnerAppCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeletePipelineCommand: (
  input: DeletePipelineCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteProjectCommand: (
  input: DeleteProjectCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteSpaceCommand: (
  input: DeleteSpaceCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteStudioLifecycleConfigCommand: (
  input: DeleteStudioLifecycleConfigCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteTagsCommand: (
  input: DeleteTagsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteTrialCommand: (
  input: DeleteTrialCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteTrialComponentCommand: (
  input: DeleteTrialComponentCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteUserProfileCommand: (
  input: DeleteUserProfileCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteWorkforceCommand: (
  input: DeleteWorkforceCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteWorkteamCommand: (
  input: DeleteWorkteamCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeregisterDevicesCommand: (
  input: DeregisterDevicesCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeActionCommand: (
  input: DescribeActionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeAlgorithmCommand: (
  input: DescribeAlgorithmCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeAppCommand: (
  input: DescribeAppCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeAppImageConfigCommand: (
  input: DescribeAppImageConfigCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeArtifactCommand: (
  input: DescribeArtifactCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeAutoMLJobCommand: (
  input: DescribeAutoMLJobCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeAutoMLJobV2Command: (
  input: DescribeAutoMLJobV2CommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeClusterCommand: (
  input: DescribeClusterCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeClusterNodeCommand: (
  input: DescribeClusterNodeCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeClusterSchedulerConfigCommand: (
  input: DescribeClusterSchedulerConfigCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeCodeRepositoryCommand: (
  input: DescribeCodeRepositoryCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeCompilationJobCommand: (
  input: DescribeCompilationJobCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeComputeQuotaCommand: (
  input: DescribeComputeQuotaCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeContextCommand: (
  input: DescribeContextCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeDataQualityJobDefinitionCommand: (
  input: DescribeDataQualityJobDefinitionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeDeviceCommand: (
  input: DescribeDeviceCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeDeviceFleetCommand: (
  input: DescribeDeviceFleetCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeDomainCommand: (
  input: DescribeDomainCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeEdgeDeploymentPlanCommand: (
  input: DescribeEdgeDeploymentPlanCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeEdgePackagingJobCommand: (
  input: DescribeEdgePackagingJobCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeEndpointCommand: (
  input: DescribeEndpointCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeEndpointConfigCommand: (
  input: DescribeEndpointConfigCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeExperimentCommand: (
  input: DescribeExperimentCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeFeatureGroupCommand: (
  input: DescribeFeatureGroupCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeFeatureMetadataCommand: (
  input: DescribeFeatureMetadataCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeFlowDefinitionCommand: (
  input: DescribeFlowDefinitionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeHubCommand: (
  input: DescribeHubCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeHubContentCommand: (
  input: DescribeHubContentCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeHumanTaskUiCommand: (
  input: DescribeHumanTaskUiCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeHyperParameterTuningJobCommand: (
  input: DescribeHyperParameterTuningJobCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeImageCommand: (
  input: DescribeImageCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeImageVersionCommand: (
  input: DescribeImageVersionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeInferenceComponentCommand: (
  input: DescribeInferenceComponentCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeInferenceExperimentCommand: (
  input: DescribeInferenceExperimentCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeInferenceRecommendationsJobCommand: (
  input: DescribeInferenceRecommendationsJobCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeLabelingJobCommand: (
  input: DescribeLabelingJobCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeLineageGroupCommand: (
  input: DescribeLineageGroupCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeMlflowTrackingServerCommand: (
  input: DescribeMlflowTrackingServerCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeModelCommand: (
  input: DescribeModelCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeModelBiasJobDefinitionCommand: (
  input: DescribeModelBiasJobDefinitionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeModelCardCommand: (
  input: DescribeModelCardCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeModelCardExportJobCommand: (
  input: DescribeModelCardExportJobCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeModelExplainabilityJobDefinitionCommand: (
  input: DescribeModelExplainabilityJobDefinitionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeModelPackageCommand: (
  input: DescribeModelPackageCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeModelPackageGroupCommand: (
  input: DescribeModelPackageGroupCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeModelQualityJobDefinitionCommand: (
  input: DescribeModelQualityJobDefinitionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeMonitoringScheduleCommand: (
  input: DescribeMonitoringScheduleCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeNotebookInstanceCommand: (
  input: DescribeNotebookInstanceCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeNotebookInstanceLifecycleConfigCommand: (
  input: DescribeNotebookInstanceLifecycleConfigCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeOptimizationJobCommand: (
  input: DescribeOptimizationJobCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribePartnerAppCommand: (
  input: DescribePartnerAppCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribePipelineCommand: (
  input: DescribePipelineCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribePipelineDefinitionForExecutionCommand: (
  input: DescribePipelineDefinitionForExecutionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribePipelineExecutionCommand: (
  input: DescribePipelineExecutionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeProcessingJobCommand: (
  input: DescribeProcessingJobCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeProjectCommand: (
  input: DescribeProjectCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeSpaceCommand: (
  input: DescribeSpaceCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeStudioLifecycleConfigCommand: (
  input: DescribeStudioLifecycleConfigCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeSubscribedWorkteamCommand: (
  input: DescribeSubscribedWorkteamCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeTrainingJobCommand: (
  input: DescribeTrainingJobCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeTrainingPlanCommand: (
  input: DescribeTrainingPlanCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeTransformJobCommand: (
  input: DescribeTransformJobCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeTrialCommand: (
  input: DescribeTrialCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeTrialComponentCommand: (
  input: DescribeTrialComponentCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeUserProfileCommand: (
  input: DescribeUserProfileCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeWorkforceCommand: (
  input: DescribeWorkforceCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeWorkteamCommand: (
  input: DescribeWorkteamCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DisableSagemakerServicecatalogPortfolioCommand: (
  input: DisableSagemakerServicecatalogPortfolioCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DisassociateTrialComponentCommand: (
  input: DisassociateTrialComponentCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_EnableSagemakerServicecatalogPortfolioCommand: (
  input: EnableSagemakerServicecatalogPortfolioCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetDeviceFleetReportCommand: (
  input: GetDeviceFleetReportCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetLineageGroupPolicyCommand: (
  input: GetLineageGroupPolicyCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetModelPackageGroupPolicyCommand: (
  input: GetModelPackageGroupPolicyCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetSagemakerServicecatalogPortfolioStatusCommand: (
  input: GetSagemakerServicecatalogPortfolioStatusCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetScalingConfigurationRecommendationCommand: (
  input: GetScalingConfigurationRecommendationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetSearchSuggestionsCommand: (
  input: GetSearchSuggestionsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ImportHubContentCommand: (
  input: ImportHubContentCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListActionsCommand: (
  input: ListActionsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListAlgorithmsCommand: (
  input: ListAlgorithmsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListAliasesCommand: (
  input: ListAliasesCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListAppImageConfigsCommand: (
  input: ListAppImageConfigsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListAppsCommand: (
  input: ListAppsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListArtifactsCommand: (
  input: ListArtifactsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListAssociationsCommand: (
  input: ListAssociationsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListAutoMLJobsCommand: (
  input: ListAutoMLJobsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListCandidatesForAutoMLJobCommand: (
  input: ListCandidatesForAutoMLJobCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListClusterNodesCommand: (
  input: ListClusterNodesCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListClustersCommand: (
  input: ListClustersCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListClusterSchedulerConfigsCommand: (
  input: ListClusterSchedulerConfigsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListCodeRepositoriesCommand: (
  input: ListCodeRepositoriesCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListCompilationJobsCommand: (
  input: ListCompilationJobsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListComputeQuotasCommand: (
  input: ListComputeQuotasCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListContextsCommand: (
  input: ListContextsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListDataQualityJobDefinitionsCommand: (
  input: ListDataQualityJobDefinitionsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListDeviceFleetsCommand: (
  input: ListDeviceFleetsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListDevicesCommand: (
  input: ListDevicesCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListDomainsCommand: (
  input: ListDomainsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListEdgeDeploymentPlansCommand: (
  input: ListEdgeDeploymentPlansCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListEdgePackagingJobsCommand: (
  input: ListEdgePackagingJobsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListEndpointConfigsCommand: (
  input: ListEndpointConfigsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListEndpointsCommand: (
  input: ListEndpointsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListExperimentsCommand: (
  input: ListExperimentsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListFeatureGroupsCommand: (
  input: ListFeatureGroupsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListFlowDefinitionsCommand: (
  input: ListFlowDefinitionsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListHubContentsCommand: (
  input: ListHubContentsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListHubContentVersionsCommand: (
  input: ListHubContentVersionsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListHubsCommand: (
  input: ListHubsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListHumanTaskUisCommand: (
  input: ListHumanTaskUisCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListHyperParameterTuningJobsCommand: (
  input: ListHyperParameterTuningJobsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListImagesCommand: (
  input: ListImagesCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListImageVersionsCommand: (
  input: ListImageVersionsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListInferenceComponentsCommand: (
  input: ListInferenceComponentsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListInferenceExperimentsCommand: (
  input: ListInferenceExperimentsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListInferenceRecommendationsJobsCommand: (
  input: ListInferenceRecommendationsJobsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListInferenceRecommendationsJobStepsCommand: (
  input: ListInferenceRecommendationsJobStepsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListLabelingJobsCommand: (
  input: ListLabelingJobsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListLabelingJobsForWorkteamCommand: (
  input: ListLabelingJobsForWorkteamCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListLineageGroupsCommand: (
  input: ListLineageGroupsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListMlflowTrackingServersCommand: (
  input: ListMlflowTrackingServersCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListModelBiasJobDefinitionsCommand: (
  input: ListModelBiasJobDefinitionsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListModelCardExportJobsCommand: (
  input: ListModelCardExportJobsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListModelCardsCommand: (
  input: ListModelCardsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListModelCardVersionsCommand: (
  input: ListModelCardVersionsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListModelExplainabilityJobDefinitionsCommand: (
  input: ListModelExplainabilityJobDefinitionsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListModelMetadataCommand: (
  input: ListModelMetadataCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListModelPackageGroupsCommand: (
  input: ListModelPackageGroupsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListModelPackagesCommand: (
  input: ListModelPackagesCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListModelQualityJobDefinitionsCommand: (
  input: ListModelQualityJobDefinitionsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListModelsCommand: (
  input: ListModelsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListMonitoringAlertHistoryCommand: (
  input: ListMonitoringAlertHistoryCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListMonitoringAlertsCommand: (
  input: ListMonitoringAlertsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListMonitoringExecutionsCommand: (
  input: ListMonitoringExecutionsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListMonitoringSchedulesCommand: (
  input: ListMonitoringSchedulesCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListNotebookInstanceLifecycleConfigsCommand: (
  input: ListNotebookInstanceLifecycleConfigsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListNotebookInstancesCommand: (
  input: ListNotebookInstancesCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListOptimizationJobsCommand: (
  input: ListOptimizationJobsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListPartnerAppsCommand: (
  input: ListPartnerAppsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListPipelineExecutionsCommand: (
  input: ListPipelineExecutionsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListPipelineExecutionStepsCommand: (
  input: ListPipelineExecutionStepsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListPipelineParametersForExecutionCommand: (
  input: ListPipelineParametersForExecutionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListPipelinesCommand: (
  input: ListPipelinesCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListProcessingJobsCommand: (
  input: ListProcessingJobsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListProjectsCommand: (
  input: ListProjectsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListResourceCatalogsCommand: (
  input: ListResourceCatalogsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListSpacesCommand: (
  input: ListSpacesCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListStageDevicesCommand: (
  input: ListStageDevicesCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListStudioLifecycleConfigsCommand: (
  input: ListStudioLifecycleConfigsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListSubscribedWorkteamsCommand: (
  input: ListSubscribedWorkteamsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListTagsCommand: (
  input: ListTagsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListTrainingJobsCommand: (
  input: ListTrainingJobsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListTrainingJobsForHyperParameterTuningJobCommand: (
  input: ListTrainingJobsForHyperParameterTuningJobCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListTrainingPlansCommand: (
  input: ListTrainingPlansCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListTransformJobsCommand: (
  input: ListTransformJobsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListTrialComponentsCommand: (
  input: ListTrialComponentsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListTrialsCommand: (
  input: ListTrialsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListUserProfilesCommand: (
  input: ListUserProfilesCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListWorkforcesCommand: (
  input: ListWorkforcesCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListWorkteamsCommand: (
  input: ListWorkteamsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutModelPackageGroupPolicyCommand: (
  input: PutModelPackageGroupPolicyCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_QueryLineageCommand: (
  input: QueryLineageCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_RegisterDevicesCommand: (
  input: RegisterDevicesCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_RenderUiTemplateCommand: (
  input: RenderUiTemplateCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_RetryPipelineExecutionCommand: (
  input: RetryPipelineExecutionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_SearchCommand: (
  input: SearchCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_SearchTrainingPlanOfferingsCommand: (
  input: SearchTrainingPlanOfferingsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_SendPipelineExecutionStepFailureCommand: (
  input: SendPipelineExecutionStepFailureCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_SendPipelineExecutionStepSuccessCommand: (
  input: SendPipelineExecutionStepSuccessCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_StartEdgeDeploymentStageCommand: (
  input: StartEdgeDeploymentStageCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_StartInferenceExperimentCommand: (
  input: StartInferenceExperimentCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_StartMlflowTrackingServerCommand: (
  input: StartMlflowTrackingServerCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_StartMonitoringScheduleCommand: (
  input: StartMonitoringScheduleCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_StartNotebookInstanceCommand: (
  input: StartNotebookInstanceCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_StartPipelineExecutionCommand: (
  input: StartPipelineExecutionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_StopAutoMLJobCommand: (
  input: StopAutoMLJobCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_StopCompilationJobCommand: (
  input: StopCompilationJobCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_StopEdgeDeploymentStageCommand: (
  input: StopEdgeDeploymentStageCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_StopEdgePackagingJobCommand: (
  input: StopEdgePackagingJobCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_StopHyperParameterTuningJobCommand: (
  input: StopHyperParameterTuningJobCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_StopInferenceExperimentCommand: (
  input: StopInferenceExperimentCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_StopInferenceRecommendationsJobCommand: (
  input: StopInferenceRecommendationsJobCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_StopLabelingJobCommand: (
  input: StopLabelingJobCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_StopMlflowTrackingServerCommand: (
  input: StopMlflowTrackingServerCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_StopMonitoringScheduleCommand: (
  input: StopMonitoringScheduleCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_StopNotebookInstanceCommand: (
  input: StopNotebookInstanceCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_StopOptimizationJobCommand: (
  input: StopOptimizationJobCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_StopPipelineExecutionCommand: (
  input: StopPipelineExecutionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_StopProcessingJobCommand: (
  input: StopProcessingJobCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_StopTrainingJobCommand: (
  input: StopTrainingJobCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_StopTransformJobCommand: (
  input: StopTransformJobCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateActionCommand: (
  input: UpdateActionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateAppImageConfigCommand: (
  input: UpdateAppImageConfigCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateArtifactCommand: (
  input: UpdateArtifactCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateClusterCommand: (
  input: UpdateClusterCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateClusterSchedulerConfigCommand: (
  input: UpdateClusterSchedulerConfigCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateClusterSoftwareCommand: (
  input: UpdateClusterSoftwareCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateCodeRepositoryCommand: (
  input: UpdateCodeRepositoryCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateComputeQuotaCommand: (
  input: UpdateComputeQuotaCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateContextCommand: (
  input: UpdateContextCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateDeviceFleetCommand: (
  input: UpdateDeviceFleetCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateDevicesCommand: (
  input: UpdateDevicesCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateDomainCommand: (
  input: UpdateDomainCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateEndpointCommand: (
  input: UpdateEndpointCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateEndpointWeightsAndCapacitiesCommand: (
  input: UpdateEndpointWeightsAndCapacitiesCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateExperimentCommand: (
  input: UpdateExperimentCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateFeatureGroupCommand: (
  input: UpdateFeatureGroupCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateFeatureMetadataCommand: (
  input: UpdateFeatureMetadataCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateHubCommand: (
  input: UpdateHubCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateHubContentCommand: (
  input: UpdateHubContentCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateHubContentReferenceCommand: (
  input: UpdateHubContentReferenceCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateImageCommand: (
  input: UpdateImageCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateImageVersionCommand: (
  input: UpdateImageVersionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateInferenceComponentCommand: (
  input: UpdateInferenceComponentCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateInferenceComponentRuntimeConfigCommand: (
  input: UpdateInferenceComponentRuntimeConfigCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateInferenceExperimentCommand: (
  input: UpdateInferenceExperimentCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateMlflowTrackingServerCommand: (
  input: UpdateMlflowTrackingServerCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateModelCardCommand: (
  input: UpdateModelCardCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateModelPackageCommand: (
  input: UpdateModelPackageCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateMonitoringAlertCommand: (
  input: UpdateMonitoringAlertCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateMonitoringScheduleCommand: (
  input: UpdateMonitoringScheduleCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateNotebookInstanceCommand: (
  input: UpdateNotebookInstanceCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateNotebookInstanceLifecycleConfigCommand: (
  input: UpdateNotebookInstanceLifecycleConfigCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdatePartnerAppCommand: (
  input: UpdatePartnerAppCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdatePipelineCommand: (
  input: UpdatePipelineCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdatePipelineExecutionCommand: (
  input: UpdatePipelineExecutionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateProjectCommand: (
  input: UpdateProjectCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateSpaceCommand: (
  input: UpdateSpaceCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateTrainingJobCommand: (
  input: UpdateTrainingJobCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateTrialCommand: (
  input: UpdateTrialCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateTrialComponentCommand: (
  input: UpdateTrialComponentCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateUserProfileCommand: (
  input: UpdateUserProfileCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateWorkforceCommand: (
  input: UpdateWorkforceCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateWorkteamCommand: (
  input: UpdateWorkteamCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const de_AddAssociationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<AddAssociationCommandOutput>;
export declare const de_AddTagsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<AddTagsCommandOutput>;
export declare const de_AssociateTrialComponentCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<AssociateTrialComponentCommandOutput>;
export declare const de_BatchDeleteClusterNodesCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<BatchDeleteClusterNodesCommandOutput>;
export declare const de_BatchDescribeModelPackageCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<BatchDescribeModelPackageCommandOutput>;
export declare const de_CreateActionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateActionCommandOutput>;
export declare const de_CreateAlgorithmCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateAlgorithmCommandOutput>;
export declare const de_CreateAppCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateAppCommandOutput>;
export declare const de_CreateAppImageConfigCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateAppImageConfigCommandOutput>;
export declare const de_CreateArtifactCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateArtifactCommandOutput>;
export declare const de_CreateAutoMLJobCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateAutoMLJobCommandOutput>;
export declare const de_CreateAutoMLJobV2Command: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateAutoMLJobV2CommandOutput>;
export declare const de_CreateClusterCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateClusterCommandOutput>;
export declare const de_CreateClusterSchedulerConfigCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateClusterSchedulerConfigCommandOutput>;
export declare const de_CreateCodeRepositoryCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateCodeRepositoryCommandOutput>;
export declare const de_CreateCompilationJobCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateCompilationJobCommandOutput>;
export declare const de_CreateComputeQuotaCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateComputeQuotaCommandOutput>;
export declare const de_CreateContextCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateContextCommandOutput>;
export declare const de_CreateDataQualityJobDefinitionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateDataQualityJobDefinitionCommandOutput>;
export declare const de_CreateDeviceFleetCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateDeviceFleetCommandOutput>;
export declare const de_CreateDomainCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateDomainCommandOutput>;
export declare const de_CreateEdgeDeploymentPlanCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateEdgeDeploymentPlanCommandOutput>;
export declare const de_CreateEdgeDeploymentStageCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateEdgeDeploymentStageCommandOutput>;
export declare const de_CreateEdgePackagingJobCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateEdgePackagingJobCommandOutput>;
export declare const de_CreateEndpointCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateEndpointCommandOutput>;
export declare const de_CreateEndpointConfigCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateEndpointConfigCommandOutput>;
export declare const de_CreateExperimentCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateExperimentCommandOutput>;
export declare const de_CreateFeatureGroupCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateFeatureGroupCommandOutput>;
export declare const de_CreateFlowDefinitionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateFlowDefinitionCommandOutput>;
export declare const de_CreateHubCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateHubCommandOutput>;
export declare const de_CreateHubContentReferenceCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateHubContentReferenceCommandOutput>;
export declare const de_CreateHumanTaskUiCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateHumanTaskUiCommandOutput>;
export declare const de_CreateHyperParameterTuningJobCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateHyperParameterTuningJobCommandOutput>;
export declare const de_CreateImageCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateImageCommandOutput>;
export declare const de_CreateImageVersionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateImageVersionCommandOutput>;
export declare const de_CreateInferenceComponentCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateInferenceComponentCommandOutput>;
export declare const de_CreateInferenceExperimentCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateInferenceExperimentCommandOutput>;
export declare const de_CreateInferenceRecommendationsJobCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateInferenceRecommendationsJobCommandOutput>;
export declare const de_CreateLabelingJobCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateLabelingJobCommandOutput>;
export declare const de_CreateMlflowTrackingServerCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateMlflowTrackingServerCommandOutput>;
export declare const de_CreateModelCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateModelCommandOutput>;
export declare const de_CreateModelBiasJobDefinitionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateModelBiasJobDefinitionCommandOutput>;
export declare const de_CreateModelCardCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateModelCardCommandOutput>;
export declare const de_CreateModelCardExportJobCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateModelCardExportJobCommandOutput>;
export declare const de_CreateModelExplainabilityJobDefinitionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateModelExplainabilityJobDefinitionCommandOutput>;
export declare const de_CreateModelPackageCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateModelPackageCommandOutput>;
export declare const de_CreateModelPackageGroupCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateModelPackageGroupCommandOutput>;
export declare const de_CreateModelQualityJobDefinitionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateModelQualityJobDefinitionCommandOutput>;
export declare const de_CreateMonitoringScheduleCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateMonitoringScheduleCommandOutput>;
export declare const de_CreateNotebookInstanceCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateNotebookInstanceCommandOutput>;
export declare const de_CreateNotebookInstanceLifecycleConfigCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateNotebookInstanceLifecycleConfigCommandOutput>;
export declare const de_CreateOptimizationJobCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateOptimizationJobCommandOutput>;
export declare const de_CreatePartnerAppCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreatePartnerAppCommandOutput>;
export declare const de_CreatePartnerAppPresignedUrlCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreatePartnerAppPresignedUrlCommandOutput>;
export declare const de_CreatePipelineCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreatePipelineCommandOutput>;
export declare const de_CreatePresignedDomainUrlCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreatePresignedDomainUrlCommandOutput>;
export declare const de_CreatePresignedMlflowTrackingServerUrlCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreatePresignedMlflowTrackingServerUrlCommandOutput>;
export declare const de_CreatePresignedNotebookInstanceUrlCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreatePresignedNotebookInstanceUrlCommandOutput>;
export declare const de_CreateProcessingJobCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateProcessingJobCommandOutput>;
export declare const de_CreateProjectCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateProjectCommandOutput>;
export declare const de_CreateSpaceCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateSpaceCommandOutput>;
export declare const de_CreateStudioLifecycleConfigCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateStudioLifecycleConfigCommandOutput>;
export declare const de_CreateTrainingJobCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateTrainingJobCommandOutput>;
export declare const de_CreateTrainingPlanCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateTrainingPlanCommandOutput>;
export declare const de_CreateTransformJobCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateTransformJobCommandOutput>;
export declare const de_CreateTrialCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateTrialCommandOutput>;
export declare const de_CreateTrialComponentCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateTrialComponentCommandOutput>;
export declare const de_CreateUserProfileCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateUserProfileCommandOutput>;
export declare const de_CreateWorkforceCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateWorkforceCommandOutput>;
export declare const de_CreateWorkteamCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateWorkteamCommandOutput>;
export declare const de_DeleteActionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteActionCommandOutput>;
export declare const de_DeleteAlgorithmCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteAlgorithmCommandOutput>;
export declare const de_DeleteAppCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteAppCommandOutput>;
export declare const de_DeleteAppImageConfigCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteAppImageConfigCommandOutput>;
export declare const de_DeleteArtifactCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteArtifactCommandOutput>;
export declare const de_DeleteAssociationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteAssociationCommandOutput>;
export declare const de_DeleteClusterCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteClusterCommandOutput>;
export declare const de_DeleteClusterSchedulerConfigCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteClusterSchedulerConfigCommandOutput>;
export declare const de_DeleteCodeRepositoryCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteCodeRepositoryCommandOutput>;
export declare const de_DeleteCompilationJobCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteCompilationJobCommandOutput>;
export declare const de_DeleteComputeQuotaCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteComputeQuotaCommandOutput>;
export declare const de_DeleteContextCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteContextCommandOutput>;
export declare const de_DeleteDataQualityJobDefinitionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteDataQualityJobDefinitionCommandOutput>;
export declare const de_DeleteDeviceFleetCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteDeviceFleetCommandOutput>;
export declare const de_DeleteDomainCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteDomainCommandOutput>;
export declare const de_DeleteEdgeDeploymentPlanCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteEdgeDeploymentPlanCommandOutput>;
export declare const de_DeleteEdgeDeploymentStageCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteEdgeDeploymentStageCommandOutput>;
export declare const de_DeleteEndpointCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteEndpointCommandOutput>;
export declare const de_DeleteEndpointConfigCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteEndpointConfigCommandOutput>;
export declare const de_DeleteExperimentCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteExperimentCommandOutput>;
export declare const de_DeleteFeatureGroupCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteFeatureGroupCommandOutput>;
export declare const de_DeleteFlowDefinitionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteFlowDefinitionCommandOutput>;
export declare const de_DeleteHubCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteHubCommandOutput>;
export declare const de_DeleteHubContentCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteHubContentCommandOutput>;
export declare const de_DeleteHubContentReferenceCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteHubContentReferenceCommandOutput>;
export declare const de_DeleteHumanTaskUiCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteHumanTaskUiCommandOutput>;
export declare const de_DeleteHyperParameterTuningJobCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteHyperParameterTuningJobCommandOutput>;
export declare const de_DeleteImageCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteImageCommandOutput>;
export declare const de_DeleteImageVersionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteImageVersionCommandOutput>;
export declare const de_DeleteInferenceComponentCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteInferenceComponentCommandOutput>;
export declare const de_DeleteInferenceExperimentCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteInferenceExperimentCommandOutput>;
export declare const de_DeleteMlflowTrackingServerCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteMlflowTrackingServerCommandOutput>;
export declare const de_DeleteModelCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteModelCommandOutput>;
export declare const de_DeleteModelBiasJobDefinitionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteModelBiasJobDefinitionCommandOutput>;
export declare const de_DeleteModelCardCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteModelCardCommandOutput>;
export declare const de_DeleteModelExplainabilityJobDefinitionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteModelExplainabilityJobDefinitionCommandOutput>;
export declare const de_DeleteModelPackageCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteModelPackageCommandOutput>;
export declare const de_DeleteModelPackageGroupCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteModelPackageGroupCommandOutput>;
export declare const de_DeleteModelPackageGroupPolicyCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteModelPackageGroupPolicyCommandOutput>;
export declare const de_DeleteModelQualityJobDefinitionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteModelQualityJobDefinitionCommandOutput>;
export declare const de_DeleteMonitoringScheduleCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteMonitoringScheduleCommandOutput>;
export declare const de_DeleteNotebookInstanceCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteNotebookInstanceCommandOutput>;
export declare const de_DeleteNotebookInstanceLifecycleConfigCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteNotebookInstanceLifecycleConfigCommandOutput>;
export declare const de_DeleteOptimizationJobCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteOptimizationJobCommandOutput>;
export declare const de_DeletePartnerAppCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeletePartnerAppCommandOutput>;
export declare const de_DeletePipelineCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeletePipelineCommandOutput>;
export declare const de_DeleteProjectCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteProjectCommandOutput>;
export declare const de_DeleteSpaceCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteSpaceCommandOutput>;
export declare const de_DeleteStudioLifecycleConfigCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteStudioLifecycleConfigCommandOutput>;
export declare const de_DeleteTagsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteTagsCommandOutput>;
export declare const de_DeleteTrialCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteTrialCommandOutput>;
export declare const de_DeleteTrialComponentCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteTrialComponentCommandOutput>;
export declare const de_DeleteUserProfileCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteUserProfileCommandOutput>;
export declare const de_DeleteWorkforceCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteWorkforceCommandOutput>;
export declare const de_DeleteWorkteamCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteWorkteamCommandOutput>;
export declare const de_DeregisterDevicesCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeregisterDevicesCommandOutput>;
export declare const de_DescribeActionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeActionCommandOutput>;
export declare const de_DescribeAlgorithmCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeAlgorithmCommandOutput>;
export declare const de_DescribeAppCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeAppCommandOutput>;
export declare const de_DescribeAppImageConfigCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeAppImageConfigCommandOutput>;
export declare const de_DescribeArtifactCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeArtifactCommandOutput>;
export declare const de_DescribeAutoMLJobCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeAutoMLJobCommandOutput>;
export declare const de_DescribeAutoMLJobV2Command: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeAutoMLJobV2CommandOutput>;
export declare const de_DescribeClusterCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeClusterCommandOutput>;
export declare const de_DescribeClusterNodeCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeClusterNodeCommandOutput>;
export declare const de_DescribeClusterSchedulerConfigCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeClusterSchedulerConfigCommandOutput>;
export declare const de_DescribeCodeRepositoryCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeCodeRepositoryCommandOutput>;
export declare const de_DescribeCompilationJobCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeCompilationJobCommandOutput>;
export declare const de_DescribeComputeQuotaCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeComputeQuotaCommandOutput>;
export declare const de_DescribeContextCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeContextCommandOutput>;
export declare const de_DescribeDataQualityJobDefinitionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeDataQualityJobDefinitionCommandOutput>;
export declare const de_DescribeDeviceCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeDeviceCommandOutput>;
export declare const de_DescribeDeviceFleetCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeDeviceFleetCommandOutput>;
export declare const de_DescribeDomainCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeDomainCommandOutput>;
export declare const de_DescribeEdgeDeploymentPlanCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeEdgeDeploymentPlanCommandOutput>;
export declare const de_DescribeEdgePackagingJobCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeEdgePackagingJobCommandOutput>;
export declare const de_DescribeEndpointCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeEndpointCommandOutput>;
export declare const de_DescribeEndpointConfigCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeEndpointConfigCommandOutput>;
export declare const de_DescribeExperimentCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeExperimentCommandOutput>;
export declare const de_DescribeFeatureGroupCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeFeatureGroupCommandOutput>;
export declare const de_DescribeFeatureMetadataCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeFeatureMetadataCommandOutput>;
export declare const de_DescribeFlowDefinitionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeFlowDefinitionCommandOutput>;
export declare const de_DescribeHubCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeHubCommandOutput>;
export declare const de_DescribeHubContentCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeHubContentCommandOutput>;
export declare const de_DescribeHumanTaskUiCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeHumanTaskUiCommandOutput>;
export declare const de_DescribeHyperParameterTuningJobCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeHyperParameterTuningJobCommandOutput>;
export declare const de_DescribeImageCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeImageCommandOutput>;
export declare const de_DescribeImageVersionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeImageVersionCommandOutput>;
export declare const de_DescribeInferenceComponentCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeInferenceComponentCommandOutput>;
export declare const de_DescribeInferenceExperimentCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeInferenceExperimentCommandOutput>;
export declare const de_DescribeInferenceRecommendationsJobCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeInferenceRecommendationsJobCommandOutput>;
export declare const de_DescribeLabelingJobCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeLabelingJobCommandOutput>;
export declare const de_DescribeLineageGroupCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeLineageGroupCommandOutput>;
export declare const de_DescribeMlflowTrackingServerCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeMlflowTrackingServerCommandOutput>;
export declare const de_DescribeModelCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeModelCommandOutput>;
export declare const de_DescribeModelBiasJobDefinitionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeModelBiasJobDefinitionCommandOutput>;
export declare const de_DescribeModelCardCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeModelCardCommandOutput>;
export declare const de_DescribeModelCardExportJobCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeModelCardExportJobCommandOutput>;
export declare const de_DescribeModelExplainabilityJobDefinitionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeModelExplainabilityJobDefinitionCommandOutput>;
export declare const de_DescribeModelPackageCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeModelPackageCommandOutput>;
export declare const de_DescribeModelPackageGroupCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeModelPackageGroupCommandOutput>;
export declare const de_DescribeModelQualityJobDefinitionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeModelQualityJobDefinitionCommandOutput>;
export declare const de_DescribeMonitoringScheduleCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeMonitoringScheduleCommandOutput>;
export declare const de_DescribeNotebookInstanceCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeNotebookInstanceCommandOutput>;
export declare const de_DescribeNotebookInstanceLifecycleConfigCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeNotebookInstanceLifecycleConfigCommandOutput>;
export declare const de_DescribeOptimizationJobCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeOptimizationJobCommandOutput>;
export declare const de_DescribePartnerAppCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribePartnerAppCommandOutput>;
export declare const de_DescribePipelineCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribePipelineCommandOutput>;
export declare const de_DescribePipelineDefinitionForExecutionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribePipelineDefinitionForExecutionCommandOutput>;
export declare const de_DescribePipelineExecutionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribePipelineExecutionCommandOutput>;
export declare const de_DescribeProcessingJobCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeProcessingJobCommandOutput>;
export declare const de_DescribeProjectCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeProjectCommandOutput>;
export declare const de_DescribeSpaceCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeSpaceCommandOutput>;
export declare const de_DescribeStudioLifecycleConfigCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeStudioLifecycleConfigCommandOutput>;
export declare const de_DescribeSubscribedWorkteamCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeSubscribedWorkteamCommandOutput>;
export declare const de_DescribeTrainingJobCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeTrainingJobCommandOutput>;
export declare const de_DescribeTrainingPlanCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeTrainingPlanCommandOutput>;
export declare const de_DescribeTransformJobCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeTransformJobCommandOutput>;
export declare const de_DescribeTrialCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeTrialCommandOutput>;
export declare const de_DescribeTrialComponentCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeTrialComponentCommandOutput>;
export declare const de_DescribeUserProfileCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeUserProfileCommandOutput>;
export declare const de_DescribeWorkforceCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeWorkforceCommandOutput>;
export declare const de_DescribeWorkteamCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeWorkteamCommandOutput>;
export declare const de_DisableSagemakerServicecatalogPortfolioCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DisableSagemakerServicecatalogPortfolioCommandOutput>;
export declare const de_DisassociateTrialComponentCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DisassociateTrialComponentCommandOutput>;
export declare const de_EnableSagemakerServicecatalogPortfolioCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<EnableSagemakerServicecatalogPortfolioCommandOutput>;
export declare const de_GetDeviceFleetReportCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetDeviceFleetReportCommandOutput>;
export declare const de_GetLineageGroupPolicyCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetLineageGroupPolicyCommandOutput>;
export declare const de_GetModelPackageGroupPolicyCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetModelPackageGroupPolicyCommandOutput>;
export declare const de_GetSagemakerServicecatalogPortfolioStatusCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetSagemakerServicecatalogPortfolioStatusCommandOutput>;
export declare const de_GetScalingConfigurationRecommendationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetScalingConfigurationRecommendationCommandOutput>;
export declare const de_GetSearchSuggestionsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetSearchSuggestionsCommandOutput>;
export declare const de_ImportHubContentCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ImportHubContentCommandOutput>;
export declare const de_ListActionsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListActionsCommandOutput>;
export declare const de_ListAlgorithmsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListAlgorithmsCommandOutput>;
export declare const de_ListAliasesCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListAliasesCommandOutput>;
export declare const de_ListAppImageConfigsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListAppImageConfigsCommandOutput>;
export declare const de_ListAppsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListAppsCommandOutput>;
export declare const de_ListArtifactsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListArtifactsCommandOutput>;
export declare const de_ListAssociationsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListAssociationsCommandOutput>;
export declare const de_ListAutoMLJobsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListAutoMLJobsCommandOutput>;
export declare const de_ListCandidatesForAutoMLJobCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListCandidatesForAutoMLJobCommandOutput>;
export declare const de_ListClusterNodesCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListClusterNodesCommandOutput>;
export declare const de_ListClustersCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListClustersCommandOutput>;
export declare const de_ListClusterSchedulerConfigsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListClusterSchedulerConfigsCommandOutput>;
export declare const de_ListCodeRepositoriesCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListCodeRepositoriesCommandOutput>;
export declare const de_ListCompilationJobsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListCompilationJobsCommandOutput>;
export declare const de_ListComputeQuotasCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListComputeQuotasCommandOutput>;
export declare const de_ListContextsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListContextsCommandOutput>;
export declare const de_ListDataQualityJobDefinitionsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListDataQualityJobDefinitionsCommandOutput>;
export declare const de_ListDeviceFleetsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListDeviceFleetsCommandOutput>;
export declare const de_ListDevicesCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListDevicesCommandOutput>;
export declare const de_ListDomainsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListDomainsCommandOutput>;
export declare const de_ListEdgeDeploymentPlansCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListEdgeDeploymentPlansCommandOutput>;
export declare const de_ListEdgePackagingJobsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListEdgePackagingJobsCommandOutput>;
export declare const de_ListEndpointConfigsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListEndpointConfigsCommandOutput>;
export declare const de_ListEndpointsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListEndpointsCommandOutput>;
export declare const de_ListExperimentsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListExperimentsCommandOutput>;
export declare const de_ListFeatureGroupsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListFeatureGroupsCommandOutput>;
export declare const de_ListFlowDefinitionsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListFlowDefinitionsCommandOutput>;
export declare const de_ListHubContentsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListHubContentsCommandOutput>;
export declare const de_ListHubContentVersionsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListHubContentVersionsCommandOutput>;
export declare const de_ListHubsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListHubsCommandOutput>;
export declare const de_ListHumanTaskUisCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListHumanTaskUisCommandOutput>;
export declare const de_ListHyperParameterTuningJobsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListHyperParameterTuningJobsCommandOutput>;
export declare const de_ListImagesCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListImagesCommandOutput>;
export declare const de_ListImageVersionsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListImageVersionsCommandOutput>;
export declare const de_ListInferenceComponentsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListInferenceComponentsCommandOutput>;
export declare const de_ListInferenceExperimentsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListInferenceExperimentsCommandOutput>;
export declare const de_ListInferenceRecommendationsJobsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListInferenceRecommendationsJobsCommandOutput>;
export declare const de_ListInferenceRecommendationsJobStepsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListInferenceRecommendationsJobStepsCommandOutput>;
export declare const de_ListLabelingJobsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListLabelingJobsCommandOutput>;
export declare const de_ListLabelingJobsForWorkteamCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListLabelingJobsForWorkteamCommandOutput>;
export declare const de_ListLineageGroupsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListLineageGroupsCommandOutput>;
export declare const de_ListMlflowTrackingServersCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListMlflowTrackingServersCommandOutput>;
export declare const de_ListModelBiasJobDefinitionsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListModelBiasJobDefinitionsCommandOutput>;
export declare const de_ListModelCardExportJobsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListModelCardExportJobsCommandOutput>;
export declare const de_ListModelCardsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListModelCardsCommandOutput>;
export declare const de_ListModelCardVersionsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListModelCardVersionsCommandOutput>;
export declare const de_ListModelExplainabilityJobDefinitionsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListModelExplainabilityJobDefinitionsCommandOutput>;
export declare const de_ListModelMetadataCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListModelMetadataCommandOutput>;
export declare const de_ListModelPackageGroupsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListModelPackageGroupsCommandOutput>;
export declare const de_ListModelPackagesCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListModelPackagesCommandOutput>;
export declare const de_ListModelQualityJobDefinitionsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListModelQualityJobDefinitionsCommandOutput>;
export declare const de_ListModelsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListModelsCommandOutput>;
export declare const de_ListMonitoringAlertHistoryCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListMonitoringAlertHistoryCommandOutput>;
export declare const de_ListMonitoringAlertsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListMonitoringAlertsCommandOutput>;
export declare const de_ListMonitoringExecutionsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListMonitoringExecutionsCommandOutput>;
export declare const de_ListMonitoringSchedulesCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListMonitoringSchedulesCommandOutput>;
export declare const de_ListNotebookInstanceLifecycleConfigsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListNotebookInstanceLifecycleConfigsCommandOutput>;
export declare const de_ListNotebookInstancesCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListNotebookInstancesCommandOutput>;
export declare const de_ListOptimizationJobsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListOptimizationJobsCommandOutput>;
export declare const de_ListPartnerAppsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListPartnerAppsCommandOutput>;
export declare const de_ListPipelineExecutionsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListPipelineExecutionsCommandOutput>;
export declare const de_ListPipelineExecutionStepsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListPipelineExecutionStepsCommandOutput>;
export declare const de_ListPipelineParametersForExecutionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListPipelineParametersForExecutionCommandOutput>;
export declare const de_ListPipelinesCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListPipelinesCommandOutput>;
export declare const de_ListProcessingJobsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListProcessingJobsCommandOutput>;
export declare const de_ListProjectsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListProjectsCommandOutput>;
export declare const de_ListResourceCatalogsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListResourceCatalogsCommandOutput>;
export declare const de_ListSpacesCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListSpacesCommandOutput>;
export declare const de_ListStageDevicesCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListStageDevicesCommandOutput>;
export declare const de_ListStudioLifecycleConfigsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListStudioLifecycleConfigsCommandOutput>;
export declare const de_ListSubscribedWorkteamsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListSubscribedWorkteamsCommandOutput>;
export declare const de_ListTagsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListTagsCommandOutput>;
export declare const de_ListTrainingJobsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListTrainingJobsCommandOutput>;
export declare const de_ListTrainingJobsForHyperParameterTuningJobCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListTrainingJobsForHyperParameterTuningJobCommandOutput>;
export declare const de_ListTrainingPlansCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListTrainingPlansCommandOutput>;
export declare const de_ListTransformJobsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListTransformJobsCommandOutput>;
export declare const de_ListTrialComponentsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListTrialComponentsCommandOutput>;
export declare const de_ListTrialsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListTrialsCommandOutput>;
export declare const de_ListUserProfilesCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListUserProfilesCommandOutput>;
export declare const de_ListWorkforcesCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListWorkforcesCommandOutput>;
export declare const de_ListWorkteamsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListWorkteamsCommandOutput>;
export declare const de_PutModelPackageGroupPolicyCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutModelPackageGroupPolicyCommandOutput>;
export declare const de_QueryLineageCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<QueryLineageCommandOutput>;
export declare const de_RegisterDevicesCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<RegisterDevicesCommandOutput>;
export declare const de_RenderUiTemplateCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<RenderUiTemplateCommandOutput>;
export declare const de_RetryPipelineExecutionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<RetryPipelineExecutionCommandOutput>;
export declare const de_SearchCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<SearchCommandOutput>;
export declare const de_SearchTrainingPlanOfferingsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<SearchTrainingPlanOfferingsCommandOutput>;
export declare const de_SendPipelineExecutionStepFailureCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<SendPipelineExecutionStepFailureCommandOutput>;
export declare const de_SendPipelineExecutionStepSuccessCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<SendPipelineExecutionStepSuccessCommandOutput>;
export declare const de_StartEdgeDeploymentStageCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<StartEdgeDeploymentStageCommandOutput>;
export declare const de_StartInferenceExperimentCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<StartInferenceExperimentCommandOutput>;
export declare const de_StartMlflowTrackingServerCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<StartMlflowTrackingServerCommandOutput>;
export declare const de_StartMonitoringScheduleCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<StartMonitoringScheduleCommandOutput>;
export declare const de_StartNotebookInstanceCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<StartNotebookInstanceCommandOutput>;
export declare const de_StartPipelineExecutionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<StartPipelineExecutionCommandOutput>;
export declare const de_StopAutoMLJobCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<StopAutoMLJobCommandOutput>;
export declare const de_StopCompilationJobCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<StopCompilationJobCommandOutput>;
export declare const de_StopEdgeDeploymentStageCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<StopEdgeDeploymentStageCommandOutput>;
export declare const de_StopEdgePackagingJobCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<StopEdgePackagingJobCommandOutput>;
export declare const de_StopHyperParameterTuningJobCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<StopHyperParameterTuningJobCommandOutput>;
export declare const de_StopInferenceExperimentCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<StopInferenceExperimentCommandOutput>;
export declare const de_StopInferenceRecommendationsJobCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<StopInferenceRecommendationsJobCommandOutput>;
export declare const de_StopLabelingJobCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<StopLabelingJobCommandOutput>;
export declare const de_StopMlflowTrackingServerCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<StopMlflowTrackingServerCommandOutput>;
export declare const de_StopMonitoringScheduleCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<StopMonitoringScheduleCommandOutput>;
export declare const de_StopNotebookInstanceCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<StopNotebookInstanceCommandOutput>;
export declare const de_StopOptimizationJobCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<StopOptimizationJobCommandOutput>;
export declare const de_StopPipelineExecutionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<StopPipelineExecutionCommandOutput>;
export declare const de_StopProcessingJobCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<StopProcessingJobCommandOutput>;
export declare const de_StopTrainingJobCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<StopTrainingJobCommandOutput>;
export declare const de_StopTransformJobCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<StopTransformJobCommandOutput>;
export declare const de_UpdateActionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateActionCommandOutput>;
export declare const de_UpdateAppImageConfigCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateAppImageConfigCommandOutput>;
export declare const de_UpdateArtifactCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateArtifactCommandOutput>;
export declare const de_UpdateClusterCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateClusterCommandOutput>;
export declare const de_UpdateClusterSchedulerConfigCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateClusterSchedulerConfigCommandOutput>;
export declare const de_UpdateClusterSoftwareCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateClusterSoftwareCommandOutput>;
export declare const de_UpdateCodeRepositoryCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateCodeRepositoryCommandOutput>;
export declare const de_UpdateComputeQuotaCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateComputeQuotaCommandOutput>;
export declare const de_UpdateContextCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateContextCommandOutput>;
export declare const de_UpdateDeviceFleetCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateDeviceFleetCommandOutput>;
export declare const de_UpdateDevicesCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateDevicesCommandOutput>;
export declare const de_UpdateDomainCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateDomainCommandOutput>;
export declare const de_UpdateEndpointCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateEndpointCommandOutput>;
export declare const de_UpdateEndpointWeightsAndCapacitiesCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateEndpointWeightsAndCapacitiesCommandOutput>;
export declare const de_UpdateExperimentCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateExperimentCommandOutput>;
export declare const de_UpdateFeatureGroupCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateFeatureGroupCommandOutput>;
export declare const de_UpdateFeatureMetadataCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateFeatureMetadataCommandOutput>;
export declare const de_UpdateHubCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateHubCommandOutput>;
export declare const de_UpdateHubContentCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateHubContentCommandOutput>;
export declare const de_UpdateHubContentReferenceCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateHubContentReferenceCommandOutput>;
export declare const de_UpdateImageCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateImageCommandOutput>;
export declare const de_UpdateImageVersionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateImageVersionCommandOutput>;
export declare const de_UpdateInferenceComponentCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateInferenceComponentCommandOutput>;
export declare const de_UpdateInferenceComponentRuntimeConfigCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateInferenceComponentRuntimeConfigCommandOutput>;
export declare const de_UpdateInferenceExperimentCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateInferenceExperimentCommandOutput>;
export declare const de_UpdateMlflowTrackingServerCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateMlflowTrackingServerCommandOutput>;
export declare const de_UpdateModelCardCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateModelCardCommandOutput>;
export declare const de_UpdateModelPackageCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateModelPackageCommandOutput>;
export declare const de_UpdateMonitoringAlertCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateMonitoringAlertCommandOutput>;
export declare const de_UpdateMonitoringScheduleCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateMonitoringScheduleCommandOutput>;
export declare const de_UpdateNotebookInstanceCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateNotebookInstanceCommandOutput>;
export declare const de_UpdateNotebookInstanceLifecycleConfigCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateNotebookInstanceLifecycleConfigCommandOutput>;
export declare const de_UpdatePartnerAppCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdatePartnerAppCommandOutput>;
export declare const de_UpdatePipelineCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdatePipelineCommandOutput>;
export declare const de_UpdatePipelineExecutionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdatePipelineExecutionCommandOutput>;
export declare const de_UpdateProjectCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateProjectCommandOutput>;
export declare const de_UpdateSpaceCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateSpaceCommandOutput>;
export declare const de_UpdateTrainingJobCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateTrainingJobCommandOutput>;
export declare const de_UpdateTrialCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateTrialCommandOutput>;
export declare const de_UpdateTrialComponentCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateTrialComponentCommandOutput>;
export declare const de_UpdateUserProfileCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateUserProfileCommandOutput>;
export declare const de_UpdateWorkforceCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateWorkforceCommandOutput>;
export declare const de_UpdateWorkteamCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateWorkteamCommandOutput>;
