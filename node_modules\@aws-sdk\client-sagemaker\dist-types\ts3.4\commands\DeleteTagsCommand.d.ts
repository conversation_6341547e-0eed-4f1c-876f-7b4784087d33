import { Command as $Command } from "@smithy/smithy-client";
import { <PERSON>ada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DeleteTagsInput, DeleteTagsOutput } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteTagsCommandInput extends DeleteTagsInput {}
export interface DeleteTagsCommandOutput
  extends DeleteTagsOutput,
    __MetadataBearer {}
declare const DeleteTagsCommand_base: {
  new (
    input: DeleteTagsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteTagsCommandInput,
    DeleteTagsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteTagsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteTagsCommandInput,
    DeleteTagsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteTagsCommand extends DeleteTagsCommand_base {
  protected static __types: {
    api: {
      input: DeleteTagsInput;
      output: {};
    };
    sdk: {
      input: DeleteTagsCommandInput;
      output: DeleteTagsCommandOutput;
    };
  };
}
