import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeActionRequest,
  DescribeActionResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeActionCommandInput extends DescribeActionRequest {}
export interface DescribeActionCommandOutput
  extends DescribeActionResponse,
    __MetadataBearer {}
declare const DescribeActionCommand_base: {
  new (
    input: DescribeActionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeActionCommandInput,
    DescribeActionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeActionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeActionCommandInput,
    DescribeActionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeActionCommand extends DescribeActionCommand_base {
  protected static __types: {
    api: {
      input: DescribeActionRequest;
      output: DescribeActionResponse;
    };
    sdk: {
      input: DescribeActionCommandInput;
      output: DescribeActionCommandOutput;
    };
  };
}
