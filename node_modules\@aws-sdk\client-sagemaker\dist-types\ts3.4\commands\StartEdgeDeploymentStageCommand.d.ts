import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { StartEdgeDeploymentStageRequest } from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface StartEdgeDeploymentStageCommandInput
  extends StartEdgeDeploymentStageRequest {}
export interface StartEdgeDeploymentStageCommandOutput
  extends __MetadataBearer {}
declare const StartEdgeDeploymentStageCommand_base: {
  new (
    input: StartEdgeDeploymentStageCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StartEdgeDeploymentStageCommandInput,
    StartEdgeDeploymentStageCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: StartEdgeDeploymentStageCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StartEdgeDeploymentStageCommandInput,
    StartEdgeDeploymentStageCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class StartEdgeDeploymentStageCommand extends StartEdgeDeploymentStageCommand_base {
  protected static __types: {
    api: {
      input: StartEdgeDeploymentStageRequest;
      output: {};
    };
    sdk: {
      input: StartEdgeDeploymentStageCommandInput;
      output: StartEdgeDeploymentStageCommandOutput;
    };
  };
}
