import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DeleteImageVersionRequest,
  DeleteImageVersionResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteImageVersionCommandInput
  extends DeleteImageVersionRequest {}
export interface DeleteImageVersionCommandOutput
  extends DeleteImageVersionResponse,
    __MetadataBearer {}
declare const DeleteImageVersionCommand_base: {
  new (
    input: DeleteImageVersionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteImageVersionCommandInput,
    DeleteImageVersionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteImageVersionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteImageVersionCommandInput,
    DeleteImageVersionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteImageVersionCommand extends DeleteImageVersionCommand_base {
  protected static __types: {
    api: {
      input: DeleteImageVersionRequest;
      output: {};
    };
    sdk: {
      input: DeleteImageVersionCommandInput;
      output: DeleteImageVersionCommandOutput;
    };
  };
}
