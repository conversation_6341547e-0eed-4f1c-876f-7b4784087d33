import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { CreateEdgePackagingJobRequest } from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateEdgePackagingJobCommandInput
  extends CreateEdgePackagingJobRequest {}
export interface CreateEdgePackagingJobCommandOutput extends __MetadataBearer {}
declare const CreateEdgePackagingJobCommand_base: {
  new (
    input: CreateEdgePackagingJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateEdgePackagingJobCommandInput,
    CreateEdgePackagingJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateEdgePackagingJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateEdgePackagingJobCommandInput,
    CreateEdgePackagingJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateEdgePackagingJobCommand extends CreateEdgePackagingJobCommand_base {
  protected static __types: {
    api: {
      input: CreateEdgePackagingJobRequest;
      output: {};
    };
    sdk: {
      input: CreateEdgePackagingJobCommandInput;
      output: CreateEdgePackagingJobCommandOutput;
    };
  };
}
