import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { StopEdgePackagingJobRequest } from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface StopEdgePackagingJobCommandInput
  extends StopEdgePackagingJobRequest {}
export interface StopEdgePackagingJobCommandOutput extends __MetadataBearer {}
declare const StopEdgePackagingJobCommand_base: {
  new (
    input: StopEdgePackagingJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StopEdgePackagingJobCommandInput,
    StopEdgePackagingJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: StopEdgePackagingJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StopEdgePackagingJobCommandInput,
    StopEdgePackagingJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class StopEdgePackagingJobCommand extends StopEdgePackagingJobCommand_base {
  protected static __types: {
    api: {
      input: StopEdgePackagingJobRequest;
      output: {};
    };
    sdk: {
      input: StopEdgePackagingJobCommandInput;
      output: StopEdgePackagingJobCommandOutput;
    };
  };
}
