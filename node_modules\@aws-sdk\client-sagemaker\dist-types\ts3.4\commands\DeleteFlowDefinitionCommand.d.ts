import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DeleteFlowDefinitionRequest,
  DeleteFlowDefinitionResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteFlowDefinitionCommandInput
  extends DeleteFlowDefinitionRequest {}
export interface DeleteFlowDefinitionCommandOutput
  extends DeleteFlowDefinitionResponse,
    __MetadataBearer {}
declare const DeleteFlowDefinitionCommand_base: {
  new (
    input: DeleteFlowDefinitionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteFlowDefinitionCommandInput,
    DeleteFlowDefinitionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteFlowDefinitionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteFlowDefinitionCommandInput,
    DeleteFlowDefinitionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteFlowDefinitionCommand extends DeleteFlowDefinitionCommand_base {
  protected static __types: {
    api: {
      input: DeleteFlowDefinitionRequest;
      output: {};
    };
    sdk: {
      input: DeleteFlowDefinitionCommandInput;
      output: DeleteFlowDefinitionCommandOutput;
    };
  };
}
