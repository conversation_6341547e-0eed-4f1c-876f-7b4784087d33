import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DeleteHumanTaskUiRequest,
  DeleteHumanTaskUiResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteHumanTaskUiCommandInput
  extends DeleteHumanTaskUiRequest {}
export interface DeleteHumanTaskUiCommandOutput
  extends DeleteHumanTaskUiResponse,
    __MetadataBearer {}
declare const DeleteHumanTaskUiCommand_base: {
  new (
    input: DeleteHumanTaskUiCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteHumanTaskUiCommandInput,
    DeleteHumanTaskUiCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteHumanTaskUiCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteHumanTaskUiCommandInput,
    DeleteHumanTaskUiCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteHumanTaskUiCommand extends DeleteHumanTaskUiCommand_base {
  protected static __types: {
    api: {
      input: DeleteHumanTaskUiRequest;
      output: {};
    };
    sdk: {
      input: DeleteHumanTaskUiCommandInput;
      output: DeleteHumanTaskUiCommandOutput;
    };
  };
}
