import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateHubContentReferenceRequest,
  CreateHubContentReferenceResponse,
} from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateHubContentReferenceCommandInput
  extends CreateHubContentReferenceRequest {}
export interface CreateHubContentReferenceCommandOutput
  extends CreateHubContentReferenceResponse,
    __MetadataBearer {}
declare const CreateHubContentReferenceCommand_base: {
  new (
    input: CreateHubContentReferenceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateHubContentReferenceCommandInput,
    CreateHubContentReferenceCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateHubContentReferenceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateHubContentReferenceCommandInput,
    CreateHubContentReferenceCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateHubContentReferenceCommand extends CreateHubContentReferenceCommand_base {
  protected static __types: {
    api: {
      input: CreateHubContentReferenceRequest;
      output: CreateHubContentReferenceResponse;
    };
    sdk: {
      input: CreateHubContentReferenceCommandInput;
      output: CreateHubContentReferenceCommandOutput;
    };
  };
}
