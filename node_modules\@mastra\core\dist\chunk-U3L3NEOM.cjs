'use strict';

var chunkYEULQPUY_cjs = require('./chunk-YEULQPUY.cjs');
var chunkLABUWBKX_cjs = require('./chunk-LABUWBKX.cjs');
var crypto = require('crypto');
var ai = require('ai');
var jsonSchemaToZod = require('json-schema-to-zod');
var zod = require('zod');
var zodFromJsonSchema = require('zod-from-json-schema');
var zodToJsonSchema = require('zod-to-json-schema');

function _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }

var jsonSchemaToZod__default = /*#__PURE__*/_interopDefault(jsonSchemaToZod);

// src/tools/tool.ts
var Tool = class {
  id;
  description;
  inputSchema;
  outputSchema;
  execute;
  mastra;
  constructor(opts) {
    this.id = opts.id;
    this.description = opts.description;
    this.inputSchema = opts.inputSchema;
    this.outputSchema = opts.outputSchema;
    this.execute = opts.execute;
    this.mastra = opts.mastra;
  }
};
function createTool(opts) {
  return new Tool(opts);
}
var ALL_STRING_CHECKS = ["regex", "emoji", "email", "url", "uuid", "cuid", "min", "max"];
var ALL_NUMBER_CHECKS = [
  "min",
  // gte internally
  "max",
  // lte internally
  "multipleOf"
];
var ALL_ARRAY_CHECKS = ["min", "max", "length"];
var UNSUPPORTED_ZOD_TYPES = ["ZodIntersection", "ZodNever", "ZodNull", "ZodTuple", "ZodUndefined"];
var SUPPORTED_ZOD_TYPES = [
  "ZodObject",
  "ZodArray",
  "ZodUnion",
  "ZodString",
  "ZodNumber",
  "ZodDate",
  "ZodAny",
  "ZodDefault"
];
var ToolCompatibility = class extends chunkYEULQPUY_cjs.MastraBase {
  model;
  constructor(model) {
    super({ name: "SchemaCompatibility" });
    this.model = model;
  }
  getModel() {
    return this.model;
  }
  applyZodSchemaCompatibility(zodSchema2) {
    const newSchema = zod.z.object(
      Object.entries(zodSchema2.shape || {}).reduce(
        (acc, [key, value]) => ({
          ...acc,
          [key]: this.processZodType(value)
        }),
        {}
      )
    );
    return { schema: newSchema };
  }
  defaultZodObjectHandler(value) {
    const zodObject = value;
    const processedShape = Object.entries(zodObject.shape || {}).reduce(
      (acc, [key, propValue]) => {
        const typedPropValue = propValue;
        const processedValue = this.processZodType(typedPropValue);
        acc[key] = processedValue;
        return acc;
      },
      {}
    );
    let result = zod.z.object(processedShape);
    if (value.description) {
      result = result.describe(value.description);
    }
    return result;
  }
  mergeParameterDescription(description, constraints) {
    if (Object.keys(constraints).length > 0) {
      return (description ? description + "\n" : "") + JSON.stringify(constraints);
    } else {
      return description;
    }
  }
  defaultUnsupportedZodTypeHandler(value, throwOnTypes = UNSUPPORTED_ZOD_TYPES) {
    if (throwOnTypes.includes(value._def.typeName)) {
      throw new Error(`${this.model.modelId} does not support zod type: ${value._def.typeName}`);
    }
    return value;
  }
  defaultZodArrayHandler(value, handleChecks = ALL_ARRAY_CHECKS) {
    const zodArray = value._def;
    const arrayType = zodArray.type;
    const constraints = {};
    if (zodArray.minLength?.value !== void 0 && handleChecks.includes("min")) {
      constraints.minLength = zodArray.minLength.value;
    }
    if (zodArray.maxLength?.value !== void 0 && handleChecks.includes("max")) {
      constraints.maxLength = zodArray.maxLength.value;
    }
    if (zodArray.exactLength?.value !== void 0 && handleChecks.includes("length")) {
      constraints.exactLength = zodArray.exactLength.value;
    }
    const processedType = arrayType._def.typeName === "ZodObject" ? this.processZodType(arrayType) : arrayType;
    let result = zod.z.array(processedType);
    if (zodArray.minLength?.value !== void 0 && !handleChecks.includes("min")) {
      result = result.min(zodArray.minLength.value);
    }
    if (zodArray.maxLength?.value !== void 0 && !handleChecks.includes("max")) {
      result = result.max(zodArray.maxLength.value);
    }
    if (zodArray.exactLength?.value !== void 0 && !handleChecks.includes("length")) {
      result = result.length(zodArray.exactLength.value);
    }
    const description = this.mergeParameterDescription(value.description, constraints);
    if (description) {
      result = result.describe(description);
    }
    return result;
  }
  defaultZodUnionHandler(value) {
    const zodUnion = value;
    const processedOptions = zodUnion._def.options.map((option) => this.processZodType(option));
    if (processedOptions.length < 2) throw new Error("Union must have at least 2 options");
    let result = zod.z.union(processedOptions);
    if (value.description) {
      result = result.describe(value.description);
    }
    return result;
  }
  defaultZodStringHandler(value, handleChecks = ALL_STRING_CHECKS) {
    const zodString = value;
    const constraints = {};
    const checks = zodString._def.checks || [];
    const newChecks = [];
    for (const check of checks) {
      if ("kind" in check) {
        if (handleChecks.includes(check.kind)) {
          switch (check.kind) {
            case "regex": {
              constraints.regex = {
                pattern: check.regex.source,
                flags: check.regex.flags
              };
              break;
            }
            case "emoji": {
              constraints.emoji = true;
              break;
            }
            case "email": {
              constraints.email = true;
              break;
            }
            case "url": {
              constraints.url = true;
              break;
            }
            case "uuid": {
              constraints.uuid = true;
              break;
            }
            case "cuid": {
              constraints.cuid = true;
              break;
            }
            case "min": {
              constraints.minLength = check.value;
              break;
            }
            case "max": {
              constraints.maxLength = check.value;
              break;
            }
          }
        } else {
          newChecks.push(check);
        }
      }
    }
    let result = zod.z.string();
    for (const check of newChecks) {
      result = result._addCheck(check);
    }
    const description = this.mergeParameterDescription(value.description, constraints);
    if (description) {
      result = result.describe(description);
    }
    return result;
  }
  defaultZodNumberHandler(value, handleChecks = ALL_NUMBER_CHECKS) {
    const zodNumber = value;
    const constraints = {};
    const checks = zodNumber._def.checks || [];
    const newChecks = [];
    for (const check of checks) {
      if ("kind" in check) {
        if (handleChecks.includes(check.kind)) {
          switch (check.kind) {
            case "min":
              if (check.inclusive) {
                constraints.gte = check.value;
              } else {
                constraints.gt = check.value;
              }
              break;
            case "max":
              if (check.inclusive) {
                constraints.lte = check.value;
              } else {
                constraints.lt = check.value;
              }
              break;
            case "multipleOf": {
              constraints.multipleOf = check.value;
              break;
            }
          }
        } else {
          newChecks.push(check);
        }
      }
    }
    let result = zod.z.number();
    for (const check of newChecks) {
      switch (check.kind) {
        case "int":
          result = result.int();
          break;
        case "finite":
          result = result.finite();
          break;
        default:
          result = result._addCheck(check);
      }
    }
    const description = this.mergeParameterDescription(value.description, constraints);
    if (description) {
      result = result.describe(description);
    }
    return result;
  }
  defaultZodDateHandler(value) {
    const zodDate = value;
    const constraints = {};
    const checks = zodDate._def.checks || [];
    for (const check of checks) {
      if ("kind" in check) {
        switch (check.kind) {
          case "min":
            const minDate = new Date(check.value);
            if (!isNaN(minDate.getTime())) {
              constraints.minDate = minDate.toISOString();
            }
            break;
          case "max":
            const maxDate = new Date(check.value);
            if (!isNaN(maxDate.getTime())) {
              constraints.maxDate = maxDate.toISOString();
            }
            break;
        }
      }
    }
    constraints.dateFormat = "date-time";
    let result = zod.z.string().describe("date-time");
    const description = this.mergeParameterDescription(value.description, constraints);
    if (description) {
      result = result.describe(description);
    }
    return result;
  }
  defaultZodOptionalHandler(value, handleTypes = SUPPORTED_ZOD_TYPES) {
    if (handleTypes.includes(value._def.innerType._def.typeName)) {
      return this.processZodType(value._def.innerType).optional();
    } else {
      return value;
    }
  }
  process(tool) {
    if (isVercelTool(tool)) {
      return {
        description: tool.description,
        // TODO: should we also process vercel tool params?
        parameters: ai.zodSchema(convertVercelToolParameters(tool))
      };
    }
    const { schema } = this.applyZodSchemaCompatibility(tool.inputSchema);
    return {
      description: tool.description,
      parameters: convertZodSchemaToAISDKSchema(schema, this.getSchemaTarget())
    };
  }
};

// src/tools/tool-compatibility/provider-compats/anthropic.ts
var AnthropicToolCompat = class extends ToolCompatibility {
  constructor(model) {
    super(model);
  }
  getSchemaTarget() {
    return "jsonSchema7";
  }
  shouldApply() {
    return this.getModel().modelId.includes("claude");
  }
  processZodType(value) {
    switch (value._def.typeName) {
      case "ZodOptional":
        const handleTypes = ["ZodObject", "ZodArray", "ZodUnion", "ZodNever", "ZodUndefined"];
        if (this.getModel().modelId.includes("claude-3.5-haiku")) handleTypes.push("ZodString");
        if (this.getModel().modelId.includes("claude-3.7")) handleTypes.push("ZodTuple");
        return this.defaultZodOptionalHandler(value, handleTypes);
      case "ZodObject": {
        return this.defaultZodObjectHandler(value);
      }
      case "ZodArray": {
        return this.defaultZodArrayHandler(value, []);
      }
      case "ZodUnion": {
        return this.defaultZodUnionHandler(value);
      }
      // the claude-3.5-haiku model support these properties but the model doesn't respect them, but it respects them when they're
      // added to the tool description
      case "ZodString": {
        if (this.getModel().modelId.includes("claude-3.5-haiku")) {
          return this.defaultZodStringHandler(value, ["max", "min"]);
        } else {
          return value;
        }
      }
      default:
        if (this.getModel().modelId.includes("claude-3.7")) {
          return this.defaultUnsupportedZodTypeHandler(value, ["ZodNever", "ZodTuple", "ZodUndefined"]);
        } else {
          return this.defaultUnsupportedZodTypeHandler(value, ["ZodNever", "ZodUndefined"]);
        }
    }
  }
};

// src/tools/tool-compatibility/provider-compats/deepseek.ts
var DeepSeekToolCompat = class extends ToolCompatibility {
  constructor(model) {
    super(model);
  }
  getSchemaTarget() {
    return "jsonSchema7";
  }
  shouldApply() {
    return this.getModel().modelId.includes("deepseek");
  }
  processZodType(value) {
    switch (value._def.typeName) {
      case "ZodOptional":
        return this.defaultZodOptionalHandler(value, ["ZodObject", "ZodArray", "ZodUnion", "ZodString", "ZodNumber"]);
      case "ZodObject": {
        return this.defaultZodObjectHandler(value);
      }
      case "ZodArray": {
        return this.defaultZodArrayHandler(value, ["min", "max"]);
      }
      case "ZodUnion": {
        return this.defaultZodUnionHandler(value);
      }
      case "ZodString": {
        return this.defaultZodStringHandler(value);
      }
      default:
        return value;
    }
  }
};

// src/tools/tool-compatibility/provider-compats/google.ts
var GoogleToolCompat = class extends ToolCompatibility {
  constructor(model) {
    super(model);
  }
  getSchemaTarget() {
    return "jsonSchema7";
  }
  shouldApply() {
    return this.getModel().provider.includes("google") || this.getModel().modelId.includes("google");
  }
  processZodType(value) {
    switch (value._def.typeName) {
      case "ZodOptional":
        return this.defaultZodOptionalHandler(value, [
          "ZodObject",
          "ZodArray",
          "ZodUnion",
          "ZodString",
          "ZodNumber",
          ...UNSUPPORTED_ZOD_TYPES
        ]);
      case "ZodObject": {
        return this.defaultZodObjectHandler(value);
      }
      case "ZodArray": {
        return this.defaultZodArrayHandler(value, []);
      }
      case "ZodUnion": {
        return this.defaultZodUnionHandler(value);
      }
      // Google models support these properties but the model doesn't respect them, but it respects them when they're
      // added to the tool description
      case "ZodString": {
        return this.defaultZodStringHandler(value);
      }
      case "ZodNumber": {
        return this.defaultZodNumberHandler(value);
      }
      default:
        return this.defaultUnsupportedZodTypeHandler(value);
    }
  }
};

// src/tools/tool-compatibility/provider-compats/meta.ts
var MetaToolCompat = class extends ToolCompatibility {
  constructor(model) {
    super(model);
  }
  getSchemaTarget() {
    return "jsonSchema7";
  }
  shouldApply() {
    return this.getModel().modelId.includes("meta");
  }
  processZodType(value) {
    switch (value._def.typeName) {
      case "ZodOptional":
        return this.defaultZodOptionalHandler(value, ["ZodObject", "ZodArray", "ZodUnion", "ZodString", "ZodNumber"]);
      case "ZodObject": {
        return this.defaultZodObjectHandler(value);
      }
      case "ZodArray": {
        return this.defaultZodArrayHandler(value, ["min", "max"]);
      }
      case "ZodUnion": {
        return this.defaultZodUnionHandler(value);
      }
      case "ZodNumber": {
        return this.defaultZodNumberHandler(value);
      }
      case "ZodString": {
        return this.defaultZodStringHandler(value);
      }
      default:
        return value;
    }
  }
};

// src/tools/tool-compatibility/provider-compats/openai.ts
var OpenAIToolCompat = class extends ToolCompatibility {
  constructor(model) {
    super(model);
  }
  getSchemaTarget() {
    return `jsonSchema7`;
  }
  shouldApply() {
    if (!this.getModel().supportsStructuredOutputs && (this.getModel().provider.includes(`openai`) || this.getModel().modelId.includes(`openai`))) {
      return true;
    }
    return false;
  }
  processZodType(value) {
    switch (value._def.typeName) {
      case "ZodOptional":
        return this.defaultZodOptionalHandler(value, [
          "ZodObject",
          "ZodArray",
          "ZodUnion",
          "ZodString",
          "ZodNever",
          "ZodUndefined",
          "ZodTuple"
        ]);
      case "ZodObject": {
        return this.defaultZodObjectHandler(value);
      }
      case "ZodUnion": {
        return this.defaultZodUnionHandler(value);
      }
      case "ZodArray": {
        return this.defaultZodArrayHandler(value);
      }
      case "ZodString": {
        const model = this.getModel();
        const checks = ["emoji"];
        if (model.modelId.includes("gpt-4o-mini")) {
          checks.push("regex");
        }
        return this.defaultZodStringHandler(value, checks);
      }
      default:
        return this.defaultUnsupportedZodTypeHandler(value, ["ZodNever", "ZodUndefined", "ZodTuple"]);
    }
  }
};
var OpenAIReasoningToolCompat = class extends ToolCompatibility {
  constructor(model) {
    super(model);
  }
  getSchemaTarget() {
    return `openApi3`;
  }
  isReasoningModel() {
    return this.getModel().modelId.includes(`o3`) || this.getModel().modelId.includes(`o4`);
  }
  shouldApply() {
    if ((this.getModel().supportsStructuredOutputs || this.isReasoningModel()) && (this.getModel().provider.includes(`openai`) || this.getModel().modelId.includes(`openai`))) {
      return true;
    }
    return false;
  }
  processZodType(value) {
    switch (value._def.typeName) {
      case "ZodOptional":
        const innerZodType = this.processZodType(value._def.innerType);
        return innerZodType.nullable();
      case "ZodObject": {
        return this.defaultZodObjectHandler(value);
      }
      case "ZodArray": {
        return this.defaultZodArrayHandler(value);
      }
      case "ZodUnion": {
        return this.defaultZodUnionHandler(value);
      }
      case "ZodDefault": {
        const defaultDef = value._def;
        const innerType = defaultDef.innerType;
        const defaultValue = defaultDef.defaultValue();
        const constraints = {};
        if (defaultValue !== void 0) {
          constraints.defaultValue = defaultValue;
        }
        const description = this.mergeParameterDescription(value.description, constraints);
        let result = this.processZodType(innerType);
        if (description) {
          result = result.describe(description);
        }
        return result;
      }
      case "ZodNumber": {
        return this.defaultZodNumberHandler(value);
      }
      case "ZodString": {
        return this.defaultZodStringHandler(value);
      }
      case "ZodDate": {
        return this.defaultZodDateHandler(value);
      }
      case "ZodAny": {
        return zod.z.string().describe(
          (value.description ?? "") + `
Argument was an "any" type, but you (the LLM) do not support "any", so it was cast to a "string" type`
        );
      }
      default:
        return this.defaultUnsupportedZodTypeHandler(value);
    }
  }
};

// src/tools/tool-compatibility/builder.ts
function convertZodSchemaToAISDKSchema(zodSchema2, target = "jsonSchema7") {
  return ai.jsonSchema(
    zodToJsonSchema.zodToJsonSchema(zodSchema2, {
      $refStrategy: "none",
      target
    }),
    {
      validate: (value) => {
        const result = zodSchema2.safeParse(value);
        return result.success ? { success: true, value: result.data } : { success: false, error: result.error };
      }
    }
  );
}
function convertVercelToolParameters(tool) {
  const schema = tool.parameters ?? zod.z.object({});
  if (isZodType(schema)) {
    return schema;
  } else {
    const jsonSchemaToConvert = "jsonSchema" in schema ? schema.jsonSchema : schema;
    try {
      return zodFromJsonSchema.convertJsonSchemaToZod(jsonSchemaToConvert);
    } catch (e) {
      const errorMessage = `[CoreToolBuilder] Failed to convert Vercel tool JSON schema parameters to Zod. Original schema: ${JSON.stringify(jsonSchemaToConvert)}`;
      console.error(errorMessage, e);
      throw new Error(errorMessage + (e instanceof Error ? `
${e.stack}` : "\nUnknown error object"));
    }
  }
}
function convertInputSchema(tool) {
  const schema = tool.inputSchema ?? zod.z.object({});
  if (isZodType(schema)) {
    return schema;
  } else {
    try {
      return zodFromJsonSchema.convertJsonSchemaToZod(schema);
    } catch (e) {
      const errorMessage = `[CoreToolBuilder] Failed to convert tool input JSON schema to Zod. Original schema: ${JSON.stringify(schema)}`;
      console.error(errorMessage, e);
      throw new Error(errorMessage + (e instanceof Error ? `
${e.stack}` : "\nUnknown error object"));
    }
  }
}
var CoreToolBuilder = class extends chunkYEULQPUY_cjs.MastraBase {
  originalTool;
  options;
  logType;
  constructor(input) {
    super({ name: "CoreToolBuilder" });
    this.originalTool = input.originalTool;
    this.options = input.options;
    this.logType = input.logType;
  }
  // Helper to get parameters based on tool type
  getParameters = () => {
    if (isVercelTool(this.originalTool)) {
      return convertVercelToolParameters(this.originalTool);
    }
    return convertInputSchema(this.originalTool);
  };
  // For provider-defined tools, we need to include all required properties
  buildProviderTool(tool) {
    if ("type" in tool && tool.type === "provider-defined" && "id" in tool && typeof tool.id === "string" && tool.id.includes(".")) {
      return {
        type: "provider-defined",
        id: tool.id,
        args: "args" in this.originalTool ? this.originalTool.args : {},
        description: tool.description,
        parameters: convertZodSchemaToAISDKSchema(this.getParameters()),
        execute: this.originalTool.execute ? this.createExecute(
          this.originalTool,
          { ...this.options, description: this.originalTool.description },
          this.logType
        ) : void 0
      };
    }
    return void 0;
  }
  createLogMessageOptions({ agentName, toolName, type }) {
    if (!agentName) {
      return {
        start: `Executing tool ${toolName}`,
        error: `Failed tool execution`
      };
    }
    const prefix = `[Agent:${agentName}]`;
    const toolType = type === "toolset" ? "toolset" : "tool";
    return {
      start: `${prefix} - Executing ${toolType} ${toolName}`,
      error: `${prefix} - Failed ${toolType} execution`
    };
  }
  createExecute(tool, options, logType) {
    const { logger, mastra: _mastra, memory: _memory, runtimeContext, ...rest } = options;
    const { start, error } = this.createLogMessageOptions({
      agentName: options.agentName,
      toolName: options.name,
      type: logType
    });
    const execFunction = async (args, execOptions) => {
      if (isVercelTool(tool)) {
        return tool?.execute?.(args, execOptions) ?? void 0;
      }
      return tool?.execute?.(
        {
          context: args,
          threadId: options.threadId,
          resourceId: options.resourceId,
          mastra: options.mastra,
          memory: options.memory,
          runId: options.runId,
          runtimeContext: options.runtimeContext ?? new chunkLABUWBKX_cjs.RuntimeContext()
        },
        execOptions
      ) ?? void 0;
    };
    return async (args, execOptions) => {
      try {
        (options.logger || this.logger).debug(start, { ...rest, args });
        return await execFunction(args, execOptions);
      } catch (err) {
        (options.logger || this.logger).error(error, { ...rest, error: err, args });
        throw err;
      }
    };
  }
  build() {
    const providerTool = this.buildProviderTool(this.originalTool);
    if (providerTool) {
      return providerTool;
    }
    const definition = {
      type: "function",
      description: this.originalTool.description,
      parameters: this.getParameters(),
      execute: this.originalTool.execute ? this.createExecute(
        this.originalTool,
        { ...this.options, description: this.originalTool.description },
        this.logType
      ) : void 0
    };
    const parametersObject = {};
    if (isVercelTool(this.originalTool)) {
      parametersObject.parameters = this.getParameters();
    } else {
      parametersObject.inputSchema = this.getParameters();
    }
    const model = this.options.model;
    const hasParameters = parametersObject.parameters || parametersObject.inputSchema;
    if (model && hasParameters) {
      for (const compat of [
        new OpenAIReasoningToolCompat(model),
        new OpenAIToolCompat(model),
        new GoogleToolCompat(model),
        new AnthropicToolCompat(model),
        new DeepSeekToolCompat(model),
        new MetaToolCompat(model)
      ]) {
        if (compat.shouldApply()) {
          return { ...definition, ...compat.process({ ...this.originalTool, ...parametersObject }) };
        }
      }
    }
    return {
      ...definition,
      parameters: convertZodSchemaToAISDKSchema(this.getParameters())
    };
  }
};

// src/utils.ts
var delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));
function deepMerge(target, source) {
  const output = { ...target };
  if (!source) return output;
  Object.keys(source).forEach((key) => {
    const targetValue = output[key];
    const sourceValue = source[key];
    if (Array.isArray(targetValue) && Array.isArray(sourceValue)) {
      output[key] = sourceValue;
    } else if (sourceValue instanceof Object && targetValue instanceof Object && !Array.isArray(sourceValue) && !Array.isArray(targetValue)) {
      output[key] = deepMerge(targetValue, sourceValue);
    } else if (sourceValue !== void 0) {
      output[key] = sourceValue;
    }
  });
  return output;
}
async function* maskStreamTags(stream, tag, options = {}) {
  const { onStart, onEnd, onMask } = options;
  const openTag = `<${tag}>`;
  const closeTag = `</${tag}>`;
  let buffer = "";
  let fullContent = "";
  let isMasking = false;
  let isBuffering = false;
  const trimOutsideDelimiter = (text, delimiter, trim) => {
    if (!text.includes(delimiter)) {
      return text;
    }
    const parts = text.split(delimiter);
    if (trim === `before-start`) {
      return `${delimiter}${parts[1]}`;
    }
    return `${parts[0]}${delimiter}`;
  };
  const startsWith = (text, pattern) => {
    if (pattern.includes(openTag.substring(0, 3))) {
      pattern = trimOutsideDelimiter(pattern, `<`, `before-start`);
    }
    return text.trim().startsWith(pattern.trim());
  };
  for await (const chunk of stream) {
    fullContent += chunk;
    if (isBuffering) buffer += chunk;
    const chunkHasTag = startsWith(chunk, openTag);
    const bufferHasTag = !chunkHasTag && isBuffering && startsWith(openTag, buffer);
    let toYieldBeforeMaskedStartTag = ``;
    if (!isMasking && (chunkHasTag || bufferHasTag)) {
      isMasking = true;
      isBuffering = false;
      const taggedTextToMask = trimOutsideDelimiter(buffer, `<`, `before-start`);
      if (taggedTextToMask !== buffer.trim()) {
        toYieldBeforeMaskedStartTag = buffer.replace(taggedTextToMask, ``);
      }
      buffer = "";
      onStart?.();
    }
    if (!isMasking && !isBuffering && startsWith(openTag, chunk) && chunk.trim() !== "") {
      isBuffering = true;
      buffer += chunk;
      continue;
    }
    if (isBuffering && buffer && !startsWith(openTag, buffer)) {
      yield buffer;
      buffer = "";
      isBuffering = false;
      continue;
    }
    if (isMasking && fullContent.includes(closeTag)) {
      onMask?.(chunk);
      onEnd?.();
      isMasking = false;
      const lastFullContent = fullContent;
      fullContent = ``;
      const textUntilEndTag = trimOutsideDelimiter(lastFullContent, closeTag, "after-end");
      if (textUntilEndTag !== lastFullContent) {
        yield lastFullContent.replace(textUntilEndTag, ``);
      }
      continue;
    }
    if (isMasking) {
      onMask?.(chunk);
      if (toYieldBeforeMaskedStartTag) {
        yield toYieldBeforeMaskedStartTag;
      }
      continue;
    }
    yield chunk;
  }
}
function resolveSerializedZodOutput(schema) {
  return Function("z", `"use strict";return (${schema});`)(zod.z);
}
function isVercelTool(tool) {
  return !!(tool && !(tool instanceof Tool) && "parameters" in tool);
}
function isZodType(value) {
  return typeof value === "object" && value !== null && "_def" in value && "parse" in value && typeof value.parse === "function" && "safeParse" in value && typeof value.safeParse === "function";
}
function createDeterministicId(input) {
  return crypto.createHash("sha256").update(input).digest("hex").slice(0, 8);
}
function setVercelToolProperties(tool) {
  const inputSchema = convertVercelToolParameters2(tool);
  const toolId = !("id" in tool) ? tool.description ? `tool-${createDeterministicId(tool.description)}` : `tool-${Math.random().toString(36).substring(2, 9)}` : tool.id;
  return {
    ...tool,
    id: toolId,
    inputSchema
  };
}
function ensureToolProperties(tools) {
  const toolsWithProperties = Object.keys(tools).reduce((acc, key) => {
    const tool = tools?.[key];
    if (tool) {
      if (isVercelTool(tool)) {
        acc[key] = setVercelToolProperties(tool);
      } else {
        acc[key] = tool;
      }
    }
    return acc;
  }, {});
  return toolsWithProperties;
}
function convertVercelToolParameters2(tool) {
  const schema = tool.parameters ?? zod.z.object({});
  return isZodType(schema) ? schema : resolveSerializedZodOutput(jsonSchemaToZod__default.default(schema));
}
function makeCoreTool(originalTool, options, logType) {
  return new CoreToolBuilder({ originalTool, options, logType }).build();
}
function createMastraProxy({ mastra, logger }) {
  return new Proxy(mastra, {
    get(target, prop) {
      const hasProp = Reflect.has(target, prop);
      if (hasProp) {
        const value = Reflect.get(target, prop);
        const isFunction = typeof value === "function";
        if (isFunction) {
          return value.bind(target);
        }
        return value;
      }
      if (prop === "logger") {
        logger.warn(`Please use 'getLogger' instead, logger is deprecated`);
        return Reflect.apply(target.getLogger, target, []);
      }
      if (prop === "telemetry") {
        logger.warn(`Please use 'getTelemetry' instead, telemetry is deprecated`);
        return Reflect.apply(target.getTelemetry, target, []);
      }
      if (prop === "storage") {
        logger.warn(`Please use 'getStorage' instead, storage is deprecated`);
        return Reflect.get(target, "storage");
      }
      if (prop === "agents") {
        logger.warn(`Please use 'getAgents' instead, agents is deprecated`);
        return Reflect.apply(target.getAgents, target, []);
      }
      if (prop === "tts") {
        logger.warn(`Please use 'getTTS' instead, tts is deprecated`);
        return Reflect.apply(target.getTTS, target, []);
      }
      if (prop === "vectors") {
        logger.warn(`Please use 'getVectors' instead, vectors is deprecated`);
        return Reflect.apply(target.getVectors, target, []);
      }
      if (prop === "memory") {
        logger.warn(`Please use 'getMemory' instead, memory is deprecated`);
        return Reflect.get(target, "memory");
      }
      return Reflect.get(target, prop);
    }
  });
}
function checkEvalStorageFields(traceObject, logger) {
  const missingFields = [];
  if (!traceObject.input) missingFields.push("input");
  if (!traceObject.output) missingFields.push("output");
  if (!traceObject.agentName) missingFields.push("agent_name");
  if (!traceObject.metricName) missingFields.push("metric_name");
  if (!traceObject.instructions) missingFields.push("instructions");
  if (!traceObject.globalRunId) missingFields.push("global_run_id");
  if (!traceObject.runId) missingFields.push("run_id");
  if (missingFields.length > 0) {
    if (logger) {
      logger.warn("Skipping evaluation storage due to missing required fields", {
        missingFields,
        runId: traceObject.runId,
        agentName: traceObject.agentName
      });
    } else {
      console.warn("Skipping evaluation storage due to missing required fields", {
        missingFields,
        runId: traceObject.runId,
        agentName: traceObject.agentName
      });
    }
    return false;
  }
  return true;
}
function detectSingleMessageCharacteristics(message) {
  if (typeof message === "object" && message !== null && (message.role === "function" || // UI-only role
  message.role === "data" || // UI-only role
  "toolInvocations" in message || // UI-specific field
  "parts" in message || // UI-specific field
  "experimental_attachments" in message)) {
    return "has-ui-specific-parts";
  } else if (typeof message === "object" && message !== null && "content" in message && (Array.isArray(message.content) || // Core messages can have array content
  "experimental_providerMetadata" in message || "providerOptions" in message)) {
    return "has-core-specific-parts";
  } else if (typeof message === "object" && message !== null && "role" in message && "content" in message && typeof message.content === "string" && ["system", "user", "assistant", "tool"].includes(message.role)) {
    return "message";
  } else {
    return "other";
  }
}
function isUiMessage(message) {
  return detectSingleMessageCharacteristics(message) === `has-ui-specific-parts`;
}
function isCoreMessage(message) {
  return [`has-core-specific-parts`, `message`].includes(detectSingleMessageCharacteristics(message));
}
function ensureAllMessagesAreCoreMessages(messages) {
  return messages.map((message) => {
    if (isUiMessage(message)) {
      return ai.convertToCoreMessages([message]);
    }
    if (isCoreMessage(message)) {
      return message;
    }
    const characteristics = detectSingleMessageCharacteristics(message);
    throw new Error(
      `Message does not appear to be a core message or a UI message but must be one of the two, found "${characteristics}" type for message:

${JSON.stringify(message, null, 2)}
`
    );
  }).flat();
}
var SQL_IDENTIFIER_PATTERN = /^[a-zA-Z_][a-zA-Z0-9_]*$/;
function parseSqlIdentifier(name, kind = "identifier") {
  if (!SQL_IDENTIFIER_PATTERN.test(name) || name.length > 63) {
    throw new Error(
      `Invalid ${kind}: ${name}. Must start with a letter or underscore, contain only letters, numbers, or underscores, and be at most 63 characters long.`
    );
  }
  return name;
}
function parseFieldKey(key) {
  if (!key) throw new Error("Field key cannot be empty");
  const segments = key.split(".");
  for (const segment of segments) {
    if (!SQL_IDENTIFIER_PATTERN.test(segment) || segment.length > 63) {
      throw new Error(`Invalid field key segment: ${segment} in ${key}`);
    }
  }
  return key;
}

exports.Tool = Tool;
exports.checkEvalStorageFields = checkEvalStorageFields;
exports.createMastraProxy = createMastraProxy;
exports.createTool = createTool;
exports.deepMerge = deepMerge;
exports.delay = delay;
exports.ensureAllMessagesAreCoreMessages = ensureAllMessagesAreCoreMessages;
exports.ensureToolProperties = ensureToolProperties;
exports.isCoreMessage = isCoreMessage;
exports.isUiMessage = isUiMessage;
exports.isVercelTool = isVercelTool;
exports.isZodType = isZodType;
exports.makeCoreTool = makeCoreTool;
exports.maskStreamTags = maskStreamTags;
exports.parseFieldKey = parseFieldKey;
exports.parseSqlIdentifier = parseSqlIdentifier;
exports.resolveSerializedZodOutput = resolveSerializedZodOutput;
