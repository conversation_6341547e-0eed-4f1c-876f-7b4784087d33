import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DeleteFeatureGroupRequest } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteFeatureGroupCommandInput
  extends DeleteFeatureGroupRequest {}
export interface DeleteFeatureGroupCommandOutput extends __MetadataBearer {}
declare const DeleteFeatureGroupCommand_base: {
  new (
    input: DeleteFeatureGroupCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteFeatureGroupCommandInput,
    DeleteFeatureGroupCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteFeatureGroupCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteFeatureGroupCommandInput,
    DeleteFeatureGroupCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteFeatureGroupCommand extends DeleteFeatureGroupCommand_base {
  protected static __types: {
    api: {
      input: DeleteFeatureGroupRequest;
      output: {};
    };
    sdk: {
      input: DeleteFeatureGroupCommandInput;
      output: DeleteFeatureGroupCommandOutput;
    };
  };
}
