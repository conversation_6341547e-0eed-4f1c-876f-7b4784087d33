import { Paginator } from "@smithy/types";
import {
  ListPipelineExecutionStepsCommandInput,
  ListPipelineExecutionStepsCommandOutput,
} from "../commands/ListPipelineExecutionStepsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
export declare const paginateListPipelineExecutionSteps: (
  config: SageMakerPaginationConfiguration,
  input: ListPipelineExecutionStepsCommandInput,
  ...rest: any[]
) => Paginator<ListPipelineExecutionStepsCommandOutput>;
