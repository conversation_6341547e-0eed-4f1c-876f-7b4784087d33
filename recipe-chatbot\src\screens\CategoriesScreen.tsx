import { Ionicons } from '@expo/vector-icons';
import React, { useEffect, useState } from 'react';
import {
    Alert,
    Dimensions,
    RefreshControl,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import { recipeService } from '../services/RecipeService';

const { width } = Dimensions.get('window');

interface Category {
  id: string;
  title: string;
  icon: keyof typeof Ionicons.glyphMap;
  color: string;
  description: string;
}

export default function CategoriesScreen({ route }: any) {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [categoryRecipes, setCategoryRecipes] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const categories: Category[] = [
    {
      id: 'ana-yemek',
      title: '<PERSON> Yemekler',
      icon: 'restaurant-outline',
      color: '#FF6B35',
      description: 'Et, tavuk, balık ve sebze yemekleri'
    },
    {
      id: 'corba',
      title: 'Çorbalar',
      icon: 'cafe-outline',
      color: '#4CAF50',
      description: 'Sıcak ve besleyici çorba tarifleri'
    },
    {
      id: 'tatli',
      title: 'Tatlılar',
      icon: 'ice-cream-outline',
      color: '#E91E63',
      description: 'Sütlü, şerbetli ve meyveli tatlılar'
    },
    {
      id: 'salata',
      title: 'Salatalar',
      icon: 'leaf-outline',
      color: '#8BC34A',
      description: 'Taze ve sağlıklı salata tarifleri'
    },
    {
      id: 'hizli',
      title: 'Hızlı Tarifler',
      icon: 'flash-outline',
      color: '#FF9800',
      description: '30 dakikada hazır tarifler'
    },
    {
      id: 'saglikli',
      title: 'Sağlıklı',
      icon: 'fitness-outline',
      color: '#009688',
      description: 'Düşük kalorili ve besleyici tarifler'
    },
    {
      id: 'kahvalti',
      title: 'Kahvaltı',
      icon: 'sunny-outline',
      color: '#FFC107',
      description: 'Güne güzel başlayacağınız tarifler'
    },
    {
      id: 'atistirmalik',
      title: 'Atıştırmalık',
      icon: 'pizza-outline',
      color: '#795548',
      description: 'Ara öğünler için lezzetli tarifler'
    },
  ];

  useEffect(() => {
    // Route'dan gelen kategori varsa otomatik seç
    if (route?.params?.category) {
      const categoryMap: { [key: string]: string } = {
        'Hızlı': 'hizli',
        'Sağlıklı': 'saglikli',
        'Tatlı': 'tatli',
      };
      const categoryId = categoryMap[route.params.category];
      if (categoryId) {
        handleCategoryPress(categoryId);
      }
    }
  }, [route?.params?.category]);

  const handleCategoryPress = async (categoryId: string) => {
    setSelectedCategory(categoryId);
    setIsLoading(true);
    setCategoryRecipes('');

    try {
      let recipes = '';
      switch (categoryId) {
        case 'ana-yemek':
          recipes = await recipeService.getMainDishRecipes();
          break;
        case 'corba':
          recipes = await recipeService.getSoupRecipes();
          break;
        case 'tatli':
          recipes = await recipeService.getDessertRecipes();
          break;
        case 'salata':
          recipes = await recipeService.getSaladRecipes();
          break;
        case 'hizli':
          recipes = await recipeService.getQuickRecipes();
          break;
        case 'saglikli':
          recipes = await recipeService.getHealthyRecipes();
          break;
        default:
          const category = categories.find(c => c.id === categoryId);
          recipes = await recipeService.getRecipesByCategory(category?.title || '');
      }
      setCategoryRecipes(recipes);
    } catch (error) {
      console.error('Kategori tarifleri yüklenirken hata:', error);
      Alert.alert('Hata', 'Tarifler yüklenirken bir hata oluştu.');
    } finally {
      setIsLoading(false);
    }
  };

  const onRefresh = async () => {
    if (selectedCategory) {
      setRefreshing(true);
      await handleCategoryPress(selectedCategory);
      setRefreshing(false);
    }
  };

  const renderCategory = (category: Category) => (
    <TouchableOpacity
      key={category.id}
      style={[
        styles.categoryCard,
        { backgroundColor: category.color },
        selectedCategory === category.id && styles.selectedCategory
      ]}
      onPress={() => handleCategoryPress(category.id)}
    >
      <Ionicons name={category.icon} size={40} color="white" />
      <Text style={styles.categoryTitle}>{category.title}</Text>
      <Text style={styles.categoryDescription}>{category.description}</Text>
    </TouchableOpacity>
  );

  return (
    <ScrollView 
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* Categories Grid */}
      <View style={styles.categoriesContainer}>
        <Text style={styles.sectionTitle}>Kategoriler</Text>
        <View style={styles.categoriesGrid}>
          {categories.map(renderCategory)}
        </View>
      </View>

      {/* Selected Category Recipes */}
      {selectedCategory && (
        <View style={styles.recipesContainer}>
          <View style={styles.recipesHeader}>
            <Ionicons
              name={categories.find(c => c.id === selectedCategory)?.icon || 'restaurant-outline'}
              size={24}
              color="#FF6B35"
            />
            <Text style={styles.recipesTitle}>
              {categories.find(c => c.id === selectedCategory)?.title} Tarifleri
            </Text>
          </View>
          
          <View style={styles.recipesContent}>
            {isLoading ? (
              <Text style={styles.loadingText}>Tarifler yükleniyor... ⌨️</Text>
            ) : (
              <Text style={styles.recipesText}>{categoryRecipes}</Text>
            )}
          </View>
        </View>
      )}

      {/* Help Text */}
      {!selectedCategory && (
        <View style={styles.helpContainer}>
          <Ionicons name="information-circle" size={24} color="#FF6B35" />
          <Text style={styles.helpText}>
            Yukarıdaki kategorilerden birini seçerek o kategoriye ait tarifleri görüntüleyebilirsiniz.
          </Text>
        </View>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  categoriesContainer: {
    backgroundColor: 'white',
    padding: 20,
    marginBottom: 10,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
  },
  categoriesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  categoryCard: {
    width: (width - 60) / 2,
    height: 120,
    borderRadius: 15,
    padding: 15,
    marginBottom: 15,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedCategory: {
    borderWidth: 3,
    borderColor: '#333',
  },
  categoryTitle: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
    marginTop: 8,
    textAlign: 'center',
  },
  categoryDescription: {
    color: 'white',
    fontSize: 12,
    marginTop: 4,
    textAlign: 'center',
    opacity: 0.9,
  },
  recipesContainer: {
    backgroundColor: 'white',
    padding: 20,
    marginBottom: 10,
  },
  recipesHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  recipesTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 10,
  },
  recipesContent: {
    backgroundColor: '#f9f9f9',
    padding: 15,
    borderRadius: 10,
    borderLeftWidth: 4,
    borderLeftColor: '#FF6B35',
  },
  recipesText: {
    fontSize: 14,
    lineHeight: 20,
    color: '#333',
  },
  loadingText: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
    textAlign: 'center',
  },
  helpContainer: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  helpText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 10,
    flex: 1,
    lineHeight: 20,
  },
});
