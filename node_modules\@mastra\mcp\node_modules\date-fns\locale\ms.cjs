"use strict";
exports.ms = void 0;
var _index = require("./ms/_lib/formatDistance.cjs");
var _index2 = require("./ms/_lib/formatLong.cjs");
var _index3 = require("./ms/_lib/formatRelative.cjs");
var _index4 = require("./ms/_lib/localize.cjs");
var _index5 = require("./ms/_lib/match.cjs");

/**
 * @category Locales
 * @summary Malay locale.
 * @language Malay
 * @iso-639-2 msa
 * <AUTHOR> [@Zyten](https://github.com/Zyten)
 */
const ms = (exports.ms = {
  code: "ms",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
});
