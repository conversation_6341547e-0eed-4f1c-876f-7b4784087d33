import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  UpdateClusterSchedulerConfigRequest,
  UpdateClusterSchedulerConfigResponse,
} from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateClusterSchedulerConfigCommandInput
  extends UpdateClusterSchedulerConfigRequest {}
export interface UpdateClusterSchedulerConfigCommandOutput
  extends UpdateClusterSchedulerConfigResponse,
    __MetadataBearer {}
declare const UpdateClusterSchedulerConfigCommand_base: {
  new (
    input: UpdateClusterSchedulerConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateClusterSchedulerConfigCommandInput,
    UpdateClusterSchedulerConfigCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateClusterSchedulerConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateClusterSchedulerConfigCommandInput,
    UpdateClusterSchedulerConfigCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateClusterSchedulerConfigCommand extends UpdateClusterSchedulerConfigCommand_base {
  protected static __types: {
    api: {
      input: UpdateClusterSchedulerConfigRequest;
      output: UpdateClusterSchedulerConfigResponse;
    };
    sdk: {
      input: UpdateClusterSchedulerConfigCommandInput;
      output: UpdateClusterSchedulerConfigCommandOutput;
    };
  };
}
