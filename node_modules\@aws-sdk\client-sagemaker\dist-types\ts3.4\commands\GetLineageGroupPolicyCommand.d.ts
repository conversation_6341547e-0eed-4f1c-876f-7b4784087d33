import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  GetLineageGroupPolicyRequest,
  GetLineageGroupPolicyResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface GetLineageGroupPolicyCommandInput
  extends GetLineageGroupPolicyRequest {}
export interface GetLineageGroupPolicyCommandOutput
  extends GetLineageGroupPolicyResponse,
    __MetadataBearer {}
declare const GetLineageGroupPolicyCommand_base: {
  new (
    input: GetLineageGroupPolicyCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetLineageGroupPolicyCommandInput,
    GetLineageGroupPolicyCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetLineageGroupPolicyCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetLineageGroupPolicyCommandInput,
    GetLineageGroupPolicyCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetLineageGroupPolicyCommand extends GetLineageGroupPolicyCommand_base {
  protected static __types: {
    api: {
      input: GetLineageGroupPolicyRequest;
      output: GetLineageGroupPolicyResponse;
    };
    sdk: {
      input: GetLineageGroupPolicyCommandInput;
      output: GetLineageGroupPolicyCommandOutput;
    };
  };
}
