import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DeleteWorkteamRequest,
  DeleteWorkteamResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteWorkteamCommandInput extends DeleteWorkteamRequest {}
export interface DeleteWorkteamCommandOutput
  extends DeleteWorkteamResponse,
    __MetadataBearer {}
declare const DeleteWorkteamCommand_base: {
  new (
    input: DeleteWorkteamCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteWorkteamCommandInput,
    DeleteWorkteamCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteWorkteamCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteWorkteamCommandInput,
    DeleteWorkteamCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteWorkteamCommand extends DeleteWorkteamCommand_base {
  protected static __types: {
    api: {
      input: DeleteWorkteamRequest;
      output: DeleteWorkteamResponse;
    };
    sdk: {
      input: DeleteWorkteamCommandInput;
      output: DeleteWorkteamCommandOutput;
    };
  };
}
