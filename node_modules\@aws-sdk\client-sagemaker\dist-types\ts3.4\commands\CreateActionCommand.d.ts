import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { CreateActionRequest, CreateActionResponse } from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateActionCommandInput extends CreateActionRequest {}
export interface CreateActionCommandOutput
  extends CreateActionResponse,
    __MetadataBearer {}
declare const CreateActionCommand_base: {
  new (
    input: CreateActionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateActionCommandInput,
    CreateActionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateActionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateActionCommandInput,
    CreateActionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateActionCommand extends CreateActionCommand_base {
  protected static __types: {
    api: {
      input: CreateActionRequest;
      output: CreateActionResponse;
    };
    sdk: {
      input: CreateActionCommandInput;
      output: CreateActionCommandOutput;
    };
  };
}
