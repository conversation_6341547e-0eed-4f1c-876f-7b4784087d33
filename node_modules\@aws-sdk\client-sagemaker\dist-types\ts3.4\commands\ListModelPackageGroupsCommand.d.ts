import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ListModelPackageGroupsInput,
  ListModelPackageGroupsOutput,
} from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface ListModelPackageGroupsCommandInput
  extends ListModelPackageGroupsInput {}
export interface ListModelPackageGroupsCommandOutput
  extends ListModelPackageGroupsOutput,
    __MetadataBearer {}
declare const ListModelPackageGroupsCommand_base: {
  new (
    input: ListModelPackageGroupsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListModelPackageGroupsCommandInput,
    ListModelPackageGroupsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    ...[input]: [] | [ListModelPackageGroupsCommandInput]
  ): import("@smithy/smithy-client").CommandImpl<
    ListModelPackageGroupsCommandInput,
    ListModelPackageGroupsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListModelPackageGroupsCommand extends ListModelPackageGroupsCommand_base {
  protected static __types: {
    api: {
      input: ListModelPackageGroupsInput;
      output: ListModelPackageGroupsOutput;
    };
    sdk: {
      input: ListModelPackageGroupsCommandInput;
      output: ListModelPackageGroupsCommandOutput;
    };
  };
}
