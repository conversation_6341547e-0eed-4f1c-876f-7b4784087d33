import { formatDistance } from "./hr/_lib/formatDistance.js";
import { formatLong } from "./hr/_lib/formatLong.js";
import { formatRelative } from "./hr/_lib/formatRelative.js";
import { localize } from "./hr/_lib/localize.js";
import { match } from "./hr/_lib/match.js";

/**
 * @category Locales
 * @summary Croatian locale.
 * @language Croatian
 * @iso-639-2 hrv
 * <AUTHOR> [@silvenon](https://github.com/silvenon)
 * <AUTHOR> [@manico](https://github.com/manico)
 * <AUTHOR> [@jerzabek](https://github.com/jerzabek)
 */
export const hr = {
  code: "hr",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default hr;
