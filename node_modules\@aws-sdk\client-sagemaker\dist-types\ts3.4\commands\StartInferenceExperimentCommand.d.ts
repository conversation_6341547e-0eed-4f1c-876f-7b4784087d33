import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  StartInferenceExperimentRequest,
  StartInferenceExperimentResponse,
} from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface StartInferenceExperimentCommandInput
  extends StartInferenceExperimentRequest {}
export interface StartInferenceExperimentCommandOutput
  extends StartInferenceExperimentResponse,
    __MetadataBearer {}
declare const StartInferenceExperimentCommand_base: {
  new (
    input: StartInferenceExperimentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StartInferenceExperimentCommandInput,
    StartInferenceExperimentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: StartInferenceExperimentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StartInferenceExperimentCommandInput,
    StartInferenceExperimentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class StartInferenceExperimentCommand extends StartInferenceExperimentCommand_base {
  protected static __types: {
    api: {
      input: StartInferenceExperimentRequest;
      output: StartInferenceExperimentResponse;
    };
    sdk: {
      input: StartInferenceExperimentCommandInput;
      output: StartInferenceExperimentCommandOutput;
    };
  };
}
