import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  CreateMlflowTrackingServerRequest,
  CreateMlflowTrackingServerResponse,
} from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateMlflowTrackingServerCommandInput
  extends CreateMlflowTrackingServerRequest {}
export interface CreateMlflowTrackingServerCommandOutput
  extends CreateMlflowTrackingServerResponse,
    __MetadataBearer {}
declare const CreateMlflowTrackingServerCommand_base: {
  new (
    input: CreateMlflowTrackingServerCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateMlflowTrackingServerCommandInput,
    Create<PERSON>lflowTrackingServerCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateMlflowTrackingServerCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateMlflowTrackingServerCommandInput,
    CreateMlflowTrackingServerCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateMlflowTrackingServerCommand extends CreateMlflowTrackingServerCommand_base {
  protected static __types: {
    api: {
      input: CreateMlflowTrackingServerRequest;
      output: CreateMlflowTrackingServerResponse;
    };
    sdk: {
      input: CreateMlflowTrackingServerCommandInput;
      output: CreateMlflowTrackingServerCommandOutput;
    };
  };
}
