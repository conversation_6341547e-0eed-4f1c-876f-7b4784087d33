import { Paginator } from "@smithy/types";
import {
  ListTrainingJobsCommandInput,
  ListTrainingJobsCommandOutput,
} from "../commands/ListTrainingJobsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
export declare const paginateListTrainingJobs: (
  config: SageMakerPaginationConfiguration,
  input: ListTrainingJobsCommandInput,
  ...rest: any[]
) => Paginator<ListTrainingJobsCommandOutput>;
