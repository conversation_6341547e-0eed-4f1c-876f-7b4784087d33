import { Paginator } from "@smithy/types";
import { ListMlflowTrackingServersCommandInput, ListMlflowTrackingServersCommandOutput } from "../commands/ListMlflowTrackingServersCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListMlflowTrackingServers: (config: SageMakerPaginationConfiguration, input: ListMlflowTrackingServersCommandInput, ...rest: any[]) => Paginator<ListMlflowTrackingServersCommandOutput>;
