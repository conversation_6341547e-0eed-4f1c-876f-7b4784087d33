import { WaiterConfiguration, WaiterResult } from "@smithy/util-waiter";
import { DescribeImageVersionCommandInput } from "../commands/DescribeImageVersionCommand";
import { SageMakerClient } from "../SageMakerClient";
export declare const waitForImageVersionDeleted: (
  params: WaiterConfiguration<SageMakerClient>,
  input: DescribeImageVersionCommandInput
) => Promise<WaiterResult>;
export declare const waitUntilImageVersionDeleted: (
  params: WaiterConfiguration<SageMakerClient>,
  input: DescribeImageVersionCommandInput
) => Promise<WaiterResult>;
