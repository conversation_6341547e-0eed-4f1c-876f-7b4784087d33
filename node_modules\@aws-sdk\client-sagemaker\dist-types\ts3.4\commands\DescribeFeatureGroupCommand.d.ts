import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeFeatureGroupRequest,
  DescribeFeatureGroupResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeFeatureGroupCommandInput
  extends DescribeFeatureGroupRequest {}
export interface DescribeFeatureGroupCommandOutput
  extends DescribeFeatureGroupResponse,
    __MetadataBearer {}
declare const DescribeFeatureGroupCommand_base: {
  new (
    input: DescribeFeatureGroupCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeFeatureGroupCommandInput,
    DescribeFeatureGroupCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeFeatureGroupCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeFeatureGroupCommandInput,
    DescribeFeatureGroupCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeFeatureGroupCommand extends DescribeFeatureGroupCommand_base {
  protected static __types: {
    api: {
      input: DescribeFeatureGroupRequest;
      output: DescribeFeatureGroupResponse;
    };
    sdk: {
      input: DescribeFeatureGroupCommandInput;
      output: DescribeFeatureGroupCommandOutput;
    };
  };
}
