import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { CreateTrialRequest, CreateTrialResponse } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateTrialCommandInput extends CreateTrialRequest {}
export interface CreateTrialCommandOutput
  extends CreateTrialResponse,
    __MetadataBearer {}
declare const CreateTrialCommand_base: {
  new (
    input: CreateTrialCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateTrialCommandInput,
    CreateTrialCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateTrialCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateTrialCommandInput,
    CreateTrialCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateTrialCommand extends CreateTrialCommand_base {
  protected static __types: {
    api: {
      input: CreateTrialRequest;
      output: CreateTrialResponse;
    };
    sdk: {
      input: CreateTrialCommandInput;
      output: CreateTrialCommandOutput;
    };
  };
}
