import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { StopOptimizationJobRequest } from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface StopOptimizationJobCommandInput
  extends StopOptimizationJobRequest {}
export interface StopOptimizationJobCommandOutput extends __MetadataBearer {}
declare const StopOptimizationJobCommand_base: {
  new (
    input: StopOptimizationJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StopOptimizationJobCommandInput,
    StopOptimizationJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: StopOptimizationJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StopOptimizationJobCommandInput,
    StopOptimizationJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class StopOptimizationJobCommand extends StopOptimizationJobCommand_base {
  protected static __types: {
    api: {
      input: StopOptimizationJobRequest;
      output: {};
    };
    sdk: {
      input: StopOptimizationJobCommandInput;
      output: StopOptimizationJobCommandOutput;
    };
  };
}
