import { WaiterConfiguration, WaiterR<PERSON>ult } from "@smithy/util-waiter";
import { DescribeTransformJobCommandInput } from "../commands/DescribeTransformJobCommand";
import { SageMakerClient } from "../SageMakerClient";
/**
 *
 *  @deprecated Use waitUntilTransformJobCompletedOrStopped instead. waitForTransformJobCompletedOrStopped does not throw error in non-success cases.
 */
export declare const waitForTransformJobCompletedOrStopped: (params: WaiterConfiguration<SageMakerClient>, input: DescribeTransformJobCommandInput) => Promise<WaiterResult>;
/**
 *
 *  @param params - Waiter configuration options.
 *  @param input - The input to DescribeTransformJobCommand for polling.
 */
export declare const waitUntilTransformJobCompletedOrStopped: (params: WaiterConfiguration<SageMakerClient>, input: DescribeTransformJobCommandInput) => Promise<WaiterResult>;
