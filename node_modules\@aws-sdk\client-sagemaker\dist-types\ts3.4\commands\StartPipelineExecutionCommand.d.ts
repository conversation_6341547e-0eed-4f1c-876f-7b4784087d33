import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  StartPipelineExecutionRequest,
  StartPipelineExecutionResponse,
} from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface StartPipelineExecutionCommandInput
  extends StartPipelineExecutionRequest {}
export interface StartPipelineExecutionCommandOutput
  extends StartPipelineExecutionResponse,
    __MetadataBearer {}
declare const StartPipelineExecutionCommand_base: {
  new (
    input: StartPipelineExecutionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StartPipelineExecutionCommandInput,
    StartPipelineExecutionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: StartPipelineExecutionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StartPipelineExecutionCommandInput,
    StartPipelineExecutionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class StartPipelineExecutionCommand extends StartPipelineExecutionCommand_base {
  protected static __types: {
    api: {
      input: StartPipelineExecutionRequest;
      output: StartPipelineExecutionResponse;
    };
    sdk: {
      input: StartPipelineExecutionCommandInput;
      output: StartPipelineExecutionCommandOutput;
    };
  };
}
