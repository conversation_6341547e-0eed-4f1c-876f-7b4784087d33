import { Paginator } from "@smithy/types";
import {
  ListHumanTaskUisCommandInput,
  ListHumanTaskUisCommandOutput,
} from "../commands/ListHumanTaskUisCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
export declare const paginateListHumanTaskUis: (
  config: SageMakerPaginationConfiguration,
  input: ListHumanTaskUisCommandInput,
  ...rest: any[]
) => Paginator<ListHumanTaskUisCommandOutput>;
