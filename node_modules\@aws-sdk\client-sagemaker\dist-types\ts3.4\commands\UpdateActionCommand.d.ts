import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { UpdateActionRequest, UpdateActionResponse } from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateActionCommandInput extends UpdateActionRequest {}
export interface UpdateActionCommandOutput
  extends UpdateActionResponse,
    __MetadataBearer {}
declare const UpdateActionCommand_base: {
  new (
    input: UpdateActionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateActionCommandInput,
    UpdateActionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateActionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateActionCommandInput,
    UpdateActionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateActionCommand extends UpdateActionCommand_base {
  protected static __types: {
    api: {
      input: UpdateActionRequest;
      output: UpdateActionResponse;
    };
    sdk: {
      input: UpdateActionCommandInput;
      output: UpdateActionCommandOutput;
    };
  };
}
