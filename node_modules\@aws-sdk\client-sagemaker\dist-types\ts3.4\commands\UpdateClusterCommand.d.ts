import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  UpdateClusterRequest,
  UpdateClusterResponse,
} from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateClusterCommandInput extends UpdateClusterRequest {}
export interface UpdateClusterCommandOutput
  extends UpdateClusterResponse,
    __MetadataBearer {}
declare const UpdateClusterCommand_base: {
  new (
    input: UpdateClusterCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateClusterCommandInput,
    UpdateClusterCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateClusterCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateClusterCommandInput,
    UpdateClusterCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateClusterCommand extends UpdateClusterCommand_base {
  protected static __types: {
    api: {
      input: UpdateClusterRequest;
      output: UpdateClusterResponse;
    };
    sdk: {
      input: UpdateClusterCommandInput;
      output: UpdateClusterCommandOutput;
    };
  };
}
