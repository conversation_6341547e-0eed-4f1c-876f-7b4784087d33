import { Paginator } from "@smithy/types";
import {
  ListSubscribedWorkteamsCommandInput,
  ListSubscribedWorkteamsCommandOutput,
} from "../commands/ListSubscribedWorkteamsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
export declare const paginateListSubscribedWorkteams: (
  config: SageMakerPaginationConfiguration,
  input: ListSubscribedWorkteamsCommandInput,
  ...rest: any[]
) => Paginator<ListSubscribedWorkteamsCommandOutput>;
