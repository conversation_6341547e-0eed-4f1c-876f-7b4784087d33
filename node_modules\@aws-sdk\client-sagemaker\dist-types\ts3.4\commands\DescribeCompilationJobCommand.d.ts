import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeCompilationJobRequest,
  DescribeCompilationJobResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeCompilationJobCommandInput
  extends DescribeCompilationJobRequest {}
export interface DescribeCompilationJobCommandOutput
  extends DescribeCompilationJobResponse,
    __MetadataBearer {}
declare const DescribeCompilationJobCommand_base: {
  new (
    input: DescribeCompilationJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeCompilationJobCommandInput,
    DescribeCompilationJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeCompilationJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeCompilationJobCommandInput,
    DescribeCompilationJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeCompilationJobCommand extends DescribeCompilationJobCommand_base {
  protected static __types: {
    api: {
      input: DescribeCompilationJobRequest;
      output: DescribeCompilationJobResponse;
    };
    sdk: {
      input: DescribeCompilationJobCommandInput;
      output: DescribeCompilationJobCommandOutput;
    };
  };
}
