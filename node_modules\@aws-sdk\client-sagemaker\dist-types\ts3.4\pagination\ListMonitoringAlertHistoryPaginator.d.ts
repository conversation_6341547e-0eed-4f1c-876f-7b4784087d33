import { Paginator } from "@smithy/types";
import {
  ListMonitoringAlertHistoryCommandInput,
  ListMonitoringAlertHistoryCommandOutput,
} from "../commands/ListMonitoringAlertHistoryCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
export declare const paginateListMonitoringAlertHistory: (
  config: SageMakerPaginationConfiguration,
  input: ListMonitoringAlertHistoryCommandInput,
  ...rest: any[]
) => Paginator<ListMonitoringAlertHistoryCommandOutput>;
