import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeClusterRequest,
  DescribeClusterResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeClusterCommandInput extends DescribeClusterRequest {}
export interface DescribeClusterCommandOutput
  extends DescribeClusterResponse,
    __MetadataBearer {}
declare const DescribeClusterCommand_base: {
  new (
    input: DescribeClusterCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeClusterCommandInput,
    DescribeClusterCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeClusterCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeClusterCommandInput,
    DescribeClusterCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeClusterCommand extends DescribeClusterCommand_base {
  protected static __types: {
    api: {
      input: DescribeClusterRequest;
      output: DescribeClusterResponse;
    };
    sdk: {
      input: DescribeClusterCommandInput;
      output: DescribeClusterCommandOutput;
    };
  };
}
