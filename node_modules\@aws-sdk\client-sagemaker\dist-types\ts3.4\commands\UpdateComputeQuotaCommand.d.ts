import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  UpdateComputeQuotaRequest,
  UpdateComputeQuotaResponse,
} from "../models/models_5";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateComputeQuotaCommandInput
  extends UpdateComputeQuotaRequest {}
export interface UpdateComputeQuotaCommandOutput
  extends UpdateComputeQuotaResponse,
    __MetadataBearer {}
declare const UpdateComputeQuotaCommand_base: {
  new (
    input: UpdateComputeQuotaCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateComputeQuotaCommandInput,
    UpdateComputeQuotaCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateComputeQuotaCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateComputeQuotaCommandInput,
    UpdateComputeQuotaCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateComputeQuotaCommand extends UpdateComputeQuotaCommand_base {
  protected static __types: {
    api: {
      input: UpdateComputeQuotaRequest;
      output: UpdateComputeQuotaResponse;
    };
    sdk: {
      input: UpdateComputeQuotaCommandInput;
      output: UpdateComputeQuotaCommandOutput;
    };
  };
}
