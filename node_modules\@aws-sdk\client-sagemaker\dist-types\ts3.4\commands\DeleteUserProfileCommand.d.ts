import { Command as $Command } from "@smithy/smithy-client";
import { <PERSON>ada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DeleteUserProfileRequest } from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteUserProfileCommandInput
  extends DeleteUserProfileRequest {}
export interface DeleteUserProfileCommandOutput extends __MetadataBearer {}
declare const DeleteUserProfileCommand_base: {
  new (
    input: DeleteUserProfileCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteUserProfileCommandInput,
    DeleteUserProfileCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteUserProfileCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteUserProfileCommandInput,
    DeleteUserProfileCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteUserProfileCommand extends DeleteUserProfileCommand_base {
  protected static __types: {
    api: {
      input: DeleteUserProfileRequest;
      output: {};
    };
    sdk: {
      input: DeleteUserProfileCommandInput;
      output: DeleteUserProfileCommandOutput;
    };
  };
}
