import { bf as MastraLanguageModel } from '../base-QP4OC4dB.js';
import '../base-tc5kgDTD.js';
import 'ai';
import '../types-Bo1uigWx.js';
import 'sift';
import 'zod';
import 'json-schema';
import '../deployer/index.js';
import '../bundler/index.js';
import '@opentelemetry/api';
import '../logger-EhZkzZOr.js';
import 'stream';
import '@opentelemetry/sdk-trace-base';
import 'node:http';
import 'hono';
import '../runtime-context/index.js';
import '../tts/index.js';
import '../vector/index.js';
import '../vector/filter/index.js';
import 'xstate';
import 'node:events';
import 'events';
import '../workflows/constants.js';
import 'hono/cors';
import 'hono-openapi';
import 'ai/test';

interface RelevanceScoreProvider {
    getRelevanceScore(text1: string, text2: string): Promise<number>;
}
declare function createSimilarityPrompt(query: string, text: string): string;

declare class CohereRelevanceScorer implements RelevanceScoreProvider {
    private client;
    private model;
    constructor(model: string, apiKey?: string);
    getRelevanceScore(query: string, text: string): Promise<number>;
}

declare class MastraAgentRelevanceScorer implements RelevanceScoreProvider {
    private agent;
    constructor(name: string, model: MastraLanguageModel);
    getRelevanceScore(query: string, text: string): Promise<number>;
}

export { CohereRelevanceScorer, MastraAgentRelevanceScorer, type RelevanceScoreProvider, createSimilarityPrompt };
