import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribePipelineExecutionRequest,
  DescribePipelineExecutionResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribePipelineExecutionCommandInput
  extends DescribePipelineExecutionRequest {}
export interface DescribePipelineExecutionCommandOutput
  extends DescribePipelineExecutionResponse,
    __MetadataBearer {}
declare const DescribePipelineExecutionCommand_base: {
  new (
    input: DescribePipelineExecutionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribePipelineExecutionCommandInput,
    DescribePipelineExecutionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribePipelineExecutionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribePipelineExecutionCommandInput,
    DescribePipelineExecutionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribePipelineExecutionCommand extends DescribePipelineExecutionCommand_base {
  protected static __types: {
    api: {
      input: DescribePipelineExecutionRequest;
      output: DescribePipelineExecutionResponse;
    };
    sdk: {
      input: DescribePipelineExecutionCommandInput;
      output: DescribePipelineExecutionCommandOutput;
    };
  };
}
