const accusativeWeekdays = [
  "ned<PERSON><PERSON>",
  "ponděl<PERSON>",
  "úter<PERSON>",
  "středu",
  "čtvrtek",
  "pátek",
  "sobotu",
];

const formatRelativeLocale = {
  lastWeek: "'posledn<PERSON>' eeee 've' p",
  yesterday: "'včera v' p",
  today: "'dnes v' p",
  tomorrow: "'zítra v' p",
  nextWeek: (date) => {
    const day = date.getDay();
    return "'v " + accusativeWeekdays[day] + " o' p";
  },
  other: "P",
};

export const formatRelative = (token, date) => {
  const format = formatRelativeLocale[token];

  if (typeof format === "function") {
    return format(date);
  }

  return format;
};
