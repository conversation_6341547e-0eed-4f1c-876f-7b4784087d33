import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  DeleteContextRequest,
  DeleteContextResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteContextCommandInput extends DeleteContextRequest {}
export interface DeleteContextCommandOutput
  extends DeleteContextResponse,
    __MetadataBearer {}
declare const DeleteContextCommand_base: {
  new (
    input: DeleteContextCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteContextCommandInput,
    DeleteContextCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteContextCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteContextCommandInput,
    DeleteContextCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteContextCommand extends DeleteContextCommand_base {
  protected static __types: {
    api: {
      input: DeleteContextRequest;
      output: DeleteContextResponse;
    };
    sdk: {
      input: DeleteContextCommandInput;
      output: DeleteContextCommandOutput;
    };
  };
}
