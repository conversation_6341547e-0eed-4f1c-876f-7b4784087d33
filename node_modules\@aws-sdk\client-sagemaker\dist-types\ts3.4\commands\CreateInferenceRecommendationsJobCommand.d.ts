import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateInferenceRecommendationsJobRequest,
  CreateInferenceRecommendationsJobResponse,
} from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateInferenceRecommendationsJobCommandInput
  extends CreateInferenceRecommendationsJobRequest {}
export interface CreateInferenceRecommendationsJobCommandOutput
  extends CreateInferenceRecommendationsJobResponse,
    __MetadataBearer {}
declare const CreateInferenceRecommendationsJobCommand_base: {
  new (
    input: CreateInferenceRecommendationsJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateInferenceRecommendationsJobCommandInput,
    CreateInferenceRecommendationsJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateInferenceRecommendationsJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateInferenceRecommendationsJobCommandInput,
    CreateInferenceRecommendationsJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateInferenceRecommendationsJobCommand extends CreateInferenceRecommendationsJobCommand_base {
  protected static __types: {
    api: {
      input: CreateInferenceRecommendationsJobRequest;
      output: CreateInferenceRecommendationsJobResponse;
    };
    sdk: {
      input: CreateInferenceRecommendationsJobCommandInput;
      output: CreateInferenceRecommendationsJobCommandOutput;
    };
  };
}
