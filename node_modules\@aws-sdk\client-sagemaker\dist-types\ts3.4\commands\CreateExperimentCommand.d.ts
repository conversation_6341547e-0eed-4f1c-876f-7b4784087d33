import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateExperimentRequest,
  CreateExperimentResponse,
} from "../models/models_1";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreateExperimentCommandInput extends CreateExperimentRequest {}
export interface CreateExperimentCommandOutput
  extends CreateExperimentResponse,
    __MetadataBearer {}
declare const CreateExperimentCommand_base: {
  new (
    input: CreateExperimentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateExperimentCommandInput,
    CreateExperimentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateExperimentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateExperimentCommandInput,
    CreateExperimentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateExperimentCommand extends CreateExperimentCommand_base {
  protected static __types: {
    api: {
      input: CreateExperimentRequest;
      output: CreateExperimentResponse;
    };
    sdk: {
      input: CreateExperimentCommandInput;
      output: CreateExperimentCommandOutput;
    };
  };
}
