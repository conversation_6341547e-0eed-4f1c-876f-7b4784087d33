import { Paginator } from "@smithy/types";
import { ListLabelingJobsCommandInput, ListLabelingJobsCommandOutput } from "../commands/ListLabelingJobsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListLabelingJobs: (config: SageMakerPaginationConfiguration, input: ListLabelingJobsCommandInput, ...rest: any[]) => Paginator<ListLabelingJobsCommandOutput>;
