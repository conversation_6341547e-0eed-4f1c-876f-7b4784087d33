import { config } from 'dotenv';
import express from 'express';
import { recipeAgent } from './src/mastra/agents/recipeAgent';
import { initializeMCP } from './src/mastra/init';

// Load environment variables
config();

const app = express();
const PORT = 3000;

app.use(express.json());
app.use(express.static('public'));

// Initialize MCP
initializeMCP();

// Serve the main page
app.get('/', (req, res) => {
  res.send(`
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recipe Agent</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f0f2f5;
            height: 100vh;
            overflow: hidden;
        }
        
        .chat-app {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            flex-direction: column;
            max-width: 800px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .msg {
            margin: 10px 0;
            padding: 12px 18px;
            border-radius: 20px;
            max-width: 80%;
            word-wrap: break-word;
            animation: fadeIn 0.3s ease;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .user {
            background: #007bff;
            color: white;
            margin-left: auto;
            border-bottom-right-radius: 5px;
        }
        
        .bot {
            background: white;
            color: #333;
            border: 1px solid #e9ecef;
            border-bottom-left-radius: 5px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .loading {
            color: #666;
            font-style: italic;
            opacity: 0.7;
        }
        
        .input-area {
            background: white;
            border-top: 1px solid #e9ecef;
            padding: 15px;
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }
        
        .input-box {
            flex: 1;
            border: 1px solid #ddd;
            border-radius: 25px;
            padding: 12px 18px;
            font-size: 16px;
            font-family: inherit;
            resize: none;
            outline: none;
            max-height: 100px;
            min-height: 44px;
        }
        
        .input-box:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }
        
        .send-button {
            background: #007bff;
            color: white;
            border: none;
            border-radius: 50%;
            width: 44px;
            height: 44px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            transition: all 0.2s;
        }
        
        .send-button:hover:not(:disabled) {
            background: #0056b3;
            transform: scale(1.05);
        }
        
        .send-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
    </style>
</head>
<body>
    <div class="chat-app">
        <div class="header">
            <h1>🍳 Recipe Agent</h1>
            <p>Malzemelerinizi söyleyin, size uygun tarifler önereyim!</p>
        </div>
        
        <div class="messages" id="messages">
            <div class="msg bot">
                Merhaba! Ben Recipe Agent'ım. Elinizde bulunan malzemeleri söyleyin, size uygun tarifler önereyim!
                <br><br>
                Örnek: "Yumurta, domates ve peynir var. Ne yapabilirim?"
            </div>
        </div>
        
        <div class="input-area">
            <textarea 
                id="messageInput" 
                class="input-box" 
                placeholder="Malzemelerinizi yazın..."
                rows="1"
            ></textarea>
            <button id="sendButton" class="send-button">
                ➤
            </button>
        </div>
    </div>

    <script>
        let processing = false;
        
        const input = document.getElementById('messageInput');
        const button = document.getElementById('sendButton');
        const messages = document.getElementById('messages');
        
        // Auto-resize textarea
        input.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 100) + 'px';
        });
        
        // Send on Enter
        input.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
        
        button.addEventListener('click', sendMessage);
        
        async function sendMessage() {
            if (processing) return;
            
            const text = input.value.trim();
            if (!text) return;
            
            processing = true;
            input.disabled = true;
            button.disabled = true;
            button.textContent = '⏳';
            
            // Add user message
            addMessage(text, 'user');
            input.value = '';
            input.style.height = 'auto';
            
            // Add loading message
            const loadingMsg = addMessage('Tarif arıyorum...', 'bot loading');
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message: text })
                });
                
                const data = await response.json();
                loadingMsg.remove();
                addMessage(data.response, 'bot');
                
            } catch (error) {
                loadingMsg.remove();
                addMessage('Üzgünüm, bir hata oluştu. Lütfen tekrar deneyin.', 'bot');
            } finally {
                processing = false;
                input.disabled = false;
                button.disabled = false;
                button.textContent = '➤';
                input.focus();
            }
        }
        
        function addMessage(text, className) {
            const div = document.createElement('div');
            div.className = 'msg ' + className;
            div.innerHTML = text.replace(/\\n/g, '<br>');
            messages.appendChild(div);
            messages.scrollTop = messages.scrollHeight;
            return div;
        }
    </script>
</body>
</html>
  `);
});

// Chat API endpoint
app.post('/api/chat', async (req, res) => {
  try {
    const { message } = req.body;
    console.log('📝 Kullanıcı mesajı:', message);

    const response = await recipeAgent.generate([
      { role: 'user', content: message }
    ]);

    console.log('🤖 Agent yanıtı:', response.text);
    res.json({ response: response.text });

  } catch (error) {
    console.error('❌ Chat hatası:', error);
    res.status(500).json({
      error: 'Bir hata oluştu',
      response: 'Üzgünüm, şu anda tarif servisimizde teknik bir sorun var. Lütfen daha sonra tekrar deneyin.'
    });
  }
});

app.listen(PORT, () => {
  console.log(`🚀 Recipe Agent sunucusu http://localhost:${PORT} adresinde çalışıyor`);
});
