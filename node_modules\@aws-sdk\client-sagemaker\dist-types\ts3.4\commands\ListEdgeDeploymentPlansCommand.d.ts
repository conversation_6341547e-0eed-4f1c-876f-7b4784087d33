import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ListEdgeDeploymentPlansRequest,
  ListEdgeDeploymentPlansResponse,
} from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface ListEdgeDeploymentPlansCommandInput
  extends ListEdgeDeploymentPlansRequest {}
export interface ListEdgeDeploymentPlansCommandOutput
  extends ListEdgeDeploymentPlansResponse,
    __MetadataBearer {}
declare const ListEdgeDeploymentPlansCommand_base: {
  new (
    input: ListEdgeDeploymentPlansCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListEdgeDeploymentPlansCommandInput,
    ListEdgeDeploymentPlansCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    ...[input]: [] | [ListEdgeDeploymentPlansCommandInput]
  ): import("@smithy/smithy-client").CommandImpl<
    ListEdgeDeploymentPlansCommandInput,
    ListEdgeDeploymentPlansCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListEdgeDeploymentPlansCommand extends ListEdgeDeploymentPlansCommand_base {
  protected static __types: {
    api: {
      input: ListEdgeDeploymentPlansRequest;
      output: ListEdgeDeploymentPlansResponse;
    };
    sdk: {
      input: ListEdgeDeploymentPlansCommandInput;
      output: ListEdgeDeploymentPlansCommandOutput;
    };
  };
}
