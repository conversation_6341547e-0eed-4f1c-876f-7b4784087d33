import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreatePresignedMlflowTrackingServerUrlRequest,
  CreatePresignedMlflowTrackingServerUrlResponse,
} from "../models/models_2";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface CreatePresignedMlflowTrackingServerUrlCommandInput
  extends CreatePresignedMlflowTrackingServerUrlRequest {}
export interface CreatePresignedMlflowTrackingServerUrlCommandOutput
  extends CreatePresignedMlflowTrackingServerUrlResponse,
    __MetadataBearer {}
declare const CreatePresignedMlflowTrackingServerUrlCommand_base: {
  new (
    input: CreatePresignedMlflowTrackingServerUrlCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreatePresignedMlflowTrackingServerUrlCommandInput,
    CreatePresignedMlflowTrackingServerUrlCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreatePresignedMlflowTrackingServerUrlCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreatePresignedMlflowTrackingServerUrlCommandInput,
    CreatePresignedMlflowTrackingServerUrlCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreatePresignedMlflowTrackingServerUrlCommand extends CreatePresignedMlflowTrackingServerUrlCommand_base {
  protected static __types: {
    api: {
      input: CreatePresignedMlflowTrackingServerUrlRequest;
      output: CreatePresignedMlflowTrackingServerUrlResponse;
    };
    sdk: {
      input: CreatePresignedMlflowTrackingServerUrlCommandInput;
      output: CreatePresignedMlflowTrackingServerUrlCommandOutput;
    };
  };
}
