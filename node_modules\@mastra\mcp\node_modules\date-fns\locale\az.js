import { formatDistance } from "./az/_lib/formatDistance.js";
import { formatLong } from "./az/_lib/formatLong.js";
import { formatRelative } from "./az/_lib/formatRelative.js";
import { localize } from "./az/_lib/localize.js";
import { match } from "./az/_lib/match.js";

/**
 * @category Locales
 * @summary Azerbaijani locale.
 * @language Azerbaijani
 * @iso-639-2 aze
 */

export const az = {
  code: "az",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default az;
